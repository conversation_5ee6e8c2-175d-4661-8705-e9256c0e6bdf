package com.semptian.constant;

/**
 *
 * <AUTHOR>
 */
public class CommonConstant {

    /**
     * 数据状态:有效
     */
    public static final int VALID = 1;
    /**
     * 数据状态: 无效
     */
    public static final int INVALID = 0;
    /**
     * 前缀匹配
     */
    public static final int PREFIX_MATCH = 1;
    /**
     * 后缀匹配
     */
    public static final int SUFFIX_MATCH = 2;
    /**
     * 模糊查询
     */
    public static final int FUZZY_MATCH = 3;
    /**
     * 精确匹配
     */
    public static final int PRECISE_MATCH = 4;

    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;
    public static final int NINE = 8;
    public static final int TEN = 8;
    public static final int NINETY_EIGHT = 98;
    public static final int NINETY_NINE = 99;

    public static final String SUFFIX_TXT = ".txt";
    public static final String SUFFIX_XLS = ".xls";
    public static final String SUFFIX_XLSX = ".xlsx";
    public static final String SUFFIX_CSV = ".csv";

    public static final String TXT = "txt";
    public static final String XLS = "xls";
    public static final String XLSX = "xlsx";
    public static final String CSV = "csv";

    public static final String LANG_CN = "zh_CN";
    public static final String LANG_FR = "fr_DZ";
    public static final String LANG_EN = "en_US";


    /**
     * excel文件上传大小限制(byte) 100M
     */
    public static final long XLS_FILE_SIZE = 104857600;

    /**
     * csv/txt文件上传大小限制(byte) 2G
     */
    public static final long CSV_FILE_SIZE = 2147483648L;

    /**
     * 域名知识库规则
     */
    public static final int DOMAIN_RULE_TYPE = 1;

    /**
     * 应用知识库规则
     */
    public static final int APP_RULE_TYPE = 2;

    /**
     * 预置规则
     */
    public static final int PRESET_RULE_SOURCE_TYPE = 0;

    /**
     * 自动更新的规则
     */
    public static final int AUTO_UPDATE_RULE_SOURCE_TYPE = 1;

    /**
     * 用户来源的规则
     */
    public static final int USER_RULE_SOURCE_TYPE = 2;

    /**
     * 是
     */
    public static final int TRUE = 1;

    /**
     * 否
     */
    public static final int FALSE = 0;

    /**
     * 未知
     */
    public static final int UNKNOWN = -1;

    public static final String FIX_IP_AUTH_ACCOUNT_TYPE = "1029997";

    public static final String USER_ID_KEY = "userId";

    public static int ICS_SUCCESS = 200;

    public static int ICS_INVALID_SESSION_ID = 401;
}
