package com.semptian.enums;

import lombok.Getter;

/**
 * 基站查询排序字段枚举类
 *
 * <AUTHOR>
 * @since 2024/5/9
 */
@Getter
public enum BaseStationQuerySortFieldEnum {

    STATION_NO("stationNo", "station_no"),

    COVERAGE_RADIUS("coverageRadius", "coverage_radius"),

    MODIFY_TIME("modifyTime", "modify_time");

    private final String name;

    private final String field;

    BaseStationQuerySortFieldEnum(String name, String field) {
        this.name = name;
        this.field = field;
    }

    public static String getFieldByName(String name) {
        for (BaseStationQuerySortFieldEnum value : BaseStationQuerySortFieldEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getField();
            }
        }
        return MODIFY_TIME.getField();
    }
}
