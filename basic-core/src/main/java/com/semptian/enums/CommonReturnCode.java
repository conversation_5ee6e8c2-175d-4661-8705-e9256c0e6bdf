package com.semptian.enums;

import com.semptian.base.service.ICode;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/11/14
 * @Description
 **/
@AllArgsConstructor
public enum CommonReturnCode implements ICode {


    PARAM_EXCEPTION(10005, "Parameters of the abnormal "),
    CURRENT_USERID_BLANK(10006,"当前登录用户id为空!"),
    PARAM_IS_BLANK_PLUS(1007,"%s参数为空"),

    IP_CHECK(10001,"ip Illegal or null "),
    IP_SAME(10003,"ip address repeat "),
    MASK_CHECK(10002,"MAC Illegal or null "),

    EXPORT_FILED_ERROR(10110,"设置导出字段失败!"),



    // 导出
    EXPORT_GET_DATA_ERROR(10130,"无数据导出."),
    EXPORT_ERROR(10131,"export failure"),

    FILE_PARSE_ERROR(10150,"parse error.");

    private Integer code;
    private String message;

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.message;
    }

    @Override
    public Object getData() {
        return null;
    }
}
