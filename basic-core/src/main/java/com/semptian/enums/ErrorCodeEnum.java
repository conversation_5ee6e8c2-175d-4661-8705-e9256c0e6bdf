package com.semptian.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * Date: 2020/11/12 10:50
 * Description:
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeEnum {

    SUCCESS(1,"响应成功"),
    ERROR(0,"响应失败"),

    /**
     * 10000~19999 区间表示参数错误
     */


    /**
     * 20000~29999 区间表示用户错误
     */
    USER_NOT_LOGGED_IN(20501,"用户未登录"),
    USER_LOGIN_ERROR(20502,"账号不存在或密码错误"),
    USER_ACCOUNT_FORBIDDEN(20503,"账号已被禁用"),
    USER_NOT_EXIST(20504,"用户不存在"),
    USER_HAS_EXISTED(20505,"用户已存在"),
    LOGIN_FAILED(20506,"登陆失败"),
    LOGIN_TIMEOUT(20507,"登陆超时"),

    /**
     * 30000~39999 区间表示业务异常
     */


    /**
     * 40000~49999 区间表示系统错误
     */

    SYSTEM_MEMORY_OVERFLOW(40502,"内存溢出"),
    DATABASE_EXCEPTION(40503,"数据库异常"),
    SQL_SYNTAX_EXCEPTION(40504,"sql语法异常"),
    NULL_POINTER_EXCEPTION(40505,"空指针异常"),
    CACHE_EXCEPTION(40506,"缓存异常"),
    EXCEPTION_404(40507,"404异常"),
    EXCEPTION_500(40508,"500异常"),
    VIOLATION_EXCEPTION(40509,"数据库字段参数非法异常"),
    /**
     * 50000~59999 区间表示数据异常
     */




    /**
     * 60000~69999 区间表示接口异常
     */

    INTERFACE_OUT_INVOKE_ERROR(60502,"外部系统接口调用异常"),
    INTERFACE_FORBID_VISIT(60503,"该接口禁止访问"),
    INTERFACE_ADDRESS_INVALID(60504,"接口地址无效"),
    INTERFACE_REQUEST_TIMEOUT(60505,"接口请求超时"),
    INTERFACE_EXCEED_LOAD(60506,"接口负载过高"),



    /**
     * 800000~899999区间标识业务提示信息码
     */
    PARAM_IS_INVALID(805001,"参数无效"),
    PARAM_IS_BLANK(805002,"参数为空"),
    PARAM_TYPE_BIND_ERROR(805003,"参数类型错误"),
    PARAM_NOT_COMPLETE(805004,"参数缺失"),
    PARAM_EXCEPTION(805005,"参数异常"),
    PERMISSION_NO_ACCESS(805006,"无访问权限"),
    SERVICE_EXCEPTION(805007,"业务异常"),
    FILE_FORM_EXCEPTION(805008,"文件转换异常"),
    FILE_READ_WRITE_EXCEPTION(805009,"文件读写异常"),
    DATA_BATCH_INSERT_EXCEPTION(805010,"数据批量插入异常"),
    FILE_DOWNLOAD_EXCEPTION(805011,"文件下载异常"),
    SYSTEM_INNER_ERROR(805012,"系统繁忙，请稍后重试"),
    FILE_PARSE_ERROR(805013, "解析错误"),
    RESULT_DATA_NONE(805014,"数据不存在"),
    DATA_IS_WRONG(805015,"数据有误"),
    DATA_ALREADY_EXISTED(805016,"数据已存在"),
    DATA_STATUS_NOT_MET(805017,"数据状态不满足"),
    DATA_IS_INVALID(805018,"数据无效"),
    INTERFACE_INNER_INVOKE_ERROR(805019,"内部系统接口调用异常"),
    VPM_EXCEPTION(805020,"vpm异常"),
    GROUP_NOT_EXISTS(80521,"用户组不存在"),
    NAME_EXISTS(80522,"重名"),
    EXCEL_IMPORT_FALSE(80523,"导入失败"),
    PRESET_DATA_CANNOT_DELETE(80524,"预置数据无法删除");
    private int code;
    private String msg;

}
