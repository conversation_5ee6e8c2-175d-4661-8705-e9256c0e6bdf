package com.semptian.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import java.util.Map;

@Getter
public enum FileTypeEnum {

    /**
     * xlsx
     */
    XLSX(1,"xlsx"),

    /**
     * txt
     */
    TXT(2,"txt");

    private int code;
    private String name;

    FileTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private static Map<Integer, FileTypeEnum> map = Maps.newHashMap();

    static {
        FileTypeEnum[] fileTypeEnums = FileTypeEnum.values();
        for (FileTypeEnum fileTypeEnum : fileTypeEnums) {
            map.put(fileTypeEnum.getCode(), fileTypeEnum);
        }
    }

    public static String codeOf(Integer code) {
        FileTypeEnum fileTypeEnum = map.get(code);
        String type = null;
        if (null != fileTypeEnum) {
            type = fileTypeEnum.getName();
        }
        return type;
    }
}
