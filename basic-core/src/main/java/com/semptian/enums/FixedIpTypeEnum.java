package com.semptian.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 档案类型枚举
 * <AUTHOR>
 */
public enum FixedIpTypeEnum {

    PERSONAL(1, "实体类型：个人"),

    ENTERPRISE(0, "实体类型：企业"),

    FIXED_IP(8,"fixed_ip");

    private Integer key;

    private String value;

    FixedIpTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(Integer key) {
        FixedIpTypeEnum[] values = FixedIpTypeEnum.values();
        for (FixedIpTypeEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }

    public static List<Integer> keys(){
        List<Integer> keyList = new ArrayList<>();
        FixedIpTypeEnum[] values  = FixedIpTypeEnum.values();
        for (FixedIpTypeEnum value : values) {
            Integer key = FixedIpTypeEnum.getByValue(value.value);
            keyList.add(key);
            }
        return keyList;
    }

    public static Integer getByValue(String value){
        FixedIpTypeEnum[] values = FixedIpTypeEnum.values();
        for (FixedIpTypeEnum arcTypeEnum : values) {
            if (arcTypeEnum.getValue().equals(value)){
                return arcTypeEnum.getKey();
            }
        }
        return 0;
    }

    public static void main(String[] args) {
        FixedIpTypeEnum app = FixedIpTypeEnum.valueOf("app");
        System.out.println(app.key);
    }
}
