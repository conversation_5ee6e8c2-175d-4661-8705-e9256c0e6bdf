package com.semptian.enums;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum ModuleEnum {
    /**
     * ADSL
     */
    ADSL(1, "ADSLLibrary"),
    BASE_STATION(2, "BaseStation"),
    IP_LIBRARY(3, "IpLibrary"),
    MOBILE_REGISTER(4, "MobileRegister"),
    ID_CARD(5, "IdCard"),
    NUMBER_SECTION(6, "NumberSection"),
    COUNTRY_CODE(7, "CountryCode"),
    INVALID_TELEPHONE(8, "InvalidTelephone"),
    DOMAIN_RULE(9, "DomainRule"),
    APPLICATION_RULE(10, "ApplicationRule");

    private int code;

    private String name;

    ModuleEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    private static Map<Integer, ModuleEnum> map = Maps.newHashMap();

    static {
        ModuleEnum[] moduleEnums = ModuleEnum.values();
        for (ModuleEnum moduleEnum : moduleEnums) {
            map.put(moduleEnum.getCode(), moduleEnum);
        }
    }

    public static String codeOf(Integer code) {
        String name = null;
        ModuleEnum moduleEnum = map.get(code);
        if (null != moduleEnum) {
            name = moduleEnum.getName();
        }
        return name;
    }
}
