package com.semptian.enums;

import lombok.Getter;

/**
 * 网络运营商枚举
 * <AUTHOR>
 * @since  2024/3/21
 */

@Getter
public enum NetworkOperatorEnum {

    MOBILIS(1, "<PERSON><PERSON><PERSON>"),
    OOREDOO(3, "<PERSON><PERSON><PERSON>"),
    DJ<PERSON><PERSON><PERSON><PERSON>(2, "<PERSON><PERSON><PERSON>");

    private final Integer code;
    private final String name;

    NetworkOperatorEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取运营商名称
     * @param code code
     * @return 运营商名称
     */
    public static String getNameByCode(Integer code) {
        for (NetworkOperatorEnum networkOperatorEnum : NetworkOperatorEnum.values()) {
            if (networkOperatorEnum.getCode().equals(code)) {
                return networkOperatorEnum.name;
            }
        }
        return "";
    }
}
