package com.semptian.enums;

import lombok.Getter;

/**
 * 骚扰号码类型枚举类
 *
 * <AUTHOR>
 * @since 2024/7/23
 */
@Getter
public enum SpamCallNumberTypeEnum {

    SERVICE_COMPANY_PUBLIC_PHONE(1, "服务商/公司公用电话"),

    FRAUD_PHONE(2, "诈骗电话"),

    ADVERTISEMENT_PROMOTION_PHONE(3, "广告推销电话"),

    OTHER(99, "其它");

    private final Integer code;
    private final String desc;

    SpamCallNumberTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
