package com.semptian.enums;

import lombok.Getter;

/**
 * 上网用户类型枚举
 *
 * <AUTHOR>
 * @since 2024/3/20
 */
@Getter
public enum UserTypeEnum {
    /**
     * 固网RADIUS账号
     */
    FIXED_LINE_RADIUS(1, "固网RADIUS"),
    /**
     * 移动网RADIUS账号
     */
    MOBILE_RADIUS(2, "移动网RADIUS"),
    /**
     * 固定IP账号
     */
    FIXED_IP(3, "固定IP地址"),
    /**
     * 自定义用户（IP或者IP段）
     */
    CUSTOM(4, "自定义");

    private final Integer code;

    private final String desc;

    UserTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (UserTypeEnum userTypeEnum : UserTypeEnum.values()) {
            if (userTypeEnum.getCode().equals(code)) {
                return userTypeEnum.getDesc();
            }
        }
        return "";
    }
}