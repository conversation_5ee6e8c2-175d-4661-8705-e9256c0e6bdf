package com.semptian.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExportModel {

    /**
     * 0=csv;1=Excel;3=txt
     */
    @ApiModelProperty(value = "导出文件格式类型, 0=csv;1=Excel;3=txt", name = "exportType", required = true)
    private Integer exportType;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "导出数据ID, 如果是全部则为-1", name = "ids", required = true)
    private String ids;
}
