package com.semptian.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 白名单导出model
 * @date 2024/9/14
 */
@Data
@Builder
public class ExportWhiteListModel implements Serializable {

    private static final long serialVersionUID = 1L;

    //国家码
    private String countryCode;

    // 白名单内容
    private String rule;

    // 白名单类型
    private String typeStr;

    //生效范围 多个用逗号隔开
    private String effectiveScope;

    // 备注
    private String remark;
}
