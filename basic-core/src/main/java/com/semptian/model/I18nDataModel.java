package com.semptian.model;

import lombok.Data;

import java.util.List;

/**
 * @author: SunQi
 * @create: 2021/01/09
 * desc:
 **/
@Data
public class I18nDataModel {
    /**
     * 应用标识
     */
    private String type;

    /**
     * 数据库中数据默认语言
     */
    private String defaultLang;
    /**
     * 国际化语言
     */
    private String lang;
    /**
     * 需要国际化的值
     */
    private List<String> values;
}
