package com.semptian.utils;

import cn.hutool.crypto.SecureUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/5/18 17:14
 **/
public class ArcIdUtil {

    /**
     * 获取认证账户id
     *
     * @param authType
     * @param authAccount
     * @return
     */
    public static String getAuthAccountId(String authType, String authAccount) {
        if (("1020001".equals(authType) || "1029997".equals(authType)) && StringUtils.isNotBlank(authAccount)) {
            return SecureUtil.md5(authType + "_" + authAccount) ;
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        String authAccountId = getAuthAccountId("1029997", "192.168.80.159");
        System.out.println(authAccountId);
    }
}