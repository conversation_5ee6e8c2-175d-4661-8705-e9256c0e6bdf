package com.semptian.utils;

import cn.hutool.core.io.FileUtil;
import com.csvreader.CsvReader;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;


/**
 * csv工具类
 *
 * <AUTHOR>
 * @date 2018/5/30
 */
@Slf4j
public class CsvUtils {


    private static final String CSVFILE = "csv";

    private static final byte[] UTF8_BOM = {(byte) 0xef, (byte) 0xbb, (byte) 0xbf};

    /**
     * 判断是否是csv文件
     *
     * @param fileName 文件名称
     * @return
     */
    public static boolean isCsvFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return Boolean.FALSE;
        }
        int index = fileName.lastIndexOf(".");

        if (index == -1) {
            return Boolean.FALSE;
        }
        String substring = fileName.substring(index + 1);
        if (!CSVFILE.equals(substring)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 生成为CVS文件
     *
     * @param exportData 源数据List
     * @param map        csv文件的列表头map
     * @param outPutPath 文件路径
     * @param fileName   文件名称
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static File createCsvFile(List exportData, LinkedHashMap map, String outPutPath,
                                     String fileName) {
        if (exportData == null || exportData.isEmpty() || map == null || StringUtils.isBlank(fileName)) {
            return null;
        }

        File csvFile = null;
//        File csvFile = null;
        BufferedWriter csvFileOutputStream = null;
        FileOutputStream fileOutputStream = null;
        try {
            File file = null;
//            if (!file.exists()) {
//                boolean result = file.mkdirs();
//                log.info("正在创建目录{},结果:{}", file.getAbsolutePath(), result);
//            }
            //定义文件名格式并创建
//            csvFile = File.createTempFile(fileName, ".csv", file);
            csvFile = new File(fileName);
            log.info("正在生成csv文件,文件名{}", csvFile.getName());
            fileOutputStream = new FileOutputStream(csvFile);
            fileOutputStream.write(UTF8_BOM);
            // UTF-8使正确读取分隔符","
            //如果生产文件乱码，windows下用gbk，linux用UTF-8
            csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8), 1024);

            // 写入文件头部
            for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext(); ) {
                Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
                csvFileOutputStream.write(propertyEntry.getValue() != null ? (String) propertyEntry.getValue() : "");
                if (propertyIterator.hasNext()) {
                    csvFileOutputStream.write(",");
                }
            }
            csvFileOutputStream.newLine();
            // 写入文件内容
            for (Iterator iterator = exportData.iterator(); iterator.hasNext(); ) {
                Object row = iterator.next();
                for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext(); ) {
                    Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
                    String property = BeanUtils.getProperty(row, (String) propertyEntry.getKey());
                    boolean quoteFlag = false;
                    if (property != null) {
                        if (property.contains("\"")) {
                            property = property.replace("\"", "\"\"");
                            property = "\"" + property + "\"";
                            quoteFlag = true;
                        }
                        if (property.contains(",") && !quoteFlag) {
                            property = "\"" + property + "\"";
                        }
                        // 如果值是纯数字，则在后面加\t，解决使用excel打开变成科学计数法的问题
                        if (StringUtils.isNumeric(property)) {
                            property = property + "\t";
                        }
//                        if (property.contains(" ")) {
//                            property = property.replace(" ", "");
//                        }
                        csvFileOutputStream.write(property);
                    }
                    if (propertyIterator.hasNext()) {
                        csvFileOutputStream.write(",");
                    }
                }
                if (iterator.hasNext()) {
                    csvFileOutputStream.newLine();
                }
            }
            csvFileOutputStream.flush();
        } catch (IOException | IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
            log.info("生成csv文件的过程中抛出一场:异常原因{}", e.getMessage());
        } finally {
            if (csvFileOutputStream != null) {
                try {
                    csvFileOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    // ignore
                }
            }
        }
        return csvFile;
    }

    /**
     * 下载文件
     *
     * @param response
     * @param csvFilePath 文件路径
     * @param fileName    文件名称
     * @throws IOException
     */
    public static void exportHistoryFile(HttpServletResponse response, String csvFilePath, String fileName) {
        if (response == null || StringUtils.isBlank(csvFilePath) || StringUtils.isBlank(fileName)) {
            log.info("执行下载文件时入参不合法");
            return;
        }
        try (InputStream in = new FileInputStream(csvFilePath)) {
            exportHistoryFile(response, in, fileName);
        } catch (IOException e) {
            log.info("下载文件时抛出IO异常,异常原因{}", e.getMessage());
        }
    }


    /**
     * 下载文件
     *
     * @param response
     * @param inputStream 文件路径
     * @param fileName    文件名称
     * @throws IOException
     */
    public static void exportHistoryFile(HttpServletResponse response, InputStream inputStream, String fileName) {
        if (response == null || inputStream == null || StringUtils.isBlank(fileName)) {
            log.info("下载文件失败,入参非法");
            return;
        }

        try (OutputStream out = response.getOutputStream()) {
            response.reset();
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            int len;
            byte[] buffer = new byte[1024];
            while ((len = inputStream.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } catch (IOException e) {
            log.info("下载文件时抛出IO异常,异常原因没有找到原始文件");
        } finally {
            try {
                inputStream.close();
            } catch (Exception e) {
                log.error(e.toString());
            }
        }
    }

    /**
     * 删除该目录filePath下的所有文件
     *
     * @param filePath 文件目录路径
     */
    public static void deleteFiles(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return;
        }

        FileUtil.del(filePath);
    }

    /**
     * 删除单个文件
     *
     * @param filePath 文件目录路径
     * @param fileName 文件名称
     */
    public static void deleteFile(String filePath, String fileName) {
        if (StringUtils.isBlank(fileName) || StringUtils.isBlank(filePath)) {
            return;
        }

        File file = new File(filePath, fileName);
        if (file.exists()) {
            boolean result = file.delete();
            log.info("正在删除{}文件,结果:{}", file.getPath(), result);
        }
    }

    /**
     * @param response
     * @param map        对应的列标题
     * @param exportData 需要导出的数据
     * @param fileds     列标题对应的实体类的属性
     * @throws IOException
     */
    @SuppressWarnings({"resource", "rawtypes", "unchecked"})
    public static void exportFile(HttpServletResponse response,
                                  HashMap map, List exportData, String[] fileds) throws IOException {

        // 写入临时文件
        File tempFile = File.createTempFile("vehicle", ".csv");

        try (OutputStream out = response.getOutputStream();
             InputStream in = new FileInputStream(tempFile.getCanonicalPath());
             BufferedWriter csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(tempFile), "GBK"), 1024)

        ) {

            // 写入文件头部
            for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext(); ) {
                Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
                csvFileOutputStream.write((String) propertyEntry.getValue() != null ? new String(((String) propertyEntry.getValue()).getBytes("GBK"), "GBK") : "");
                if (propertyIterator.hasNext()) {
                    csvFileOutputStream.write(",");
                }
            }
            csvFileOutputStream.write("\r\n");
            // 写入文件内容,
            // ============ //：Arraylist<实体类>填充实体类的基本信息==================
            for (int j = 0; exportData != null && fileds != null && !exportData.isEmpty() && j < exportData.size(); j++) {
                Class clazz = exportData.get(j).getClass();
                String[] contents = new String[fileds.length];
                for (int i = 0; i < fileds.length; i++) {
                    String filedName = toUpperCaseFirstOne(fileds[i]);
                    Object obj = null;
                    try {
                        Method method = clazz.getMethod(filedName);
                        method.setAccessible(true);
                        obj = method.invoke(exportData.get(j));
                    } catch (Exception e) {
                        log.info(e.getMessage());
                    }
                    String str = String.valueOf(obj);
                    if (str == null || "null".equals(str)) {
                        str = "";
                    }
                    contents[i] = str;
                }

                for (String content : contents) {
                    // 将生成的单元格添加到工作表中
                    csvFileOutputStream.write(content);
                    csvFileOutputStream.write(",");
                }
                csvFileOutputStream.write("\r\n");
            }

            csvFileOutputStream.flush();

            /**
             * 写入csv结束，写出流
             */
            byte[] b = new byte[10240];
            response.reset();
            response.setContentType("application/csv");
            String trueCsvName = "导出数据.csv";
            response.setHeader("Content-Disposition", "attachment;  filename=" + new String(trueCsvName.getBytes("GBK"), "ISO8859-1"));
            long fileLength = tempFile.getCanonicalPath().length();
            String length1 = String.valueOf(fileLength);
            response.setHeader("Content_Length", length1);
            int n;
            while ((n = in.read(b)) != -1) {
                /**
                 * 每次写入out1024字节
                 */
                out.write(b, 0, n);
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            boolean flag = tempFile.delete();
            if (flag) {
                log.info("临时文件删除成功");
            } else {
                log.info("临时文件删除失败");
            }
        }
    }

    /**
     * 将第一个字母转换为大写字母并和get拼合成方法
     *
     * @param origin
     * @return
     */
    private static String toUpperCaseFirstOne(String origin) {
        StringBuilder sb = new StringBuilder(origin);
        sb.setCharAt(0, Character.toUpperCase(sb.charAt(0)));
        sb.insert(0, "get");
        return sb.toString();
    }

    public static List<String> readCsvContent(String filePath) {
        return readCsvContentByFile(new File(filePath));
    }

    public static List<String> readCsvContentByFile(File file) {
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return readCsvContentByFile(fileInputStream);
    }

    public static List<String> readCsvContentByFile(InputStream inputStream) {
        if (inputStream == null) {
            return Collections.emptyList();
        }

        String content = getDictListByStream(inputStream);
        if (StringUtils.isBlank(content)) {
            return Collections.emptyList();
        }

        String[] lines = content.split("/n");
        if (lines.length == 0) {
            return Collections.emptyList();
        }

        return new ArrayList<>(Arrays.asList(lines).subList(1, lines.length));
    }


    public static String getDictListByStream(InputStream is) {
        if (is == null) {
            return "";
        }

        BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName("GBK")));
        StringBuilder lines = new StringBuilder();
        String line;
        StringBuilder str = new StringBuilder();
        try {
            while ((line = reader.readLine()) != null) {
                line = StringUtils.trimToEmpty(line);
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                if (StringUtils.endsWith(line, ",")) {
                    str.append(line).append(" ");
                }
                lines.append(str.toString()).append("/n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }


            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return lines.toString();
    }

    public static String appendLen4Str(String str, int length) {
        if (str == null) {
            str = "";
        }
        try {
            // 计算原字符串所占长度,规定中文占两个,其他占一个
            int strLen = 0;
            for (int i = 0; i < str.length(); i++) {
                if (isChinese(str.charAt(i))) {
                    strLen = strLen + 2;
                } else {
                    strLen = strLen + 1;
                }
            }
            if (strLen >= length) {
                return str;
            }
            // 计算所需补充空格长度
            int remain = length - strLen;
            for (int i = 0; i < remain; i++) {
                str = str + " ";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    // 根据Unicode编码完美的判断中文汉字和符号
    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
    }

    public static <T> File csvFile(List<T> list, String fileName, String[] assemblyField, String[] assemblyTitle) {
        LinkedHashMap<String, Object> map = Maps.newLinkedHashMap();
        for (int i = 0; i < assemblyField.length; i++) {
            map.put(assemblyField[i], assemblyTitle[i]);
        }
        return CsvUtils.createCsvFile(list, map, null, fileName);
    }

    public static List<Map<String, Object>> readTxt(String path, String[] fieldArr) {
        List<Map<String, Object>> mapList = Lists.newLinkedList();
        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader(path));
            String line;
            String firstLine = br.readLine();
            while ((line = br.readLine()) != null) {
                Map<String, Object> map = Maps.newLinkedHashMap();
                if (StringUtils.isNotBlank(line)) {
                    String[] recordArr = line.split("\\|", -1);
                    for (int i = 0; i < fieldArr.length; i++) {
                        map.put(fieldArr[i], recordArr[i]);
                    }
                    mapList.add(map);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("-------------readTxt Exception: {}", e.toString());
            return null;
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return mapList;
    }

    public static List<Map<String, Object>> readCsv(String path, String[] fieldArr) {
        List<Map<String, Object>> mapList = Lists.newLinkedList();
        CsvReader reader = null;
        try {
            reader = new CsvReader(path, ',', StandardCharsets.UTF_8);
            reader.readHeaders();
            while (reader.readRecord()) {
                Map<String, Object> map = Maps.newLinkedHashMap();
                String[] values = reader.getValues();
                for (int i = 0; i < fieldArr.length; i++) {
                    map.put(fieldArr[i], values[i]);
                }
                mapList.add(map);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("-------------readCsv Exception: {}", e.toString());
            return null;
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
        return mapList;
    }
}
