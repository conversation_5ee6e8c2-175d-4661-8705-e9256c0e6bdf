package com.semptian.utils;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2020/10/26 15:34
 * @Description
 **/
public class CurrentUserUtil {

    public static String getUserId(HttpServletRequest request) {
        String userId = "";
        userId = request.getHeader("userId");

        if (StringUtils.isBlank(userId)) {
            userId = request.getParameter("userId");
        }

        return userId;
    }

    public static String getLang(HttpServletRequest request){
        String lang = request.getHeader("lang");
        if (StringUtils.isBlank(lang)) {
            lang = request.getParameter("lang");
        }
        return lang;
    }
}
