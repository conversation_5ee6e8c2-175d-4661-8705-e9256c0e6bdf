package com.semptian.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * Date: 2020/9/21 11:02
 * Description:
 */
@Slf4j
public class DateUtil {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public final static String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYY = "yyyy";
    public static final String YYYY_MM_DD_HH_MM_SS_FR_DZ = "dd-MM-yyyy HH:mm:ss";
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static Integer formatYYYYMMDDToString(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String format = sdf.format(new Date(time));
        return Integer.valueOf(format);
    }
    public static Long getDayToTimes(long longTime, Integer index) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(longTime);
        calendar.add(Calendar.DAY_OF_YEAR, index);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取指定时间只包含月份的字符串
     *
     * @return 例:2018-12
     */
    public static String getMonthDate(Date date) {
        return format(date, YYYY_MM);
    }

    public static String getYearDate(Date date) {
        return format(date, YYYY) + "-00";
    }

    /**
     * 获取当前时间只包含月份的字符串
     *
     * @return 例:2018-12
     */
    public static String getCurrentMonthDate() {
        return format(now(), YYYY_MM);
    }

    /**
     * 获取下个月时间
     *
     * @return
     */
    public static Date getNextMonthTime() {
        return getDiffTime(1, Calendar.MONTH);
    }

    /**
     * 获取系统的年份
     *
     * @return
     */
    public static String getCurrentYear() {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY);
        Date date = new Date();
        return sdf.format(date);
    }

    /**
     * 格式化时间
     *
     * @param date   时间
     * @param format 时间格式
     * @return
     */
    public static String format(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 格式化时间
     *
     * @param timestamp
     * @param format
     * @return
     */
    public static String format(long timestamp, String format) {
        return format(new Date(timestamp), format);
    }


    /**
     * 当前时间格式化显示
     *
     * @param format
     * @return
     */
    public static String formatNow(String format) {
        return format(now(), format);
    }

    /**
     * 获取当前时间戳
     *
     * @return
     */
    public static long getTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间秒数
     *
     * @return
     */
    public static long getSeconds() {
        return System.currentTimeMillis() / 1000L;
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static Date now() {
        return new Date();
    }

    /**
     * 获取间隔时间
     *
     * @param diff  间隔时间正数是增加时间,负数是减少时间
     * @param field 时间类型,请使用Calendar自带的常量值
     * @return
     */
    public static Date getDiffTime(int diff, int field) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(field, diff);
        return calendar.getTime();
    }

    /**
     * 获取最新7天最早日期
     *
     * @return
     */
    public static Date get7dayTimeBefore() {
        return getDiffFirstTime(-7, Calendar.DATE);
    }

    /**
     * 获取最近一个月最早日期
     *
     * @return
     */
    public static Date get1monthTimeBefore() {
        return getDiffFirstTime(-1, Calendar.MONTH);
    }

    /**
     * 获取最近三个月最早日期
     *
     * @return
     */
    public static Date get3monthTimeBefore() {
        return getDiffFirstTime(-3, Calendar.MONTH);
    }

    /**
     * 获取最近一年最早日期
     *
     * @return
     */
    public static Date getOneYearTimeBefore() {
        return getDiffFirstTime(-1, Calendar.YEAR);
    }


    /**
     * 获取指定间隔的最早时间
     *
     * @param diff
     * @return
     */
    public static Date getDiffFirstTime(int diff, int field) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(field, diff);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定月份间隔最早的时间
     *
     * @param diff 月份间隔
     * @return
     */
    public static Date getMonthDiffFirstTime(int diff) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, diff);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定月份间隔最晚的时间
     *
     * @param diff 月份间隔
     * @return
     */
    public static Date getMonthDiffLastTime(int diff) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, diff);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getLeastMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取今天最后时间
     *
     * @return
     */
    public static Date getTodayLastTime() {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        return instance.getTime();
    }


    /**
     * 格式化日期
     *
     * @param time   待格式化的时间
     * @param format 时间格式
     * @return null表示格式化失败
     */
    public static Date parse(String time, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            Date date = sdf.parse(time);
            return date;
        } catch (ParseException e) {
            log.error(e.toString());
        }
        return null;
    }

    /**
     * 判断一个时间是否在另一个时间之前
     *
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 判断结果
     */
    public static boolean before(String time1, String time2) {
        Date date1 = parse(time1, YYYY_MM_DD_HH_MM_SS);
        Date date2 = parse(time2, YYYY_MM_DD_HH_MM_SS);
        if (date1 == null || date2 == null || !date1.before(date2)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 判断一个时间是否在另一个时间之后
     *
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 判断结果
     */
    public static boolean after(String time1, String time2) {
        Date date1 = parse(time1, YYYY_MM_DD_HH_MM_SS);
        Date date2 = parse(time2, YYYY_MM_DD_HH_MM_SS);
        if (date1 == null || date2 == null || !date1.after(date2)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 计算时间差值（单位为秒）
     *
     * @param time1 时间1
     * @param time2 时间2
     * @return 差值
     */
    public static long minus(String time1, String time2) {
        Date date1 = parse(time1, YYYY_MM_DD_HH_MM_SS);
        Date date2 = parse(time2, YYYY_MM_DD_HH_MM_SS);

        if (date1 == null || date2 == null) {
            return 0;
        }
        return Math.abs(date1.getTime() - date2.getTime());
    }

    /**
     * 获取当天日期
     *
     * @return
     */
    public static String getTodayDate() {
        return formatNow(YYYY_MM_DD);
    }

    /**
     * 时间戳转文本
     *
     * @param timestamp
     * @return
     */
    public static String formatTimeMinute(long timestamp) {
        return format(new Date(timestamp), YYYY_MM_DD_HH_MM);
    }


    /**
     * 获取指定日期下一天日期
     *
     * @param date
     * @return
     */
    public static Date getNextDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day + 1);
        return c.getTime();
    }

    /**
     * 秒级时间戳转换为文本
     *
     * @param secondTimestamp 时间戳
     * @return 文本
     * <AUTHOR>
     * @date 2020/8/31 15:57
     */
    public static String formatTimeSecond(long secondTimestamp) {
        return format(new Date(secondTimestamp * 1000L), YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 文本转换为秒级时间戳
     *
     * @param content 时间文本
     * @return 时间戳
     * <AUTHOR>
     * @date 2020/8/31 15:57
     */
    public static long formatTextToTimeSecond(String content) {

        if (StringUtils.isBlank(content)) {
            return 0L;
        }

        SimpleDateFormat simple = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        Date date;
        long timeStamp = 0L;
        try {
            date = simple.parse(content);
            timeStamp = date.getTime() / 1000L;
        } catch (ParseException e) {
            log.error(e.toString());
        }

        return timeStamp;

    }

    /**
     * 文本转换为秒级时间戳
     *
     * @param content 时间文本
     * @return 时间戳
     * <AUTHOR>
     * @date 2020/8/31 15:57
     */
    public static long timeStr2Long(String content) {

        if (StringUtils.isBlank(content)) {
            return 0L;
        }
        SimpleDateFormat simple = new SimpleDateFormat(YYYY_MM_DD);
        Date date;
        long timeStamp = 0L;
        try {
            date = simple.parse(content);
            timeStamp = date.getTime() / 1000L;
        } catch (ParseException e) {
            log.error(e.toString());
        }

        return timeStamp;

    }

    public static List<Long> getBetweenDate(String begin, String end, boolean isFilter) {

        List<Long> days = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date startDate = dateFormat.parse(begin);
            Date endDate = dateFormat.parse(end);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(startDate);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endDate);
            tempEnd.add(Calendar.DATE, 1);
            long yesterdaySecond = getYesterdaySecond();
            while (tempStart.before(tempEnd)) {
                if(isFilter && tempStart.getTime().getTime()/1000L > yesterdaySecond){
                    break;
                }
                days.add(tempStart.getTime().getTime() / 1000L);
                tempStart.add(Calendar.DATE, 1);
            }
        } catch (ParseException e) {
            log.error(e.toString());
        }
        return days;
    }

    /**
     * 获取昨天0点时间秒
     * @return
     */
    public static long getYesterdaySecond() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime().getTime() / 1000L;
    }

    /**
     * 获取某天0点时间戳
     * @param date 日期
     * @return 时间戳
     */
    public static long getDateFirstTimestamp(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * 获取某天23:59:59时间戳
     * @param date 日期
     * @return 时间戳
     */
    public static long getDateLastTimestamp(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime().getTime();
    }
}