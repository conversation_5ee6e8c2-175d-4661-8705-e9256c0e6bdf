package com.semptian.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: SunQi
 * @create: 2021/01/09
 * desc:
 **/
public class DictionaryUtils {

    /**
     * 根据请求url解析出type和model
     *
     * @param url
     * @return
     */
    public static String parseUrl(String url) {
        String[] split = url.split("/");
        return split[1];

    }

    /**
     * 根据解析格式解析JSON字符串，获取需要国际化的原始值
     *
     * @param jsonStr
     * @param parseFormat
     * @return
     */
    public static List<String> parseJOSN(String jsonStr, String parseFormat, List<String> list) {
        Object parse = JSON.parse(parseFormat);
        Object json = JSON.parse(jsonStr);
        if (parse instanceof JSONObject) {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONObject jsonFormat = JSON.parseObject(parseFormat);
            List<String> keys = jsonFormat.keySet().stream().collect(Collectors.toList());
            for (String key : keys) {
                Object format = jsonFormat.get(key);
                if (key.equals("*")) {
                    Set<String> keySet = jsonObject.keySet();
                    for (String commonkey : keySet) {
                        Object object = jsonObject.get(commonkey);
                        String objectStr = JSON.toJSONString(object);
                        String formatStr = JSON.toJSONString(jsonFormat.get(key));
                        parseJOSN(objectStr, formatStr, list);
                    }
                } else {
                    Object obj = jsonObject.get(key);
                    if (format instanceof JSONObject) {
                        JSONObject jsonObject1 = (JSONObject) obj;
                        JSONObject jsonObject2 = (JSONObject) format;
                        parseJOSN(jsonObject1.toJSONString(), jsonObject2.toJSONString(), list);
                    } else if (format instanceof JSONArray) {
                        JSONArray jsonArray1 = (JSONArray) obj;
                        JSONArray jsonArray2 = (JSONArray) format;
                        if (jsonArray2.size() == 1 && "*".equals(jsonArray2.get(0))) {
                            list.addAll(jsonArray1.toJavaList(String.class));
                        } else {
                            for (int i = 0; i < jsonArray2.size(); i++) {
                                for (int j = 0; j < jsonArray1.size(); j++) {
                                    parseJOSN(jsonArray1.get(j).toString(), jsonArray2.get(i).toString(), list);
                                }
                            }
                        }
                    } else {
                        if (obj != null) {
                            list.add(obj.toString());
                        }
                    }
                }
            }
        } else {
            JSONArray jsonArray1 = (JSONArray) json;
            JSONArray jsonArray2 = (JSONArray) parse;
            if (jsonArray2.size() == 1 && "*".equals(jsonArray2.get(0))) {
                list.addAll(jsonArray1.toJavaList(String.class));
            } else {
                for (int i = 0; i < jsonArray2.size(); i++) {
                    for (int j = 0; j < jsonArray1.size(); j++) {
                        parseJOSN(jsonArray1.get(j).toString(), jsonArray2.get(i).toString(), list);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 解析JSON字符串将原始值替换为国际化后的值
     *
     * @return
     */
    public static Object replaceI18nValue(Object json, Object parse, Map<String, String> i18nValueMap) {
        if (parse instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) json;
            JSONObject jsonFormat = (JSONObject) parse;
            List<String> keys = jsonFormat.keySet().stream().collect(Collectors.toList());
            for (String key : keys) {
                if (key.equals("*")) {
                    Set<String> keySet = jsonObject.keySet();
                    for (String commonkey : keySet) {
                        Object s = replaceI18nValue(jsonObject.get(commonkey), jsonFormat.get(key), i18nValueMap);
                        jsonObject.put(commonkey, s);
                    }
                } else {
                    Object format = jsonFormat.get(key);
                    Object obj = jsonObject.get(key);
                    if (format instanceof JSONObject) {
                        JSONObject jsonObject1 = (JSONObject) obj;
                        JSONObject jsonObject2 = (JSONObject) format;
                        Object s = replaceI18nValue(jsonObject1, jsonObject2, i18nValueMap);
                        jsonObject.put(key, s);
                    } else if (format instanceof JSONArray) {
                        JSONArray jsonArray1 = (JSONArray) obj;
                        JSONArray jsonArray2 = (JSONArray) format;
                        if (jsonArray2.size() == 1 && "*".equals(jsonArray2.get(0))) {
                            List<String> list = jsonArray1.toJavaList(String.class);
                            for(int i =0;i<list.size();i++){
                                String str = list.get(i);
                                String i18nValue = i18nValueMap.get(str);
                                jsonArray1.set(i,i18nValue);
                            }
                        } else {
                            for (int i = 0; i < jsonArray2.size(); i++) {
                                for (int j = 0; j < jsonArray1.size(); j++) {
                                    Object s = replaceI18nValue(jsonArray1.get(j), jsonArray2.get(i), i18nValueMap);
                                    jsonArray1.set(j, s);
                                }
                            }
                        }
                        jsonObject.put(key, jsonArray1);
                    } else {
                        //替换值
                        String i18nValue = i18nValueMap.get(obj);
                        jsonObject.put(key, i18nValue);
                    }
                }
            }
        } else {
            JSONArray jsonArray1 = (JSONArray) json;
            JSONArray jsonArray2 = (JSONArray) parse;
            if (jsonArray2.size() == 1 && "*".equals(jsonArray2.get(0))) {
                List<String> list = jsonArray1.toJavaList(String.class);
                for(int i =0;i<list.size();i++){
                    String str = list.get(i);
                    String i18nValue = i18nValueMap.get(str);
                    jsonArray1.set(i,i18nValue);
                }
            } else {
                for (int i = 0; i < jsonArray2.size(); i++) {
                    for (int j = 0; j < jsonArray1.size(); j++) {
                        replaceI18nValue(jsonArray1.get(j), jsonArray2.get(i), i18nValueMap);
                    }
                }
            }
        }
        return json;
    }

    /**
     * 根据JSON串获取最合适的解析格式
     *
     * @param jsonStr
     * @param formats
     * @return
     */
    public static String getParseFormat(String jsonStr, List<String> formats) {
        if (formats.size() == 1) {
            return formats.get(0);
        }
        for (int i = 0; i < formats.size(); i++) {

        }
        return null;
    }
}
