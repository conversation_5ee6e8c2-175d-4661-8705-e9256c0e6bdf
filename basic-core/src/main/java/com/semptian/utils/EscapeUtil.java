package com.semptian.utils;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2021/7/10 15:20
 * @description mysql的模糊查询时特殊字符转义
 */
public class EscapeUtil {
    public static String escapeChar(String before) {
        if (StringUtils.isNotBlank(before)) {
            before = before.replaceAll("\\\\", "\\\\\\\\");
            before = before.replaceAll("_", "\\\\_");
            before = before.replaceAll("%", "\\\\%");
        }
        return before;
    }

    /**
     * clickHouse 模糊查询时对通配符转义
     *
     * @param before
     * @return
     */
    public static String escapeCKMatch(String before) {
        if (StringUtils.isNotBlank(before)) {
            before = before.replaceAll("\\\\", "\\\\\\\\\\\\\\\\");
            before = before.replaceAll("_", "\\\\\\\\_");
            before = before.replaceAll("%", "\\\\\\\\%");
        }
        return before;
    }

    /**
     * 对sql中单引号转义
     *
     * @param str 待处理字符串
     * @return 转义后字符串
     */
    public static String escapeSingleQuote(String str) {
        if (StringUtils.isNotBlank(str)) {
            str = str.replaceAll("'", "''");
        }
        return str;
    }

    /**
     * 根据反射，对String类型的实体类数据进行转义
     * @param model
     */
    public static void escapeReflect(Object model) {
        Class<?> clazz = model.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            String fieldType = field.getGenericType().toString();
            if (fieldType.equals("class java.lang.String")) {
                try {
                    field.setAccessible(true);
                    String value = (String) field.get(model);
                    if (StringUtils.isNotBlank(value)){
                        value = escapeSingleQuote(value);
                        field.set(model,value);
                    }
                } catch (Exception e) {

                }
            }
        }
    }

}
