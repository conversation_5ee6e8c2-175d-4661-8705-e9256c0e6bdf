package com.semptian.utils;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @packageName com.quicker.sana.common.excell
 * @className ExcelCreateUtils
 * @date 2019/1/24 16:23
 */
public class ExcelCreateUtils {

    private static final String XLS = "xls";

    private static final String XLSX = "xlsx";

    private static Workbook createWorkbook(String fileName) throws IOException{
        Workbook wb;
        if (fileName.endsWith(XLS)){
            wb = new HSSFWorkbook();
        }else if (fileName.endsWith(XLSX)){
            wb = new XSSFWorkbook();
        }else {
            throw new RuntimeException("文件格式错误");
        }
        return wb;
    }

    public static <T> File createExcel(List<T> beans, String[] properties, String fileName, String filePath) throws Exception{
        return createExcel(beans, properties, null, fileName, filePath);
    }

    public static <T> File createExcel(List<T> beans, String[] properties, String[] columnComments, String fileName, String filePath) throws Exception{
        Workbook wb;
        File file = new File(fileName);
        Sheet sheet = null;
        if (!file.exists()){
            wb = createWorkbook(fileName);
            sheet = wb.createSheet();
            OutputStream os = new FileOutputStream(file);
            wb.write(os);
            os.flush();
            os.close();
        }else {
            wb = createWorkbook(fileName);
        }
        if (sheet == null){
            sheet = wb.createSheet();
        }
        int indexLength;
        if (columnComments != null){
            indexLength = columnComments.length;
            Row row = sheet.createRow(0);
            for (int i = 0; i < indexLength; i++){
                Cell cell = row.createCell(i);
                cell.setCellValue(columnComments[i]);
            }
        }else {
            indexLength = properties.length;
            Row row = sheet.createRow(0);
            for (int i = 0; i < indexLength; i++){
                Cell cell = row.createCell(i);
                cell.setCellValue(properties[i]);
            }
        }

        for (int i = 0; i < beans.size(); i++){
            Row row = sheet.createRow(i + 1);
            for (int index = 0; index < indexLength; index++){
                Cell cell = row.createCell(index);
                Object obj = PropertyUtils.getProperty(beans.get(i), properties[index]);
                if (obj != null){
                    if (obj instanceof String){
                        cell.setCellValue(String.valueOf(obj));
                    }else if(obj instanceof Double){
                        cell.setCellValue(Double.parseDouble(obj.toString()));
                    }else if (obj instanceof Integer){
                        cell.setCellValue(Integer.parseInt(obj.toString()));
                    }else if (obj instanceof Long){
                        cell.setCellValue(Long.parseLong(obj.toString()));
                    } else if (obj instanceof BigDecimal) {
                        String value = ((BigDecimal) obj).stripTrailingZeros().toPlainString();
                        cell.setCellValue(value);
                    } else {
                        cell.setCellValue("");
                    }
                }else {
                    cell.setCellValue("");
                }
            }
        }

        OutputStream os = new FileOutputStream(file);
        wb.write(os);
        os.flush();
        os.close();
        return file;
    }

    public static <T> File createExcelXlsx(List<T> beans, String[] properties, String[] columnComments, String fileName) throws Exception{
        Workbook wb;
        File file = new File(fileName);
        Sheet sheet = null;

        wb = new SXSSFWorkbook(100);
        sheet = wb.createSheet();

        // 确定列数（索引长度）
        int indexLength = (columnComments != null) ? columnComments.length : properties.length;

        // 创建表头行
        Row row = sheet.createRow(0);
        for (int i = 0; i < indexLength; i++) {
            Cell cell = row.createCell(i);
            String header = (columnComments != null) ? columnComments[i] : properties[i];
            cell.setCellValue(header);
        }

        // 填充数据
        for (int i = 0; i < beans.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);
            for (int index = 0; index < indexLength; index++) {
                Cell cell = dataRow.createCell(index);
                Object obj = PropertyUtils.getProperty(beans.get(i), properties[index]);

                // 根据不同类型的值设置单元格内容
                if (obj != null) {
                    if (obj instanceof String) {
                        cell.setCellValue(String.valueOf(obj));
                    } else if (obj instanceof Double) {
                        cell.setCellValue((Double) obj);
                    } else if (obj instanceof Integer) {
                        cell.setCellValue((Integer) obj);
                    } else if (obj instanceof Long) {
                        cell.setCellValue((Long) obj);
                    } else if (obj instanceof BigDecimal) {
                        String value = ((BigDecimal) obj).stripTrailingZeros().toPlainString();
                        cell.setCellValue(value);
                    } else {
                        cell.setCellValue("");
                    }
                } else {
                    cell.setCellValue("");
                }
            }
        }

        // 将工作簿写入文件，确保输出流只关闭一次
        try (OutputStream os = Files.newOutputStream(file.toPath())) {
            wb.write(os);
        }

        return file;
    }
}
