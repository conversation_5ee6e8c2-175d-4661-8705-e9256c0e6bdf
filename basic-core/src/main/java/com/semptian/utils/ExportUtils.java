package com.semptian.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.semptian.model.ExportModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExportUtils {

    public static <T> void export(
            ExportModel model,
            List<T> list,
            HttpServletResponse response,
            String fileName,
            String[] assemblyField,
            String[] assemblyTitle
    ) {
        String exportFileName = FileUtils.generateFileName(fileName);
        File file;
        try {
            if (model.getExportType() == TWO) {
                exportFileName = exportFileName + SUFFIX_TXT;
                file = exportTxt(exportFileName, list, assemblyTitle, assemblyField);
                //packFileToZip(file);
            } else {
                if (model.getExportType() == THREE) {
                    exportFileName = exportFileName + SUFFIX_XLS;
                    file = ExcelCreateUtils.createExcel(list, assemblyField, assemblyTitle, exportFileName, null);
                    //packFileToZip(file);
                } else {
                    exportFileName = exportFileName + SUFFIX_CSV;
                    file = CsvUtils.csvFile(list, exportFileName, assemblyField, assemblyTitle);
                    //packFileToZip(file);
                }
            }
            assert file != null;
            exportToCsv(response, file, file.getName());
            ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
            File finalFile = file;
            service.schedule(() -> {
                FileUtil.del(finalFile);
            }, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static <T> void exportWithExcelXlsx(
            ExportModel model,
            List<T> list,
            HttpServletResponse response,
            String fileName,
            String[] assemblyField,
            String[] assemblyTitle
    ) {
        String exportFileName = FileUtils.generateFileName(fileName);
        File file;
        try {
            if (model.getExportType() == TWO) {
                exportFileName = exportFileName + SUFFIX_TXT;
                file = exportTxt(exportFileName, list, assemblyTitle, assemblyField);
                //packFileToZip(file);
            } else {
                if (model.getExportType() == THREE) {
                    exportFileName = exportFileName + SUFFIX_XLSX;
                    long t1 = System.currentTimeMillis();
                    file = ExcelCreateUtils.createExcelXlsx(list, assemblyField, assemblyTitle, exportFileName);
                    log.info("create excel file cost:{}ms", (System.currentTimeMillis()-t1));

                    //packFileToZip(file);
                } else {
                    exportFileName = exportFileName + SUFFIX_CSV;
                    file = CsvUtils.csvFile(list, exportFileName, assemblyField, assemblyTitle);
                    //packFileToZip(file);
                }
            }
            assert file != null;
            long t2 = System.currentTimeMillis();
            exportToCsv(response, file, file.getName());
            log.info("export file cost:{}ms", (System.currentTimeMillis()-t2));
            ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
            File finalFile = file;
            service.schedule(() -> {
                FileUtil.del(finalFile);
            }, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static <T> File exportTxt(String fileName, List<T> list, String[] titlesArr, String[] fieldArr) {

        File outFile = new File(fileName);
        Writer writer=null;
        String enter = "\r\n";
        int length = 30;

        try {
            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile, true), StandardCharsets.UTF_8), 10240);
            StringBuilder title = new StringBuilder();
            for (String s : titlesArr) {
                title.append(CsvUtils.appendLen4Str(s, length));
            }
            title.append(enter);
            writer.write(title.toString());
            if (CollectionUtil.isNotEmpty(list)) {
                for (T entity : list) {
                    StringBuilder sb = new StringBuilder();
                    Field[] fields = entity.getClass().getDeclaredFields();
                    for (String s : fieldArr) {
                        for (Field field : fields) {
                            field.setAccessible(true);
                            if (s.equals(field.getName())) {
                                Object obj = field.get(entity);
                                String value = "";
                                if (obj != null) {
                                    value = obj.toString();
                                }
                                sb.append(CsvUtils.appendLen4Str(value, length));
                                break;
                            }
                        }
                    }
                    sb.append(enter);
                    writer.write(sb.toString());
                }
            }
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }finally {
            if (writer!=null){
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("writer close error");
                }
            }
        }
        return outFile;
    }

    private static void exportToCsv(HttpServletResponse response, File file, String fName) throws IOException {
        ZipOutputStream zipos;
        DataOutputStream os = null;
        FileInputStream in = null;
        zipos = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()));
        //设置压缩方法
        zipos.setMethod(ZipOutputStream.DEFLATED);
        try {
            File file1;
            file1 = new File(file.getCanonicalPath());
            zipos.putNextEntry(new ZipEntry(fName));
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fName, "UTF-8"));
            response.setContentType("application/octet-stream");
            os = new DataOutputStream(zipos);
            in = new FileInputStream(file1);
            IOUtils.copy(in, os);
        } catch (IOException e) {
            log.warn("file is not exists");
            log.error("exportToCsv error: {}. error msg {}.", e, e.getMessage());
        } finally {
            if (os != null) {
                os.close();
            }
            zipos.close();
            if (in != null) {
                in.close();
            }
        }
    }

    public static void packFileToZip(File file) {
        FileInputStream in=null;
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(file))) {
            byte[] buf = new byte[2 * 1024];
            zos.putNextEntry(new ZipEntry(file.getName()));
            int len;
            in = new FileInputStream(file);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            zos.closeEntry();
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }finally {
            if (in!=null){
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("close stream in error");
                }
            }
        }


    }
}