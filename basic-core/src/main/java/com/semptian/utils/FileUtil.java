package com.semptian.utils;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class FileUtil {

    public static File createDir(String path) throws IOException {
        File dir = new File(path);
        if (!dir.exists()) {
            boolean result = dir.mkdirs();
            if (!result) {
                throw new IOException("Fail to create dir！");
            }
        }
        return dir;
    }

    public static String renameFile(String fileName) {
        // 重命名(如:19701102235959_5423.txt)
        String fileType = getFileType(fileName);
        String originalFilename = getFileNameWithoutFileType(fileName);
        String newFileName = namingRule(originalFilename);
        return newFileName + (StringUtils.isEmpty(fileType) ? "" : "." + fileType);
    }

    public static String getFileNameWithoutFileType(String fileName) {
        String result = fileName;
        if (!StringUtils.isEmpty(fileName)) {
            int index = fileName.lastIndexOf(getFileType(fileName));
            result = fileName.substring(0, index - 1);
        }
        return result;
    }


    public static String getFileType(String filename) {
        String fileType;
        if (StringUtils.isEmpty(filename) || filename.lastIndexOf(".") < 0) {
            fileType = null;
        } else if (filename.endsWith(".tar.gz")) {
            fileType = "tar.gz";
        } else {
            fileType = filename.substring(filename.lastIndexOf(".") + 1);
        }
        return fileType;
    }

    public static String namingRule(String originalFilename) {
        return getCurrentTime("yyyyMMddHHmmssSSS") + "_" + UUID.randomUUID();
    }

    public static String getCurrentTime(String pattern) {
        return new SimpleDateFormat(pattern).format(new Date());
    }

    public static void saveFile(MultipartFile wavFile, String targetFilePath, String remoteFileName) throws Exception {
        File f = new File(targetFilePath);
        if (!f.exists()) {
            f.mkdirs();
        }
        File file = new File(targetFilePath, remoteFileName);
        if (file.exists()) {
            file.delete();
        }
        FileUtils.copyInputStreamToFile(wavFile.getInputStream(), file);
    }

    public static void deleteFile(String filepath) {
        File file = new File(filepath);
        if (file.exists() && file.isFile()) {
            file.delete();
        }
    }

    /**
     * 读取文件内容
     *
     * @param filePath
     * @throws IOException
     */
    public static List<String> readFile(String filePath) throws IOException {
        BufferedReader input = null;
        List<String> list = new ArrayList<>(100);
        try {
            input = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8));
            String line = input.readLine();
            while (line != null) {
                list.add(line);
                line = input.readLine();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (input != null) {
                input.close();
            }
        }
        return list;
    }

}
