package com.semptian.utils;


import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.commons.lang3.StringUtils.join;

/**
 * <AUTHOR>
 * @date 2022/05/18
 **/
public class IPv6ParseUtil {

    //    public static final String IPv6Reg = "([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}$"
//            + "|^:((:[\\da-fA-F]{1,4}){1,6}|:)$"
//            + "|^[\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:)$"
//            + "|^([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:)$"
//            + "|^([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:)$"
//            + "|^([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:)$"
//            + "|^([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?$"
//            + "|^([\\da-fA-F]{1,4}:){6}:"; // IPv6地址的格式

    /**
     * IPv6地址的格式
     */
    public static final String IPv6Reg = "((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))";


    //使用前需要使用ipv6正则判断是否是合法值
    public static String getShortIPv6(String ipv6){
        //完整ipv6临时变量
        String fullIpv6 = "";

        //入参为::时，此时全为0
        if (ipv6.equals("::")){
            fullIpv6= "0000:0000:0000:0000:0000:0000:0000:0000";
        }else{
            //入参以::结尾时，直接在后缀加0
            if (ipv6.endsWith("::")) {
                ipv6 += "0";
            }

            //遇到::填充为需要长度端的:
            String[] arrs=ipv6.split(":");
            String symbol="::";
            int arrleng=arrs.length;
            while (arrleng<8){
                symbol+=":";
                arrleng++;
            }
            ipv6=ipv6.replace("::",symbol);

            //填充所有端中0
            String fullip="";
            for (String ip:ipv6.split(":")){
                while (ip.length()<4){
                    ip="0"+ip;
                }
                fullip+=ip+':';
            }
            fullIpv6 = fullip.substring(0,fullip.length()-1);
        }

        //将ipv6变更为完整ipv6
        ipv6=fullIpv6;

        //使用:分割
        String[] arr = ipv6.split(":");

        //去掉每组数据前的0
        for (int i = 0; i < arr.length; i++){
            arr[i] = arr[i].replaceAll("^0{1,3}", "");
        }

        //最长的连续0
        String[] arr2 = arr.clone();
        for (int i = 0; i < arr2.length; i++){
            if (!"0".equals(arr2[i])){
                arr2[i] = "-";
            }
        }
        Pattern pattern = Pattern.compile("0{2,}");
        Matcher matcher = pattern.matcher(join(arr2, ""));
        String maxStr= "";
        int start = -1;
        int end = -1;
        while (matcher.find()) {
            if(maxStr.length()<matcher.group().length()) {
                maxStr=matcher.group();
                start = matcher.start();
                end = matcher.end();
            }
        }

        // 组合IPv6简写地址
        if(maxStr.length()>0) {
            for (int i = start; i < end; i++){
                arr[i] = ":";
            }
        }

        //转换结果
        String shortIP="";
        shortIP = join(arr, ":");
        shortIP= shortIP.replaceAll(":{2,}", "::");
        return shortIP;
    }
}
