package com.semptian.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * Date: 2020/9/3 15:51
 * Description:
 */
@Slf4j
public class IpUtil {

    private static final Integer EIGHT = 8;
    private static final Integer THREE = 3;

    /**
     * 将字符型IP转换成无符号整数
     *
     * @param strIp 字符串IP
     * @return long型IP
     */
    public static long iptolong(String strIp) {

        if (StringUtils.isEmpty(strIp)) {
            return -1;
        }

        long[] ip = new long[4];
        try {
            //计算每个点的位置
            int position1 = strIp.indexOf(".");
            int position2 = strIp.indexOf(".", position1 + 1);
            int position3 = strIp.indexOf(".", position2 + 1);

            //将每个。之间的字符串转换成整数
            ip[0] = Long.parseLong(strIp.substring(0, position1));
            ip[1] = Long.parseLong(strIp.substring(position1 + 1, position2));
            ip[2] = Long.parseLong(strIp.substring(position2 + 1, position3));
            ip[3] = Long.parseLong(strIp.substring(position3 + 1));
            return ((ip[0] << 24) + (ip[1] << 16) + (ip[2] << 8) + ip[3]);
        } catch (Exception e) {
            log.error(e.toString());
            return -1;
        }
    }

    /**
     * 将长整型IP转换成字符型
     *
     * @param longIp 长整形IP
     * @return String 字符型IP
     */
    public static String longtoip(long longIp) {

        StringBuffer sb = new StringBuffer();
        //直接右移24位
        sb.append(String.valueOf(longIp >>> 24));
        sb.append(".");
        //将高8位置0，然后右移16位
        sb.append(String.valueOf((longIp & 0x00FFFFFF) >>> 16));
        sb.append(".");
        //将高16位置0，然后右移8位
        sb.append(String.valueOf((longIp & 0x0000FFFF) >>> 8));
        sb.append(".");
        //将高24们置0
        sb.append(String.valueOf((longIp & 0x000000FF)));
        return sb.toString();
    }

    /**
     * 将整型IPv6转换成字符型
     *
     * @param ipInt 长整形IP
     * @return String 字符型IP
     */
    public static String iPv6NumToStr(BigInteger ipInt) {

        StringBuilder result = new StringBuilder();
        BigInteger temp;
        for (int i = 0; i < EIGHT; i++) {
            if (result.length() > 0) {
                result.insert(0, ':');
            }
            temp = ipInt.mod(BigInteger.valueOf(65536));
            result.insert(0, String.format("%1$04x", temp));
            ipInt = ipInt.shiftRight(16);
        }
        return result.toString();
    }

    /**
     * 将字符型IPv6转换成无符号整数
     *
     * @param ipStr 字符串IP
     * @return long型IP
     */
    public static BigInteger iPv6StrToNum(String ipStr) {
        if (isipv6(ipStr)) {
            String[] ipArr = ipStr.split(":");
            if (ipArr.length <= EIGHT && ipArr.length >= THREE) {
                BigInteger result = BigInteger.valueOf(0);
                // 从后往前添加数值
                int curBit = 0;
                for (int i = ipArr.length - 1; i >= 0; i--) {
                    if (i == ipArr.length - 1 && isip(ipArr[i])) {
                        long val = iptolong(ipArr[i]);
                        BigInteger temp = BigInteger.valueOf(val);
                        result = result.add(temp);
                        curBit += 2;
                    } else if (!StringUtils.isEmpty(ipArr[i])) {
                        long val = Long.parseLong(ipArr[i], 16);
                        BigInteger temp = BigInteger.valueOf(val);
                        result = result.add(temp.shiftLeft(curBit++ * 16));
                    } else {
                        curBit += 8 - ipArr.length;
                    }
                }
                return result;
            }
        }
        return BigInteger.valueOf(-1L);
    }

    private static Pattern IPV6Pat = Pattern.compile("((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))");
    private static Pattern ipv4Pat = Pattern.compile("^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");
    /**
     *     找到最长的连续的0
     */
    private static Pattern pattern = Pattern.compile("0{2,}");
    private static final String IPV6 = "((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))";
    private static final String IPV4 = "(2[5][0-5]|2[0-4]\\d|1\\d{2}|\\d{1,2})\\.(25[0-5]|2[0-4]\\d|1\\d{2}|\\d{1,2})\\.(25[0-5]|2[0-4]\\d|1\\d{2}|\\d{1,2})\\.(25[0-5]|2[0-4]\\d|1\\d{2}|\\d{1,2})";


    public static boolean isipv6(String ip) {
        if (StringUtils.isNotEmpty(ip)) {
            Matcher matcher = IPV6Pat.matcher(ip);
            return matcher.matches();
        }
        return false;
    }

    public static boolean isipv4(String ip) {
        if (StringUtils.isNotEmpty(ip)) {
            Matcher matcher = ipv4Pat.matcher(ip);
            return matcher.matches();
        }
        return false;
    }

    public static boolean isip(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        } else {
            str = str.trim();
            String test = "^(\\d|[0-9]\\d|[01]\\d\\d|[2][0-4]\\d|[2][5][0-5])(\\.(\\d|[0-9]\\d|[01]\\d\\d|[2][0-4]\\d|[2][5][0-5])){3}$";
            Pattern pattern = Pattern.compile(test);
            Matcher matcher = pattern.matcher(str);
            return matcher.matches();
        }
    }


    /**
     * 将一个IPv6地址转为全写格式，全写中的前导0省略
     * 例：将1ade:03da:0::转为1ade:3da:0:0:0:0:0:0
     *
     * @param IPv6Str
     * @return fullIPv6
     */
    public static String parseFullIPv6(String IPv6Str) {
        // 判断IPv6地址的格式是否正确
        if (!IPv6Str.matches(IPV6)) {
            return "";
        }

        String[] arr = new String[]{"0", "0", "0", "0", "0", "0", "0", "0"};

        // 将IPv6地址用::分开
        // 如果IPv6地址为::，tempArr.length==0
        // 如果不包含::或以::结尾，tempArr.length==1
        // 如果以::开头或::在中间，tempArr.length==2
        String[] tempArr = IPv6Str.split("::");

        // tempArr[0]用:分开，填充到arr前半部分
        if (tempArr.length > 0) {
            // new String[0]为空数组，因为"".split(":")为{""}，如果tempArr[0]==""，此时数组包含一个元素
            String[] tempArr0 = tempArr[0].isEmpty() ? new String[0] : tempArr[0].split(":");
            for (int i = 0; i < tempArr0.length; i++) {
                // 如果是纯数字，用parseInt去除前导0，如果包含字母，用正则去除前导0
                arr[i] = tempArr0[i].matches("\\d+")
                        ? (Integer.parseInt(tempArr0[i]) + "")
                        : tempArr0[i].replaceAll("^(0+)", "");
            }
        }

        // tempArr[1]用:分开，填充到arr后半部分
        if (tempArr.length > 1) {
            String[] tempArr1 = tempArr[1].isEmpty() ? new String[0] : tempArr[1].split(":");
            for (int i = 0; i < tempArr1.length; i++) {
                arr[i + arr.length - tempArr1.length] = tempArr1[i].matches("\\d+")
                        ? (Integer.parseInt(tempArr1[i]) + "")
                        : tempArr1[i].replaceAll("^(0+)", "");
            }
        }

        return StringUtils.join(arr, ":");
    }

    /**
     * 将一个IPv6地址转为简写格式
     * 例：将1ade:03da:0::转为1ade:3da::
     *
     * @param ipv6
     * @return AbbrIPv6
     */
    public static String parseAbbrIPv6(String ipv6) {
        // 判断IPv6地址的格式是否正确
        if (!ipv6.matches(IPV6)) {
            return "";
        }

        //完整ipv6临时变量
        String fullIpv6 = "";

        //入参为::时，此时全为0
        if (ipv6.equals("::")){
            fullIpv6= "0000:0000:0000:0000:0000:0000:0000:0000";
        }else{
            //入参以::结尾时，直接在后缀加0
            if (ipv6.endsWith("::")) {
                ipv6 += "0";
            }

            //遇到::填充为需要长度端的:
            String[] arrs=ipv6.split(":");
            String symbol="::";
            int arrleng=arrs.length;
            while (arrleng<8){
                symbol+=":";
                arrleng++;
            }
            ipv6=ipv6.replace("::",symbol);

            //填充所有端中0
            String fullip="";
            for (String ip:ipv6.split(":")){
                while (ip.length()<4){
                    ip="0"+ip;
                }
                fullip+=ip+':';
            }
            fullIpv6 = fullip.substring(0,fullip.length()-1);
        }

        //将ipv6变更为完整ipv6
        ipv6=fullIpv6;

        //使用:分割
        String[] arr = ipv6.split(":");

        //去掉每组数据前的0
        for (int i = 0; i < arr.length; i++){
            arr[i] = arr[i].replaceAll("^0{1,3}", "");
        }

        //最长的连续0
        String[] arr2 = arr.clone();
        for (int i = 0; i < arr2.length; i++){
            if (!"0".equals(arr2[i])){
                arr2[i] = "-";
            }
        }
        Matcher matcher = pattern.matcher(StringUtils.join(arr2, ""));
        String maxStr= "";
        int start = -1;
        int end = -1;
        while (matcher.find()) {
            if(maxStr.length()<matcher.group().length()) {
                maxStr=matcher.group();
                start = matcher.start();
                end = matcher.end();
            }
        }

        // 组合IPv6简写地址
        if(maxStr.length()>0) {
            for (int i = start; i < end; i++){
                arr[i] = ":";
            }
        }

        //转换结果
        String shortIP="";
        shortIP = StringUtils.join(arr, ":");
        shortIP= shortIP.replaceAll(":{2,}", "::");
        return shortIP;
    }

    /**
     * 判断一个字符串数组中的元素是否都是ipv4或者ipv6
     * @param ips 字符串数组
     * @return boolean
     */
    public static boolean checkIP(String[] ips) {
        if(ips!=null&&ips.length>0){
            for(String s : ips){
                if(!checkIP(s)){
                    return false;
                }
            }
        }else{
            return false;
        }
        return true;
    }

    /**
     * 判断某个IP是否是ipv4或者ipv6
     * @param ip ip字符串
     * @return boolean
     */
    public static boolean checkIP(String ip) {
        if(StringUtils.isBlank(ip)){
            return false;
        }
        return ip.matches(IPV4)||ip.matches(IPV6);
    }

    /**
     * 判断某个IP是否是ipv4或者ipv6,如果是ipv4,会判断端口号是否合法
     * @param ip ip字符串
     * @return boolean
     */
    public static boolean checkIPAndPort(String ip) {
        if(StringUtils.isBlank(ip)){
            return false;
        }
        //判断ip是否事ipv6带端口
        int i = ip.lastIndexOf("]:");
        if(i<0){
            //如果是ipv6,不判断端口号
            if(ip.matches(IPV6)){
                return true;
            }else{
                String[] split = ip.split(":");
                if(split.length==1){
                    return ip.matches(IPV4);
                }else if(split.length==2){
                    return split[0].matches(IPV4)&&checkPort(split[1]);
                }else{
                    return false;
                }
            }
        }else{
            String port = ip.substring(i+2);
            int a = ip.indexOf("[");
            if(a==0){
                String tempIp =ip.substring(1,i);
                if(tempIp.matches(IPV6)){
                    return checkPort(port);
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }
    }


    /**
     * 判断端口号是否合法
     * @param port 端口号
     * @return boolean
     */
    public static boolean checkPort(String port) {
        try{
            int i = Integer.parseInt(port);
            return i>=0&&i<65535;
        }catch (Exception e){
            return false;
        }
    }


    /**
     * 判断一个字符串数组中的元素是否都是ipv4或者ipv6,如果是IPV4，会判断端口号是否合法
     * @param ips 字符串数组
     * @return boolean
     */
    public static boolean checkIPSAndPort(String[] ips) {
        if(ips!=null&&ips.length>0){
            for(String s : ips){
                if(!checkIPAndPort(s)){
                    return false;
                }
            }
        }else{
            return false;
        }
        return true;
    }

    /**
     * 将带端口得IPV6转换为ip+端口得格式
     * @param ip ipv6
     * @return boolean
     */
    public static String[] parseIPAndPort(String ip) {
        String[] array = new String[2];
        if(StringUtils.isBlank(ip)){
            return null;
        }
        //判断ip是否事ipv6带端口
        int i = ip.lastIndexOf("]:");
        if(i<0){
            //如果是ipv6,不判断端口号
            if(ip.matches(IPV6)){
                array[0] = ip;
                array[1] = "";
            }else{
                String[] split = ip.split(":");
                if(split.length==1){
                    array[0] = ip;
                    array[1] = "";
                }else if(split.length==2){
                    array[0] = split[0];
                    array[1] = split[1];
                }else{
                    return null;
                }
            }
        }else{
            String port = ip.substring(i+2);
            int a = ip.indexOf("[");
            if(a==0){
                String tempIp =ip.substring(1,i);
                if(tempIp.matches(IPV6)){
                    array[0] = tempIp;
                    array[1] = port;
                }else{
                    return null;
                }
            }else{
                return null;
            }
        }
        return array;
    }


}
