package com.semptian.utils;

import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: ZC
 * @date: 2020/11/16 14:36
 */
public class StringChangeListModel {

    /**
     *  默认以字符","切除
     */
    public static final String DEFAULT_SPLIT_STR = ",";

    /**
     * 公共方法，自定义切除。将ids转换成集合.
     *
     * @param str
     * @param split
     * @return
     */
    public static List<String> splitStrToLost(String str, String split) {
        List<String> result = new ArrayList<>();
        if (!StringUtils.isEmpty(str) && !StringUtils.isEmpty(split)) {
            String[] arr = str.split(split);
            result = Arrays.asList(arr);
        }
        return result;
    }


    /**
     *  默认以"," 切除成集合
     * @param str
     * @return
     */
    public static List<String> splitStrToLost(String str) {
        List<String> result = new ArrayList<>();
        if (!StringUtils.isEmpty(str) && !StringUtils.isEmpty(DEFAULT_SPLIT_STR)) {
            String[] arr = str.split(DEFAULT_SPLIT_STR);
            result = Arrays.asList(arr);
        }
        return result;
    }



}
