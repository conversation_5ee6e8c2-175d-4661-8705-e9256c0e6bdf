package com.semptian.utils;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

import static java.util.concurrent.Executors.*;

/**
 * <AUTHOR>
 * Date: 2019/1/8 16:31
 * Modified:
 * Description: 线程池工具类
 */
public class ThreadPoolUtil {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolUtil.class);

    private static ExecutorService THREAD_POOL_EXECUTOR = newFixedThreadPool(
            getCPUCore(),
            new ThreadFactoryBuilder().setNamePrefix("common thread pool").build());

    /**
     * 知识库规则更新线程池(只有一个线程,控制访问频率)
     */
    private static ExecutorService RULE_UPDATE_THREAD_POOL_EXECUTOR = newSingleThreadExecutor(
            new ThreadFactoryBuilder().setNamePrefix("rule update thread pool").build());

    private ThreadPoolUtil() {
    }

    /**
     * 默认构造方法
     *
     * @return 返回一个线程池
     */
    public static ExecutorService getDefaultExecutorInstance() {
        return THREAD_POOL_EXECUTOR;
    }


    public static ScheduledExecutorService getScheduledThreadPoolInstance(){
        return newScheduledThreadPool(getCPUCore());
    }
    /**
     * 获取CPU核数
     *
     * @return
     */
    private static int getCPUCore() {
        int availProcess = Runtime.getRuntime().availableProcessors();
        logger.info("availProcess : {}", availProcess);
        return 2 * availProcess + 1;
    }

    /**
     * 提交规则更新任务
     * @param task 提交更新任务
     */
    public static void submitRuleUpdateTask(Runnable task) {
        RULE_UPDATE_THREAD_POOL_EXECUTOR.submit(task);
    }

    /**
     * 任务业务用线程池
     */
    private static final ExecutorService BUSINESS_THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(
            20,
            40,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("query-thread-pool").build(),
            new ThreadPoolExecutor.AbortPolicy());

    public static ExecutorService getBusinessPoolExecutor() {
        return BUSINESS_THREAD_POOL_EXECUTOR;
    }

}
