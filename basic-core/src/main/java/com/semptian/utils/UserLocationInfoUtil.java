package com.semptian.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 移动网用户位置
 *
 * <AUTHOR>
 * @since 2024/11/20
 */
@Slf4j
public class UserLocationInfoUtil {

    private static final String USER_LOCATION_INFO = "8206f3%s0%s06f3%s0%s";

    public static String getUserLocationInfo(String cgi) {
        if(StrUtil.isEmpty(cgi)) {
            return "";
        }

        String format = "";
        try {
            // 以-分隔, 第三位和第四位, 10进制转换为16进制, 分别为4位和8位, 位数不足在前面补0
            String[] split1 = cgi.split("-");
            StringBuilder s1 = new StringBuilder(Integer.toHexString(Integer.parseInt(split1[2])));
            StringBuilder s2 = new StringBuilder(Integer.toHexString(Integer.parseInt(split1[3])));
            while (s1.length() < 4) {
                s1.insert(0, "0");
            }
            while (s2.length() < 8) {
                s2.insert(0, "0");
            }
            format  = String.format(USER_LOCATION_INFO, split1[1], s1, split1[1], s2);
        }catch (Exception e) {
            log.error("getUserLocationInfo format error cgi:{}", cgi, e);
        }
        return format;
    }

    public static Integer getNetworkOperator(String cgi) {
        if(StrUtil.isEmpty(cgi)) {
            return 0;
        }

        Integer networkOperator = 0;
        try {
            String[] split = cgi.split("-");
            return Integer.parseInt(split[1]);
        }catch (Exception e) {
            log.error("getNetworkOperator error cgi:{}", cgi, e);
        }

        return networkOperator;
    }

    public static void main(String[] args) {
        String cgiList = "603-1-16407-4222721,603-1-16407-4222722,603-1-16407-4222723,603-1-16407-4222724,603-1-16407-4222725,603-1-16407-4222726,603-2-21610-106528512,603-2-21610-106528513,603-2-21610-106528514,603-2-21610-106528515,603-2-21610-106528516,603-2-21610-106528517,603-2-21610-106528610,603-3-16007-42338816,603-3-16007-42338817,603-3-16007-42338818,603-3-16007-42338836,603-3-16007-42338842,603-3-16007-42338837,603-3-16007-42338843,603-3-16007-42338838,603-3-16007-42338844,603-3-16007-42338822,603-3-16007-42338823,603-3-16007-42338824";
        String[] split = cgiList.split(",");
        for (String s : split) {
            getUserLocationInfo(s);
        }
    }
}
