package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("TB_ADSL")
public class AdslEntity {

    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 上网账号
     */
    @TableField("ONLINE_ACCOUNT")
    private String onlineAccount;

    /**
     * 账号持有人身份证号码
     */
    @TableField("IDENTITY_CARD_NUMBER")
    private String identityCardNumber;

    /**
     * 账号持有人姓名
     */
    @TableField("ACCOUNT_HOLDER_NAME")
    private String accountHolderName;

    /**
     * 设备组装电话
     */
    @TableField("ASSEMBLY_PHONE")
    private String assemblyPhone;

    /**
     * 城市代码
     */
    @TableField("CITY_CODE")
    private String cityCode;

    /**
     * 安装位置
     */
    @TableField("INSTALLATION_POSITION")
    private String installationPosition;

    /**
     * 安装时间
     */
    @TableField("INSTALLATION_TIME")
    private Long installationTime;


    /**
     * 账号持有人电话
     */
    @TableField("ACCOUNT_HOLDER_PHONE")
    private String accountHolderPhone;

    /**
     * 经度
     */
    @TableField("LONGITUDE")
    private String longitude;

    /**
     * 纬度
     */
    @TableField("LATITUDE")
    private String latitude;

    /**
     * 地址
     */
    @TableField("ADDRESS")
    private String address;

    /**
     * 运营商
     * 1.Telecom
     * 99.其他
     */
    @TableField("ISP_CODE")
    private Integer ispCode;


    /**
     * 存取模式
     * 1.专用网络真实IP地址
     * 2.专线
     * 3.ADSL号码输入
     * 4.ISDN
     * 5.普通号码
     * 6.电缆调制解调器编号
     * 7.电线
     * 8.无线上网
     * 9.其他连接方式
     */
    @TableField("ACCESS_MODE")
    private Integer accessMode;

    /**
     * 联系人姓名
     */
    @TableField("CONTACT_NAME")
    private String contactName;

    /**
     * 联系人身份证号
     */
    @TableField("CONTACT_ID_NUMBER")
    private String contactIdNumber;

    /**
     * 联系人电话
     */
    @TableField("CONTACT_NUMBER")
    private String contactNumber;

    /**
     * 数据状态
     * 0: 无效
     * 1: 有效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Long updateTime;
}
