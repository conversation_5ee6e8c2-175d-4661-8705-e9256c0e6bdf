package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:08
 **/
@Data
@TableName("BASE_APP_HOT_TABLE")
public class AppHotEntity {

    /**
     * 应用名称
     */
    private String name;

    /**
     * 小写的应用名称
     */
    @TableField("lower_name")
    private String lowerName;

    /**
     * 描述
     */
    private String description;

    @TableField("create_time")
    private Long createTime;

}
