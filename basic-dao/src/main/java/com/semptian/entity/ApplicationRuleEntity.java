package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:08
 **/
@Data
public class ApplicationRuleEntity {

    /**
     * 主键
     */
    private String id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 规则
     */
    private String rule;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态,0:停用，1：启用
     */
    private Integer status;

    private String type;

    /**
     * 来源，0：预置，1：自动更新，2：用户导入或者新增
     */
    @TableField("source_type")
    private Integer sourceType;

    @TableField("user_id")
    private Long userId;

    @TableField("create_time")
    private Long createTime;

    @TableField("update_time")
    private Long updateTime;

    @TableField(exist = false)
    private String extraName;

    @TableField(exist = false)
    private String ip;

    @TableField(exist = false)
    private String appType;
}
