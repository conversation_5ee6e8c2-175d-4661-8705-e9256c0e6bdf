package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 知识库自动更新状态配置表
 * <AUTHOR>
 * @date 2022-05-19 21:23
 **/
@Data
@TableName("BASE_RULE_CONFIG_TABLE")
public class BaseRuleConfigEntity {

    private Long id;

    @TableField("rule_type")
    private Integer ruleType;

    private Integer autoUpdateStatus;

    @TableField("create_time")
    private Long createTime;

    @TableField("update_time")
    private Long updateTime;

    @TableField("last_exec_time")
    private Long lastExecTime;

}
