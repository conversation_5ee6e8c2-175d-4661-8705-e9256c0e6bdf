package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 基站位置信息库表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@TableName("tb_base_station_location_info")
public class BaseStationLocationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //基站编号
    @TableField("station_no")
    private String stationNo;

    //纬度
    @TableField("latitude")
    private BigDecimal latitude;

    //经度
    @TableField("longitude")
    private BigDecimal longitude;

    //覆盖半径
    @TableField("coverage_radius")
    private BigDecimal coverageRadius;

    //网络类型,1=2G基站,2=3G基站,3=4G基站,4=5G基站,5=混合基站,默认为0
    @TableField("network_type")
    private Integer networkType;

    //基站地址
    @TableField("station_address")
    private String stationAddress;

    //等级,1=微小区基站,2=小区基站,3=分布式天线站,4=微站,5=宏站,默认为0
    @TableField("grade")
    private Integer grade;

    //归属运营商,1=Mobilis,2=Ooredoo,3=Djezzy,默认为0
    @TableField("network_operator")
    private Integer networkOperator;

    //描述
    @TableField("description")
    private String description;

    //删除状态,0=未删除,1=已删除,默认为0
    @TableField("is_del")
    private Integer isDel;

    //创建时间
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Long createTime;

    //更新时间
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;

    /**
     * 基站CGI编号
     */
    @TableField("cgi")
    private String cgi;

    /**
     * 基站CELL编号
     */
    @TableField("cell_name")
    private String cellName;
}
