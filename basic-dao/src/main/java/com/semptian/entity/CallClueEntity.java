package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * call线索表映射实体
 *
 * <AUTHOR>
 * @date 2020-09-04 15:22
 **/
@Data
@TableName("CLUECALLTABLE")
public class CallClueEntity {

    @TableId("CLUEID")
    private Long clueId;

    @TableField("IDTYPE")
    private Integer idType;

    @TableField("ID")
    private String id;

    @TableField("KEYWORD")
    private String keyword;

    @TableField("CB_ID")
    private Long cbId;

    @TableField("ACTIONTYPE")
    private Long actionType;

    @TableField("KEYWORDFLAG")
    private Integer keywordFlag;

    @TableField("USERID")
    private Long userId;

}
