package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 线索表映射实体
 *
 * <AUTHOR>
 * @date 2020-08-28 16:32
 **/
@Data
@TableName("CLUETABLE")
public class ClueEntity {

    @TableId("CLUEID")
    private Long clueId;

    @TableField("OBJECTID")
    private Long objectId;

    @TableField("CLUENAME")
    private String clueName;

    @TableField("CLUETYPE")
    private Integer clueType;

    @TableField("CREATETIME")
    private Long createTime;

    @TableField("CLUESTATE")
    private Integer clueState;

    @TableField("LIFETIME")
    private Long lifeTime;

    @TableField("ALARMFLAG")
    private Integer alarmFlag;

    @TableField("DATACOUNT")
    private Long dataCount;

    @TableField("CLUEINFO")
    private String clueInfo;

    @TableField("CREATEUSERID")
    private Long createUserId;

    @TableField("CREATEACCOUNT")
    private String createAccount;

    @TableField("PRODATACOUNT")
    private Long proDataCount;

//    @TableField("ACTION")
//    private Long action;

//    @TableField("CLUELEVEL")
//    private String clueLevel;

//    @TableField("PROCMETHOD")
//    private Long procMethod;

//    @TableField("CLUEFROM")
//    private Long clueFrom;

//    @TableField("REASON")
//    private String reason;

    @TableField(value = "",exist = false)
    private Integer approvalTime;

//    @TableField("VISITER_NUM")
//    private String visiterNum;

    @TableField("LATESTHITTIME")
    private Long latestHitTime;

    @TableField("OPERATETIME")
    private Long operateTime;

    @TableField("UPDATETIME")
    private Long updateTime;

    @TableField("SRC_APP_ID")
    private String srcAppId;

    @TableField("SRC_ARCHIVE_ID")
    private String srcArchiveId;

    @TableField("BATCH_NO")
    private Long batchNo;

    @TableField("MATCH_TYPE")
    private String matchType;

    @TableField("MATCH_EXPRESSION")
    private String matchExpression;

    @TableField("MATCH_RESOURCE_ID")
    private String matchResourceId;

    @TableField("MATCH_CLUE_ID")
    private String matchClueId;

}
