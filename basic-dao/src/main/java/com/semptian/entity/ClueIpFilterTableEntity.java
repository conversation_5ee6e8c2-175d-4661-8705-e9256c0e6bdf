package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: ZC
 * @date: 2020/12/15 14:24
 */
@Data
@TableName("CLUEIPFILTERTABLE")
public class ClueIpFilterTableEntity {

    /**
     *  线索号——主键
     */
    @TableId("CLUEID")
    private Integer clueId;

    /**
     *  IP地址
     */
    @TableField("IPADDR")
    private String ipAddr;

    /**
     *  IPV6地址
     */
    @TableField("IPADDR_IPV6")
    private String ipAddrIpv6;

    /**
     *  Ip类型
     */
    @TableField("IP_TYPE")
    private Integer ipType;

    /**
     *  IpMask
     */
    @TableField("IPMASK")
    private String ipMask;

    /**
     *  监控开始时间
     */
    @TableField("EXPIRESTARTTIME")
    private Long expireStartTime;

    /**
     *  监控结束时间
     */
    @TableField("EXPIREENDTIME")
    private Long expireEndTime;

    /**
     *  是否长期有效 1，短期  2，长期
     */
    @TableField("IPSTATE")
    private Integer ipState ;

    /**
     *  用户账号
     */
    @TableField("USERID")
    private Integer userId;













}
