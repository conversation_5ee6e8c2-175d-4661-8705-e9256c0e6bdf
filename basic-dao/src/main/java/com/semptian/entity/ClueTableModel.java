package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: ZC
 * @date: 2020/12/17 10:48
 */
@Data
@TableName("CLUETABLE")
public class ClueTableModel {

    /**
     *  主键id
     */
    @TableId("CLUEID")
    private Integer clueId;

    /**
     *  隶属对象号  写死-10
     */
    @TableField("OBJECTID")
    private Integer objectId;

    /**
     *  创建时间
     */
    @TableField("CREATETIME")
    private long createTime;

    /**
     *  规则状态  8停控  16在控  32待删除
     */
    @TableField("CLUESTATE")
    private Integer clueState;

    /**
     *  数量 不准确 不适用默认给0
     */
    @TableField("DATACOUNT")
    private Integer dataCount;

    /**
     *  是否触发  默认ordinaryrule
     */
    @TableField("CLUELEVEL")
    private String clueLevel;

    /**
     *  标识中标数据处理方式 0
     */
    @TableField("PROCMETHOD")
    private Integer procMethod;

    /**
     *  所属模块  6
     */
    @TableField("CLUEFROM")
    private Integer clueFrom;

    /**
     *  修改时间
     */
    @TableField("UPDATE_TIME")
    private long updateTime;

    /**
     *  ip地址
     */
    @TableField("CLUENAME")
    private String clueName;

    /**
     *  常量249
     */
    @TableField("CLUETYPE")
    private Integer clueType;

    @TableField("CLUEADDNAME")
    private String clueAddName;


}
