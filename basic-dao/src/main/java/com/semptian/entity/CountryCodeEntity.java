package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("TB_COUNTRY_CODE")
public class CountryCodeEntity {

    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 国家码
     */
    @TableField("COUNTRY_CODE")
    private String countryCode;

    /**
     * 国家名称
     */
    @TableField("COUNTRY_NAME")
    private String countryName;

    /**
     * 国家英文名称
     */
    @TableField("COUNTRY_EN_NAME")
    private String countryEnName;

    /**
     * 数据状态
     * 0: 无效
     * 1: 有效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Long updateTime;
}
