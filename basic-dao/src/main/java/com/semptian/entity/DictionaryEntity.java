package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: SunQi
 * @create: 2021/01/04
 * desc:
 **/
@Data
@TableName("tb_sys_national_dict")
public class DictionaryEntity {

    /**
     * 主键id
     */
    @TableId("id")
    private Long id;

    /**
     * 应用名
     */
    @TableField("type")
    private String type;

    /**
     * 字段的国际化key
     */
    @TableField("code")
    private String code;

    /**
     * 语言
     */
    @TableField("lang")
    private String lang;

    /**
     * 字段的国际化值
     */
    @TableField("value")
    private String value;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("modify_time")
    private Long modifyTime;

    /**
     * 更新人
     */
    @TableField("modify_user")
    private String modifyUser;
}
