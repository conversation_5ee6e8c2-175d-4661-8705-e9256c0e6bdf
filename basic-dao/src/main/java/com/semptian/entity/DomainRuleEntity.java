package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-09 21:23
 **/
@Data
public class DomainRuleEntity {

    private String id;

    private String name;

    private String rule;

    private String description;

    /**
     * 状态，0：停用，1：启用
     */
    private Integer status;

    /**
     * 分类，固定visit web site
     */
    private String type;

    /**
     * 来源，0：预置，1：自动更新，2：用户导入或者新增
     */
    @TableField("source_type")
    private Integer sourceType;

    @TableField("user_id")
    private Long userId;

    @TableField("create_time")
    private Long createTime;

    @TableField("update_time")
    private Long updateTime;

    @TableField("domain_level")
    private Integer domainLevel;

    @TableField(exist = false)
    private String extraName;

    @TableField(exist = false)
    private String ip;

    @TableField(exist = false)
    private Long ipCount;
}
