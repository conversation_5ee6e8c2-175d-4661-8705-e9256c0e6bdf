package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lmz
 * @description:
 * @date: 2022/9/1 12:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("fixed_ip_archive")
public class FixedIpEntity implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @TableField(value = "id")
    private Long id;

    /**
     * 固定IP 公共成员
     */
    /**
     * ip名称
     */
    @TableField("ip_name")
    private String ipName;

    /**
     * ip地址,如果是ipv4类型则与original_ip_address一致;如果是ipv6类型,则会存入简写处理后值
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 原始ip地址,用户输入值
     */
    @TableField("original_ip_address")
    private String originalIpAddress;

    /**
     * 实体类型：1:个人,0:企业
     */
    @TableField("entity_type")
    private Integer entityType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 个人 私有成员
     */
    /**
     * 个人/政企名称
     */
    @TableField("individual_name")
    private String individualName;

    /**
     * 性别
     */
    @TableField("person_sex")
    private Integer personSex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @TableField("birth")
    private Date birth;

    /**
     * 证件/政企类型，0：护照/有限责任公司，1：户口本/股份责任公司
     */
    @TableField("card_enterprise_type")
    private Integer cardEnterpriseType;

    /**
     * 证件号码/政企代码
     */
    @TableField("card_num_code")
    private String cardNumCode;

    /**
     * 住址/注册地址
     */
    @TableField("address")
    private String address;

    /**
     * 工作地址/官网地址
     */
    @TableField("work_address")
    private String workAddress;

    /**
     * 法人
     */
    @TableField("juridical_person")
    private String juridicalPerson;

    /**
     * 职位
     */
    @TableField("position")
    private String position;

    /**
     * 学位，0：本科，1：硕士，2：博士，3：其他
     */
    @TableField("person_degree")
    private Integer personDegree;

    /**
     * 联系电话/办公电话
     */
    @TableField("phone_num")
    private String phoneNum;

    /**
     * 政治地位
     */
    @TableField("person_political_status")
    private String personPoliticalStatus;

    /**
     * 国籍
     */
    @TableField("nationality_industry")
    private String nationalityIndustry;

    /**
     * 政企规模，0：特大型、1：大型、2中型、3：小型、4：微型、5：其他
     */
    @TableField("enterprise_scale")
    private Integer enterpriseScale;

    /**
     * 员工人数
     */
    @TableField("enterprise_employee_num")
    private Long enterpriseEmployeeNum;

    /**
     * 注册资本，单位：万
     */
    @TableField("enterprise_register_capital")
    private Double enterpriseRegisterCapital;


    /**
     * 系统参数
     */

    /**
     * 入库时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Long modifyTime;

    /**
     * 删除状态，0：未删除，1：已删除
     */
    @TableField("delete_status")
    private Integer deleteStatus;

    /**
     * 建档状态，0：未删除，1：已删除
     */
    @TableField(exist = false)
    private Integer archiveStatus;

    /**
     * 档案关注状态，0：未关注，1：已关注
     */
    @TableField(exist = false)
    private Integer isCare;

    /**
     * 警告提示符，用于提示用户某个字段长度超出限制
     */
    @TableField(exist = false)
    private String warnMsg;
}
