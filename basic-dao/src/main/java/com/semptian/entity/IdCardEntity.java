package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("TB_IDCARD")
public class IdCardEntity {

    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 城市代码
     */
    @TableField("CITY_CODE")
    private String cityCode;


    /**
     * 互联网服务位置代码
     */
    @TableField("NET_SITE_ID")
    private String netSiteId;

    /**
     * 互联网服务标题
     */
    @TableField("NET_SITE_NAME")
    private String netSiteName;

    /**
     * 网卡号
     */
    @TableField("CARD_ID")
    private String cardId;

    /**
     * 上网人员姓名
     */
    @TableField("USERNAME")
    private String userName;

    /**
     * 性别
     */
    @TableField("SEX")
    private Integer sex;

    /**
     * 证件类型
     */
    @TableField("CERTIFICATE_TYPE")
    private String certificateType;

    /**
     * 证件号码
     */
    @TableField("CERTIFICATE_CODE")
    private String certificateCode;

    /**
     * 发证单位名称
     */
    @TableField("CERTIFICATION_UNIT")
    private String certificationUnit;

    /**
     * 国籍
     */
    @TableField("NATIONALITY")
    private String nationality;

    /**
     * 单位名称
     */
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 联系电话
     */
    @TableField("MOBILE")
    private String mobile;

    /**
     * 生日
     */
    @TableField("BIRTHDAY")
    private String birthday;

    /**
     * 房间号
     */
    @TableField("ROOM_ID")
    private String roomId;

    /**
     * 数据状态
     * 0: 无效
     * 1: 有效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Long updateTime;
}
