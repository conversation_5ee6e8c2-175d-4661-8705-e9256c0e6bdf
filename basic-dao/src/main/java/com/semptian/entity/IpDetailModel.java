package com.semptian.entity;

import lombok.Data;

/**
 * @author: ZC
 * @date: 2020/12/18 20:12
 */
@Data
public class IpDetailModel {

    /**
     * id
     */
    private Integer clueId;

    /**
     * ip地址
     */
    private String ipAddr;

    /**
     * MAC掩码
     */
    private String ipMask;

    /**
     * 规则状态  8停控  16在控  32待删除
     */
    private Integer clueState;

    /**
     * 状态描述
     */
    private String clueStateStr;

    /**
     * 修改时间
     */
    private long updateTime;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     *  监控开始时间
     */
    private Long expireStartTime;

    /**
     *  监控结束时间
     */
    private Long expireEndTime;

    /**
     *  是否长期有效 1，短期  2，长期
     */
    private Integer ipState ;
}
