package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("TB_IP_LIBRARY")
public class IpLibraryEntity {
    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 唯一标识
     */
    @TableField("IP_ID")
    private String ipId;

    /**
     * 启动IP
     */
    @TableField("START_IP")
    private String startIp;

    /**
     * 结束IP
     */
    @TableField("END_IP")
    private String endIp;

    /**
     * 公司名称
     */
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 地址
     */
    @TableField("ADDRESS")
    private String address;

    /**
     * 联系人姓名
     */
    @TableField("CONTACT_NAME")
    private String contactName;

    /**
     * 联系人手机号码
     */
    @TableField("CONTACT_NUMBER")
    private String contactNumber;

    /**
     * 数据状态
     * 0: 无效
     * 1: 有效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Long updateTime;

}
