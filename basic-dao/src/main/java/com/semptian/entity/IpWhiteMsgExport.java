package com.semptian.entity;

import lombok.Data;

/**
 * @author: ZC
 * @date: 2020/12/17 17:39
 */
@Data
public class IpWhiteMsgExport {

    /**
     * id
     */
    private Integer clueId;

    /**
     * ip地址
     */
    private String ipAddr;

    /**
     * MAC掩码
     */
    private String ipMask;

    /**
     * 规则状态  8停控  16在控  32待删除
     */
//    private Integer clueState;

    /**
     * 状态描述
     */
    private String clueStateStr;

    /**
     * 修改时间
     */
    private String updateTime;




}
