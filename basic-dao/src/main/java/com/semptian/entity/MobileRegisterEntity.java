package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: ZC
 * @date: 2021/3/11 19:56
 * 手机注册信息
 */
@Data
@TableName("TB_MOBILE_REGISTER")
public class MobileRegisterEntity {

    /**
     * 主键ID
     */
    @TableId("ID")
    private Long id;

    /**
     * IMSI号
     */
    @TableField("IMSI")
    private String imsi;

    /**
     * 手机号码
     */
    @TableField("MSISDN")
    private String msisdn;

    /**
     * 姓名
     */
    @TableField("NAME")
    private String name;

    /**
     * 性别
     * 1: 男
     * 2: 女
     * 3: 未知
     */
    @TableField("SEX")
    private Integer sex;

    /**
     * 年龄
     */
    @TableField("AGE")
    private Integer age;

    /**
     * 国籍
     */
    @TableField("NATIONALITY")
    private String nationality;

    /**
     * 文化程度
     */
    @TableField("EDUCATION")
    private String education;

    /**
     * 证件类型
     */
    @TableField("CERTIFICATE_TYPE")
    private String certificateType;

    /**
     * 证件号
     */
    @TableField("CERTIFICATE_CODE")
    private String certificateCode;

    /**
     * 单位名称
     */
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 涉嫌组织
     */
    @TableField("ALLEGED_ORG")
    private String allegedOrg;

    /**
     * 户口所在地
     */
    @TableField("REGISTERED_ADDRESS")
    private String registeredAddress;

    /**
     * 现住址
     */
    @TableField("NOW_ADDRESS")
    private String nowAddress;

    /**
     * 联系人电话
     */
    @TableField("CONTACT_NUMBER")
    private String contactNumber;

    /**
     * 邮编
     */
    @TableField("POST_CODE")
    private String postCode;

    /**
     * 邮箱
     */
    @TableField("EMAIL")
    private String email;

    /**
     * 生日
     */
    @TableField("BIRTHDAY")
    private String birthday;

    /**
     * 开通日期
     */
    @TableField("OPEN_DATE")
    private Long openDate;

    /**
     * 无效日期
     */
    @TableField("INVALID_DATE")
    private Long invalidDate;

    /**
     * 房间号
     */
    @TableField("ROOM_ID")
    private String roomId;

    /**
     * 居住类型
     * 1.普通护照
     * 2.出入境许可证
     * 3. 外国人入境许可证
     * 4.外国人永久居留证
     * 5:外国人临时居留证、
     * 6:入籍证明
     */
    @TableField("RESIDENCE_TYPE")
    private Integer residenceType;

    /**
     * 数据状态
     * 0: 无效
     * 1: 有效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Long updateTime;

}
