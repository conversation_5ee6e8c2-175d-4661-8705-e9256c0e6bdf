package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * @author: SunQi
 * @create: 2021/01/08
 * desc:
 **/
@TableName("tb_sys_national_conf")
@Data
public class NationalConfigEntity {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 请求路径
     */
    @TableField("url")
    private String url;

    /**
     * JSON解析格式
     */
    @TableField("parse_format")
    private String parseFormat;

    /**
     * 模糊查询参数名称
     */
    @TableField("keyword")
    private String keyword;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;


}
