package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("TB_MOBILE_SECTION")
public class NumberSectionEntity {
    /**
     * 主键ID
     */
    @TableId("ID")
    private Long id;

    /**
     * 手机号码
     */
    @TableField("MSISDN")
    private String msisdn;

    /**
     * 城市代码
     */
    @TableField("CITY_CODE")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField("CITY_NAME")
    private String cityName;

    /**
     * 网络类型
     */
    @TableField("NET_TYPE")
    private String netType;

    /**
     * 邮编号
     */
    @TableField("POST_CODE")
    private String postCode;

    /**
     * 区号
     */
    @TableField("AREA_CODE")
    private String areaCode;

    /**
     * 运营商
     * 1.Telecom
     * 2.其他
     */
    @TableField("ISP_CODE")
    private Integer ispCode;

    /**
     * 数据状态
     * 0: 无效
     * 1: 有效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    private Long updateTime;
}
