package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 号码国际区号实体类
 * <AUTHOR>
 * @since 2024/5/10
 */

@Data
public class PhoneCodeAreaEntity implements Serializable  {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //号码国际区号
    @TableField("phone_code")
    private Integer phoneCode;

    //号码国际区号对应国家码
    @TableField("country_code")
    private String countryCode;
}
