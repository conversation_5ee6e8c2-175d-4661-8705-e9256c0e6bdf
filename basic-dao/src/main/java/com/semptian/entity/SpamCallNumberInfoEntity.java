package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 骚扰号码信息库表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@TableName("tb_spam_call_number_info")
public class SpamCallNumberInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //号码,包含国家码,多个用逗号拼接,最多5个号码
    @TableField("phone_number")
    private String phoneNumber;

    //名称
    @TableField("name")
    private String name;

    //号码类型:1=服务商/公司公用电话,2=诈骗电话,3=广告推销电话,99=其它
    @TableField("type")
    private Integer type;

    //备注
    @TableField("remark")
    private String remark;

    //删除状态,0=未删除,1=已删除,默认为0
    @TableField("is_del")
    private Integer isDel;

    //创建时间
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Long createTime;

    //更新时间
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;


}
