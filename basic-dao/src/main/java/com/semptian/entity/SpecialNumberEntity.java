package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2020/12/17 14:19
 */
@Data
@TableName("SPECIALTELEFILTERTABLE")
public class SpecialNumberEntity {

    @TableId(value = "ID")
    private Long id;

    @TableField("PHONENUM")
    private String phoneNum;

    @TableField("PHONESTATE")
    private Integer phoneState;

    @TableField("REMARK")
    private String remark;

    @TableField("CREATORNM")
    private String creatorNm;

    @TableField("CREATETIME")
    private Long createTime;

    @TableField("UPDATE_TIME")
    private Long updateTime;

    @TableField("COUNTRY_CODE")
    private String countryCode;

    @TableField("TELEPHONE_NUM")
    private String telephoneNum;



}
