package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 上网用户分类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@TableName("tb_user_category")
@EqualsAndHashCode
public class UserCategoryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //分类名称
    @TableField("name")
    private String name;

    //父级分类ID,默认为0
    @TableField("pid")
    private Integer pid;

    //层级
    @TableField("level")
    private Integer level;

    //是否叶子节点,0=否,1=是,默认为1
    @TableField("is_last")
    private Integer isLast;

    //数据来源,1=系统预置,2=用户新增,默认为2
    @TableField("source")
    private Integer source;

    //删除状态,0=未删除,1=已删除,默认为0
    @TableField("is_del")
    private Integer isDel;

    //创建时间
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Long createTime;

    //更新时间
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
}
