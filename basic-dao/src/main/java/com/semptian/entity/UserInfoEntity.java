package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 上网用户信息库表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@TableName("tb_user_info")
public class UserInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //上网用户账号
    @TableField("user_name")
    private String userName;

    //用户类型 1=固网RADIUS账号,3=固定IP账号,2=移动网RADIUS账号,4=自定义用户（IP或者IP段）
    @TableField("user_type")
    private Integer userType;

    //备注名称
    @TableField("remark")
    private String remark;

    //用户分类
    @TableField("user_category_id")
    private Integer userCategoryId;

    //状态,1=启用,2=停用,默认为1
    @TableField("status")
    private Integer status;

    //删除状态,0=未删除,1=已删除,默认为0
    @TableField("is_del")
    private Integer isDel;

    //创建时间
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Long createTime;

    //更新时间
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
}
