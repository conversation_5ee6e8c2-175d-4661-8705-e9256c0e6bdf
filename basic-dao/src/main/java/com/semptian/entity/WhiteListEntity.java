package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 白名单信息表
 * @date 2024/9/14
 */
@Data
@Builder
@TableName("tb_white_list")
public class WhiteListEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    //国家码
    @TableField("country_code")
    private String countryCode;

    // 白名单内容
    @TableField("rule")
    private String rule;

    // 格式化后的白名单内容
    @TableField("format_rule")
    private String formatRule;

    //白名单类型：1.ip  2.radius 3.mobile phone 4. fixed phone
    @TableField("type")
    private Integer type;

    //白名单状态：0:删除；1.在控中；2.在控；3.停控中；4.停控
    @TableField("status")
    private Integer status;

    //生效范围：1：上网数据；2：Phone; 多个用逗号隔开
    @TableField("effective_scope")
    private String effectiveScope;

    // 备注
    @TableField("remark")
    private String remark;

    //创建时间
    @TableField(value = "create_time")
    private Long createTime;

    //修改时间
    @TableField(value = "modify_time")
    private Long modifyTime;

    //创建人
    @TableField(value = "create_user")
    private String createUser;

    //修改人
    @TableField(value = "modify_user")
    private String modifyUser;
}
