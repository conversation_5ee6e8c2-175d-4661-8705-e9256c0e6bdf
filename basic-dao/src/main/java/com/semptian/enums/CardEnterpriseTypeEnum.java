package com.semptian.enums;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public enum CardEnterpriseTypeEnum {

    PASSPORT(0, "护照","有限责任公司", "passeport","société à responsabilité limitée"),

    HU_KOU_BEN(1, "户口本","股份责任公司", "livret de famille","société à responsabilité par actions"),

    ID(2, "身份证","其它", "carte d’identité","autres");

    private Integer key;

    private String china;

    private String china1;

    private String french;

    private String french1;

    CardEnterpriseTypeEnum(Integer key, String china,String china1, String french, String french1) {
        this.key = key;
        this.china = china;
        this.china1 = china1;
        this.french = french;
        this.french1 = french1;
    }

    public Integer getKey() {
        return key;
    }

    public String getChina1() {
        return china1;
    }

    public String getFrench1() {
        return french1;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            CardEnterpriseTypeEnum[] values = CardEnterpriseTypeEnum.values();
            for (CardEnterpriseTypeEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            CardEnterpriseTypeEnum[] values = CardEnterpriseTypeEnum.values();
            for (CardEnterpriseTypeEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static Integer getKeyByValue(String key, String lang,int type) {
        if ("zh_CN".equals(lang)) {
            CardEnterpriseTypeEnum[] values = CardEnterpriseTypeEnum.values();
            for (CardEnterpriseTypeEnum value : values) {
                if (type == EntityTypeEnum.ENTERPRISE.getKey()){
                    if (value.getChina1().equals(key)) {
                        return value.getKey();
                    }
                }else if (type == EntityTypeEnum.PERSON.getKey()){
                    if (value.getChina().equals(key)) {
                        return value.getKey();
                    }
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            CardEnterpriseTypeEnum[] values = CardEnterpriseTypeEnum.values();
            for (CardEnterpriseTypeEnum value : values) {
                if (type == EntityTypeEnum.ENTERPRISE.getKey()){
                    if (value.getFrench1().equals(key)) {
                        return value.getKey();
                    }
                }else if (type == EntityTypeEnum.PERSON.getKey()){
                    if (value.getFrench().equals(key)) {
                        return value.getKey();
                    }
                }
            }
        }
        return -1;
    }

    public static String[] getByValue(String lang,int type) {
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)) {
            CardEnterpriseTypeEnum[] values = CardEnterpriseTypeEnum.values();
            for (CardEnterpriseTypeEnum value : values) {
                if (type == EntityTypeEnum.ENTERPRISE.getKey()){
                    stringJoiner.add(value.getChina1());
                }else if (type == EntityTypeEnum.PERSON.getKey()){
                    stringJoiner.add(value.getChina());
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            CardEnterpriseTypeEnum[] values = CardEnterpriseTypeEnum.values();
            for (CardEnterpriseTypeEnum value : values) {
                if (type == EntityTypeEnum.ENTERPRISE.getKey()){
                    stringJoiner.add(value.getFrench1());
                }else if (type == EntityTypeEnum.PERSON.getKey()){
                    stringJoiner.add(value.getFrench());
                }
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }

}
