package com.semptian.enums;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public enum EnterpriseScaleEnum {

    SUPER(0, "特大型","très grande"),

    BIG(1, "大型","grande"),

    MILL(2, "中型","moyenne"),

    SMALL(3, "小型","petite"),

    MICRO(4, "微型","micro"),

    OTHER(5, "其他","autres");

    private Integer key;

    private String china;

    private String french;

    EnterpriseScaleEnum(Integer key, String china, String french) {
        this.key = key;
        this.china = china;
        this.french = french;
    }

    public Integer getKey() {
        return key;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key,String lang) {
        if ("zh_CN".equals(lang)){
            EnterpriseScaleEnum[] values = EnterpriseScaleEnum.values();
            for (EnterpriseScaleEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)){
            EnterpriseScaleEnum[] values = EnterpriseScaleEnum.values();
            for (EnterpriseScaleEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static Integer getKeyByValue(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            EnterpriseScaleEnum[] values = EnterpriseScaleEnum.values();
            for (EnterpriseScaleEnum value : values) {
                if (value.getChina().equals(key)) {
                    return value.getKey();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            EnterpriseScaleEnum[] values = EnterpriseScaleEnum.values();
            for (EnterpriseScaleEnum value : values) {
                if (value.getFrench().equals(key)) {
                    return value.getKey();
                }
            }
        }
        return -1;
    }

    public static String[] getByValue(String lang){
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)){
            EnterpriseScaleEnum[] values = EnterpriseScaleEnum.values();
            for (EnterpriseScaleEnum value : values) {
                stringJoiner.add(value.getChina());
            }
        }
        if ("fr_DZ".equals(lang)){
            EnterpriseScaleEnum[] values = EnterpriseScaleEnum.values();
            for (EnterpriseScaleEnum value : values) {
                stringJoiner.add(value.getFrench());
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }

}
