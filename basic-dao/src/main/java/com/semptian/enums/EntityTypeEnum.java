package com.semptian.enums;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public enum EntityTypeEnum {

    PERSON(1, "个人","personnes"),

    ENTERPRISE(0, "企业","société");

    private Integer key;

    private String china;

    private String french;

    EntityTypeEnum(Integer key, String china, String french) {
        this.key = key;
        this.china = china;
        this.french = french;
    }

    public Integer getKey() {
        return key;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key,String lang) {
        if ("zh_CN".equals(lang)){
            EntityTypeEnum[] values = EntityTypeEnum.values();
            for (EntityTypeEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)){
            EntityTypeEnum[] values = EntityTypeEnum.values();
            for (EntityTypeEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static String[] getByValue(String lang,Integer key){
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)){
            EntityTypeEnum[] values = EntityTypeEnum.values();
            for (EntityTypeEnum value : values) {
                if (key.equals(value.getKey())){
                    stringJoiner.add(value.getChina());
                }
            }
        }
        if ("fr_DZ".equals(lang)){
            EntityTypeEnum[] values = EntityTypeEnum.values();
            for (EntityTypeEnum value : values) {
                if (key.equals(value.getKey())){
                    stringJoiner.add(value.getFrench());
                }
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }

    public static Integer getKeyByValue(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            EntityTypeEnum[] values = EntityTypeEnum.values();
            for (EntityTypeEnum value : values) {
                if (value.getChina().equals(key)) {
                    return value.getKey();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            EntityTypeEnum[] values = EntityTypeEnum.values();
            for (EntityTypeEnum value : values) {
                if (value.getFrench().equals(key)) {
                    return value.getKey();
                }
            }
        }
        return -1;
    }

}
