package com.semptian.enums;

/**
 * <AUTHOR>
 * @description 实时查看固定IP状态
 * @date 2022/10/10
 */
public enum FixedIpArchiveStatus {
    CARE(1,"存在关注档案、已建档"),
    ARCHIVES(2,"存在已建档"),
    NOBODY(3,"什么都不存在，允许直接删除");

    private int key;
    private String value;

    FixedIpArchiveStatus(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
