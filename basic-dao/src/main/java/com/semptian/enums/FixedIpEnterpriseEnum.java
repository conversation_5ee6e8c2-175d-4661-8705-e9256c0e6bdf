package com.semptian.enums;

import java.util.StringJoiner;

/**
 * @author: lmz
 * @description:
 * @date: 2022/9/13 21:06
 */
public enum FixedIpEnterpriseEnum {

    IP_NAME("IP_NAME","IP名称","Nom de IP",0),

    IP_ADDRESS("IP_ADDRESS","IP地址","IP fixe",1),

    ENTITY_TYPE("ENTITY_TYPE","实体类型","Type d'entité",2),

    REMARK("REMARK","备注","Remarques",3),

    INDIVIDUAL_NAME("INDIVIDUAL_NAME","政企名称","Nom de société",4),

    BIRTH("BIRTH","成立日期","Date de création",5),

    CARD_ENTERPRISE_TYPE("CARD_ENTERPRISE_TYPE","政企类型","Type de société",6),

    CARD_NUM_CODE("CARD_NUM_CODE","政企代码","Code de société",7),

    PHONE_NUM("PHONE_NUM","办公电话","Numéro officiel",8),

    ADDRESS("ADDRESS","注册地址","Adresse d'enregistrement",9),

    WORK_ADDRESS("WORK_ADDRESS","官网地址","Site officiel",10),

    JURIDICAL_PERSON("JURIDICAL_PERSON","法人","Personne juridique",11),

    ENTERPRISE_SCALE("ENTERPRISE_SCALE","政企规模","Taille de société",12),

    ENTERPRISE_EMPLOYEE_NUM("ENTERPRISE_EMPLOYEE_NUM","员工人数","Nombre d'employé",13),

    ENTERPRISE_REGISTER_CAPITAL("ENTERPRISE_REGISTER_CAPITAL","注册资本，单位万","Capital de société，unité : Dix mille",14);

    private String key;

    private String china;

    private String french;

    private int sort;

    FixedIpEnterpriseEnum(String key, String china, String french, int sort) {
        this.key = key;
        this.china = china;
        this.french = french;
        this.sort = sort;
    }

    public int getSort() {
        return sort;
    }

    public String getKey() {
        return key;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key,String lang) {
        if ("zh_CN".equals(lang)){
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                if (value.getChina().equals(key)) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)){
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                if (value.getFrench().equals(key)) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static String[] getByValue(String lang){
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)){
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                stringJoiner.add(value.getChina());
            }
        }
        if ("fr_DZ".equals(lang)){
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                stringJoiner.add(value.getFrench());
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }

    public static String  getKeyLang(String key,String lang) {
        if ("zh_CN".equals(lang)){
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                if (value.getKey().equals(key)) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)){
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                if (value.getKey().equals(key)) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static String getByKey(String key, String lang, int index) {
        if ("zh_CN".equals(lang)) {
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                if (value.getChina().equals(key) && value.getSort() == index) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            FixedIpEnterpriseEnum[] values = FixedIpEnterpriseEnum.values();
            for (FixedIpEnterpriseEnum value : values) {
                if (value.getFrench().equals(key) && value.getSort() == index) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }
}
