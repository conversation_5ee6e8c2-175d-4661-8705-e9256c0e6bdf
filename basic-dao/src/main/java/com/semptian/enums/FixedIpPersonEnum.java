package com.semptian.enums;

import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public enum FixedIpPersonEnum {


    IP_NAME("IP_NAME", "IP名称", "Nom de IP", 0),

    IP_ADDRESS("IP_ADDRESS", "IP地址", "IP fixe", 1),

    ENTITY_TYPE("ENTITY_TYPE", "实体类型", "Type d'entité", 2),

    REMARK("REMARK", "备注", "Remarques", 3),

    INDIVIDUAL_NAME("INDIVIDUAL_NAME", "个人名称", "Nom de personne", 4),

    PERSON_SEX("PERSON_SEX", "性别", "Sexe", 5),

    BIRTH("BIRTH", "出生日期", "Date de naissance", 6),

    CARD_ENTERPRISE_TYPE("CARD_ENTERPRISE_TYPE", "证件类型", "Type de carte", 7),

    PHONE_NUM("PHONE_NUM", "联系电话", "Numéro de téléphone", 8),

    CARD_NUM_CODE("CARD_NUM_CODE", "证件号码", "Numéro de pièce", 9),

    ADDRESS("ADDRESS", "住址", "Adresse", 10),

    WORK_ADDRESS("WORK_ADDRESS", "工作地址", "Adresse de travail", 11),

    POSITION("POSITION", "职位", "Position", 12),

    PERSON_DEGREE("PERSON_DEGREE", "学历", "Niveau éducatif", 13),

    PERSON_POLITICAL_STATUS("PERSON_POLITICAL_STATUS", "政治地位", "Position politique", 14),

    NATIONALITY_INDUSTRY("NATIONALITY_INDUSTRY", "国籍", "Nationalité", 15);

    private String key;

    private String china;

    private String french;

    private int sort;

    FixedIpPersonEnum(String key, String china, String french, int sort) {
        this.key = key;
        this.china = china;
        this.french = french;
        this.sort = sort;
    }

    public int getSort() {
        return sort;
    }

    public String getKey() {
        return key;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                if (value.getChina().equals(key)) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                if (value.getFrench().equals(key)) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static String[] getByValue(String lang) {
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                stringJoiner.add(value.getChina());
            }
        }
        if ("fr_DZ".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                stringJoiner.add(value.getFrench());
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }

    public static String getKeyLang(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                if (value.getKey().equals(key)) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                if (value.getKey().equals(key)) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static String getByKey(String key, String lang, int index) {
        if ("zh_CN".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                if (value.getChina().equals(key) && value.getSort() == index) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            FixedIpPersonEnum[] values = FixedIpPersonEnum.values();
            for (FixedIpPersonEnum value : values) {
                if (value.getFrench().equals(key) && value.getSort() == index) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }
}
