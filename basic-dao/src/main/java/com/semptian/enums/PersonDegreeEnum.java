package com.semptian.enums;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public enum PersonDegreeEnum {

    SCHOLAR(0, "本科","licence"),

    MASTER(1, "硕士","mastère"),

    DOCTOR(2, "博士","doctorat"),

    OTHER(3, "其他","autres");

    private Integer key;

    private String china;

    private String french;

    PersonDegreeEnum(Integer key, String china, String french) {
        this.key = key;
        this.china = china;
        this.french = french;
    }

    public Integer getKey() {
        return key;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key,String lang) {
        if ("zh_CN".equals(lang)){
            PersonDegreeEnum[] values = PersonDegreeEnum.values();
            for (PersonDegreeEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)){
            PersonDegreeEnum[] values = PersonDegreeEnum.values();
            for (PersonDegreeEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static Integer getKeyByValue(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            PersonDegreeEnum[] values = PersonDegreeEnum.values();
            for (PersonDegreeEnum value : values) {
                if (value.getChina().equals(key)) {
                    return value.getKey();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            PersonDegreeEnum[] values = PersonDegreeEnum.values();
            for (PersonDegreeEnum value : values) {
                if (value.getFrench().equals(key)) {
                    return value.getKey();
                }
            }
        }
        return -1;
    }

    public static String[] getByValue(String lang){
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)){
            PersonDegreeEnum[] values = PersonDegreeEnum.values();
            for (PersonDegreeEnum value : values) {
                stringJoiner.add(value.getChina());
            }
        }
        if ("fr_DZ".equals(lang)){
            PersonDegreeEnum[] values = PersonDegreeEnum.values();
            for (PersonDegreeEnum value : values) {
                stringJoiner.add(value.getFrench());
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }
}
