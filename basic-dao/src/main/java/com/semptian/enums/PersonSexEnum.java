package com.semptian.enums;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public enum PersonSexEnum {

    MALE(1, "男", "masculin"),

    FEMALE(0, "女", "féminin"),

    UNKNOWN(2, "未知", "inconnu");

    private Integer key;

    private String china;

    private String french;

    PersonSexEnum(Integer key, String china, String french) {
        this.key = key;
        this.china = china;
        this.french = french;
    }

    public Integer getKey() {
        return key;
    }

    public String getChina() {
        return china;
    }

    public String getFrench() {
        return french;
    }

    public static String getByKey(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            PersonSexEnum[] values = PersonSexEnum.values();
            for (PersonSexEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getChina();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            PersonSexEnum[] values = PersonSexEnum.values();
            for (PersonSexEnum value : values) {
                if (value.getKey().equals(Integer.valueOf(key))) {
                    return value.getFrench();
                }
            }
        }
        return "";
    }

    public static Integer getKeyByValue(String key, String lang) {
        if ("zh_CN".equals(lang)) {
            PersonSexEnum[] values = PersonSexEnum.values();
            for (PersonSexEnum value : values) {
                if (value.getChina().equals(key)) {
                    return value.getKey();
                }
            }
        }
        if ("fr_DZ".equals(lang)) {
            PersonSexEnum[] values = PersonSexEnum.values();
            for (PersonSexEnum value : values) {
                if (value.getFrench().equals(key)) {
                    return value.getKey();
                }
            }
        }
        return -1;
    }

    public static String[] getByValue(String lang) {
        String[] strs;
        StringJoiner stringJoiner = new StringJoiner(",");
        if ("zh_CN".equals(lang)) {
            PersonSexEnum[] values = PersonSexEnum.values();
            for (PersonSexEnum value : values) {
                stringJoiner.add(value.getChina());
            }
        }
        if ("fr_DZ".equals(lang)) {
            PersonSexEnum[] values = PersonSexEnum.values();
            for (PersonSexEnum value : values) {
                stringJoiner.add(value.getFrench());
            }
        }
        String s = stringJoiner.toString();
        strs = s.split(",");
        return strs;
    }

}
