package com.semptian.enums;

/**
 * <AUTHOR>
 * @desc 白名单生效范围枚举
 * @date 2024/9/14
 */
public enum WhiteListScopeEnum {

    ONLINE_DATA(1, "Online data"),

    PHONE(2, "Phone"),;

    private Integer code;

    private String name;

    WhiteListScopeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getByCode(Integer code) {
        for (WhiteListScopeEnum whiteListTypeEnum : WhiteListScopeEnum.values()) {
            if (whiteListTypeEnum.getCode().equals(code)) {
                return whiteListTypeEnum.getName();
            }
        }
        return null;
    }
}
