package com.semptian.enums;

/**
 * <AUTHOR>
 * @desc 白名单状态枚举
 * @date 2024/9/14
 */
public enum WhiteListStatusEnum {
    DELETE(0, "删除"),

    TO_BE_START(1, "在控中"),

    IN_CONTROL(16, "在控"),

    TO_BE_STOP(4, "停控中"),

    STOP_CONTROL(8, "停控");

    private Integer code;

    private String name;

    WhiteListStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static WhiteListStatusEnum getByCode(Integer code) {
        for (WhiteListStatusEnum whiteListTypeEnum : WhiteListStatusEnum.values()) {
            if (whiteListTypeEnum.getCode().equals(code)) {
                return whiteListTypeEnum;
            }
        }
        return null;
    }
}
