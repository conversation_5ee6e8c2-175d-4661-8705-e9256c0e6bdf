package com.semptian.enums;

/**
 * <AUTHOR>
 * @desc 白名单类型枚举，该名称(name)禁止修改，会影响到治理组获取在控白名单
 * @date 2024/9/14
 */
public enum WhiteListTypeEnum {

    IP(1, "ip"),

    RADIUS(2, "radius"),

    MOBILE_PHONE(3, "mobilePhone"),

    FIXED_PHONE(4, "fixedPhone");

    private Integer code;

    private String name;

    WhiteListTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getByCode(Integer code) {
        for (WhiteListTypeEnum whiteListTypeEnum : WhiteListTypeEnum.values()) {
            if (whiteListTypeEnum.getCode().equals(code)) {
                return whiteListTypeEnum.getName();
            }
        }
        return null;
    }
}
