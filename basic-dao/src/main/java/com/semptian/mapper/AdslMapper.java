package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.AdslEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdslMapper extends BaseMapper<AdslEntity> {

    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_ADSL.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    /**
     * 判断上网账号是否存在
     *
     * @param id            主键ID
     * @param onlineAccount 上网账号
     * @return int
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM TB_ADSL WHERE ONLINE_ACCOUNT = #{onlineAccount}",
            "<if test='id!= null'>",
            "AND ID != #{id}",
            "</if>",
            "</script>"
    })
    int checkOnlineAccountExist(
            @Param("id") Long id,
            @Param("onlineAccount") String onlineAccount
    );

    @Insert({
            "<script>",
            "INSERT INTO TB_ADSL (",
                    "ID,",
                    "ACCOUNT_HOLDER_NAME,",
                    "IDENTITY_CARD_NUMBER,",
                    "ASSEMBLY_PHONE,",
                    "ONLINE_ACCOUNT,",
                    "CITY_CODE,",
                    "INSTALLATION_POSITION,",
                    "CONTACT_NUMBER,",
                    "LONGITUDE,",
                    "LATITUDE,",
                    "ADDRESS,",
                    "ISP_CODE,",
                    "ACCESS_MODE,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_ADSL.nextval,",
                    "ACCOUNT_HOLDER_NAME,",
                    "IDENTITY_CARD_NUMBER,",
                    "ASSEMBLY_PHONE,",
                    "ONLINE_ACCOUNT,",
                    "CITY_CODE,",
                    "INSTALLATION_POSITION,",
                    "CONTACT_NUMBER,",
                    "LONGITUDE,",
                    "LATITUDE,",
                    "ADDRESS,",
                    "ISP_CODE,",
                    "ACCESS_MODE,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
                "SELECT",
                    "#{item.accountHolderName} AS ACCOUNT_HOLDER_NAME,",
                    "#{item.identityCardNumber} AS IDENTITY_CARD_NUMBER,",
                    "#{item.assemblyPhone} AS ASSEMBLY_PHONE,",
                    "#{item.onlineAccount} AS ONLINE_ACCOUNT,",
                    "#{item.cityCode} AS CITY_CODE,",
                    "#{item.installationPosition} AS INSTALLATION_POSITION,",
                    "#{item.contactNumber} AS CONTACT_NUMBER,",
                    "#{item.longitude} AS LONGITUDE,",
                    "#{item.latitude} AS LATITUDE,",
                    "#{item.address} AS ADDRESS,",
                    "to_char(#{item.ispCode}) AS ISP_CODE,",
                    "to_char(#{item.accessMode}) AS ACCESS_MODE,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
                "FROM DUAL",
                "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_ADSL T WHERE T.ONLINE_ACCOUNT = TT.ONLINE_ACCOUNT)",
            "</script>"
    })
    void batchInsert(List<AdslEntity> list);
}
