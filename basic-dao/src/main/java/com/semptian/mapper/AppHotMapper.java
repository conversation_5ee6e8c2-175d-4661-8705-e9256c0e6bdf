package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.AppHotEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:08
 **/
@Mapper
public interface AppHotMapper extends BaseMapper<AppHotEntity> {

    @Select({
            "<script>",
            "SELECT NAME FROM BASE_APP_HOT_TABLE",
            "where 1=1 and",
            "<foreach collection='matchRule' item='item' index='index' separator='or' open='(' close=')'>",
            "<if test='item==0'>",
            "lower_name=#{appName}",
            "</if>",
            "<if test='item==1'>",
            "lower_name like '%'|| #{appName} || '%'",
            "</if>",
            "<if test='item==2'>",
            "instr(#{appName},lower_name) &gt; 0",
            "</if>",
            "</foreach>",
            "</script>"
    })
    List<Map<String, Object>> isHot(@Param("appName") String appName,
                                    @Param("matchRule")List<Integer> matchRule);

}
