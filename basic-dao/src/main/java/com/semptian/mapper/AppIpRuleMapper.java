package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.entity.AppIpRuleEntity;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.apache.ibatis.type.JdbcType.CLOB;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@Mapper
public interface AppIpRuleMapper extends BaseMapper<AppIpRuleEntity> {

    @Select({
            "<script>",
            "select NAME from BASE_APP_RULE_TABLE app where ",
            "exists (select 1 from BASE_APP_RULE_IP_TABLE di where (ip=#{ip} " ,
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or ip=#{abbrIPv6}" ,
            "</if>",
            ") and di.rule_id = app.id)",
            "and source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "AND EXISTS (SELECT 1 FROM BASE_APP_HOT_TABLE ah WHERE ah.LOWER_NAME = lower(app.NAME))",
            "ORDER BY ID ASC",
            "</script>"
    })
    List<String> isHot(@Param("ip") String ip,
                       @Param("abbrIPv6") String abbrIPv6,
                       @Param("queryRange") List<Integer> queryRange);

    @Select({
            "<script>",
            "SELECT  tb.rule_id ruleId, wm_concat ( tb.ip)  ip   FROM",
            "(SELECT tt.rule_id ," ,
            "(case  when tt.port is null then tt.ip",
            "when tt.IP_TYPE=1 then '['||tt.ip||']'||':'||tt.port",
            "else tt.ip||':'||tt.port end) ip",
            "FROM (SELECT ROW_NUMBER() OVER (PARTITION BY RULE_ID ORDER BY id ) r , t.rule_id , t.ip ,t.port,t.ip_type  FROM BASE_APP_RULE_IP_TABLE t " ,
            "where rule_id in ",
            "<foreach collection='ids' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            ") tt  ORDER BY tt.ip) tb group by tb.rule_id" ,
            "</script>"
    }
    )
    @Results({
            @Result(property = "ruleId", column = "ruleId", jdbcType = JdbcType.NUMERIC, javaType = String.class),
            @Result(property = "ip", column = "ip", jdbcType = CLOB, javaType = String.class, typeHandler = org.apache.ibatis.type.ClobTypeHandler.class)
    }
    )
    List<Map<String, Object>> selectTop(@Param("ids") Set<Long> ids,@Param("top") int top);


    @Select({
            "<script>",
            "select * from BASE_APP_RULE_IP_TABLE where rule_id = #{id} order by id desc",
            "</script>"
    })
    List<AppIpRuleEntity> queryIpList(@Param("id") Long id, Page<AppIpRuleEntity> page);


    @Insert({
            "<script>",
            "insert into BASE_APP_RULE_IP_TABLE (ID, RULE_ID, IP, IP_TYPE, PORT, UPDATE_TIME)",
            "SELECT ",
            "SEQ_BASE_APP_RULE_IP_ID.nextval,",
            "ruleId,",
            "ip,",
            "ipType,",
            "case port when -1 then null else port end,",
            "updateTime",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
            "#{item.ruleId} AS ruleId,",
            "#{item.ip} AS ip,",
            "#{item.ipType} AS ipType,",
            "#{item.port} AS port,",
            "#{item.updateTime} AS updateTime",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "</script>"

    })
    int insertList(@Param("list")List<AppIpRuleEntity> list);


    @Select({
            "<script>",
            "select " ,
            "(case when port is null then ip",
            "when IP_TYPE=1 then '['||ip||']'||':'||port",
            "else ip||':'||port end) ipStr,",
            "ip,",
            "port",
            " from BASE_APP_RULE_IP_TABLE where rule_id = #{id} order by id desc",
            "</script>"
    })
    List<Map<String,Object>> queryIpString(@Param("id") String id);


    @Select({
            "<script>",
            "DELETE FROM BASE_APP_RULE_IP_TABLE where ",
            "rule_id = #{id} and ",
            "<foreach collection='needDeleteIps' item='item' index='index' separator='or' open='(' close=')'>",
            "(ip=#{item.ip} " ,
            "<if test='item.port!=null'>",
            "and port=#{item.port}" ,
            "</if>",
            "<if test='item.port==null'>",
            "and port is null" ,
            "</if>",
            ")",
            "</foreach>",
            "</script>"
    })
    void deleteByIdAndIP(@Param("id") String id,@Param("needDeleteIps") List<Map<String, Object>> needDeleteIps);
}
