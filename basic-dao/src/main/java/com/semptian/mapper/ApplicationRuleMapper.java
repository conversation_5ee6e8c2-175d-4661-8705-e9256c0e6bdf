package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.bo.RuleBo;
import com.semptian.entity.ApplicationRuleEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:08
 **/
@Mapper
public interface ApplicationRuleMapper extends BaseMapper<ApplicationRuleEntity> {

    @Select({
            "<script>",
            "SELECT",
            "count(*)",
            "FROM ",
            "BASE_APP_RULE_TABLE",
            "WHERE 1=1",
            "<if test = 'status  != null and status != -1 '>",
            "AND STATUS = #{status}",
            "</if>",
            "<if test = 'maxUpdateTime != null'>",
            "AND UPDATE_TIME &lt;= #{maxUpdateTime}",
            "</if>",
            "AND source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    int queryApplicationRuleCount(@Param("status") Integer status, @Param("queryRange") List<Integer> queryRange, @Param("maxUpdateTime") Long maxUpdateTime);

    @Select({
            "<script>",
            "select",
            "bat.ID , bat.NAME , bat.RULE , bat.DESCRIPTION , ",
            "bat.STATUS, bat.TYPE appType, bat.source_type sourceType ,",
            "bat.USER_ID userId,bat.CREATE_TIME createTime,bat.UPDATE_TIME updateTime",
            "from ",
            "BASE_APP_RULE_TABLE bat",
            "where  1=1 ",
            "<if test = 'status  != null and status != -1 '>",
            " and bat.STATUS = #{status}",
            "</if>",
            "and source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test = 'keyword  != \"\" '>",
            "and (lower(bat.NAME) like   '%'|| #{lowerName} || '%'  or  bat.RULE like '%'|| #{keyword} || '%' or bat.DESCRIPTION like '%'|| #{keyword} || '%'",
            "or exists (select 1 from BASE_APP_RULE_IP_TABLE bdit where bat.id = bdit.rule_id and (lower(ip) like   '%'|| lower(#{keyword}) || '%'",
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or lower(ip)=lower(#{abbrIPv6})",
            "</if>)",
            ")",
            ")",
            "</if>",
            "<if test = 'lowerRuleName  != \"\" '>",
            "and (lower(bat.NAME) like   '%'|| #{lowerRuleName} || '%' )",
            "</if>",
            " order by bat.source_type desc,${orderField} ",
            "<if test = 'orderType  == 0'>",
            "desc",
            "</if>",
            "<if test = 'orderType  == 1'>",
            "asc",
            "</if>",
            "</script>"
    })
    List<ApplicationRuleEntity> queryApplicationRuleListWeb(@Param("keyword") String keyword,
                                                            @Param("lowerName") String lowerName,
                                                            @Param("lowerRuleName") String lowerRuleName,
                                                            @Param("abbrIPv6") String abbrIPv6,
                                                            Page<ApplicationRuleEntity> page,
                                                            @Param("orderField") String orderField,
                                                            @Param("orderType") Integer orderType,
                                                            @Param("status") Integer status,
                                                            @Param("queryRange") List<Integer> queryRange);

    @Select({
            "<script>",
            "SELECT",
            "t1.ID, t1.NAME, t1.RULE, t1.DESCRIPTION,",
            "t1.STATUS, t1.TYPE appType, t1.source_type sourceType,",
            "t1.USER_ID userId, t1.CREATE_TIME createTime, t1.UPDATE_TIME updateTime",
            "FROM",
            "(SELECT",
            "bat.ID, bat.NAME, bat.RULE, bat.DESCRIPTION, ",
            "bat.STATUS, bat.TYPE, bat.source_type,",
            "bat.USER_ID, bat.CREATE_TIME, bat.UPDATE_TIME",
            "FROM ",
            "BASE_APP_RULE_TABLE bat",
            "WHERE 1=1 ",
            "AND source_type IN ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test = 'lowerRuleName  != \"\" '>",
            "AND (lower(bat.NAME) like '%' || #{lowerRuleName} || '%' )",
            "</if>",
            "ORDER BY bat.ID ASC) t1",
            "LEFT JOIN BASE_APP_HOT_TABLE t2 ON lower(t1.NAME) = t2.LOWER_NAME",
            "WHERE t2.NAME IS NULL",
            "</script>"
    })
    List<ApplicationRuleEntity> queryApplicationRuleListExcludeHot(@Param("lowerRuleName") String lowerRuleName,
                                                                   Page<ApplicationRuleEntity> page,
                                                                   @Param("queryRange") List<Integer> queryRange);

    @Select({
            "<script>",
            "select",
            "bat.ID , bat.NAME , bat.RULE , bat.DESCRIPTION , ",
            "bat.STATUS, bat.TYPE , bat.source_type sourceType ,",
            "bat.USER_ID userId,bat.CREATE_TIME createTime,bat.UPDATE_TIME updateTime",
            "from ",
            "BASE_APP_RULE_TABLE bat",
            "where  1=1 ",
            "<if test = 'status  != null and status != -1 '>",
            " and bat.STATUS = #{status}",
            "</if>",
            "and source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test = 'maxUpdateTime != null'>",
            "AND UPDATE_TIME &lt;= #{maxUpdateTime}",
            "</if>",
            "order by ${orderField}",
            "<if test = 'orderType  == 0'>",
            "desc",
            "</if>",
            "<if test = 'orderType  == 1'>",
            "asc",
            "</if>",
            ",ID ASC",
            "</script>"
    })
    List<ApplicationRuleEntity> queryApplicationRuleList(Page<ApplicationRuleEntity> page,
                                                         @Param("orderField") String orderField,
                                                         @Param("orderType") Integer orderType,
                                                         @Param("status") Integer status,
                                                         @Param("queryRange") List<Integer> queryRange,
                                                         @Param("maxUpdateTime") Long maxUpdateTime);

    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_BASE_APP_RULE_ID.NEXTVAL FROM DUAL",
            "</script>"
    })
    @Options(useCache = false)
    Long nextId();


    @Insert({
            "<script>",
            "INSERT INTO BASE_APP_RULE_TABLE (",
            "ID,",
            "NAME,",
            "RULE,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "UPDATE_TIME",
            ")",
            "SELECT ",
            "SEQ_BASE_APP_RULE_ID.nextval,",
            "NAME,",
            "RULE,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
            "#{item.name} AS NAME,",
            "#{item.rule} AS RULE,",
            "#{item.description} AS DESCRIPTION,",
            " 1 AS STATUS,",
            "#{item.type} AS TYPE,",
            " 2 AS SOURCE_TYPE,",
            "#{item.userId} AS USER_ID,",
            "sysdate AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM BASE_APP_RULE_TABLE T WHERE T.NAME = TT.NAME)",
            "</script>"
    })
    void insertBatch(@Param("list") List<Map<String, Object>> list);

    @Insert({
            "<script>",
            "INSERT INTO BASE_APP_RULE_TABLE (",
            "ID,",
            "NAME,",
            "RULE,",
            "BEHAVIOR_NUM_SUM,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "CREATE_TIME,",
            "UPDATE_TIME",
            ")",
            "SELECT ",
            "SEQ_BASE_APP_RULE_ID.nextval,",
            "NAME,",
            "RULE,",
            "BEHAVIOR_NUM_SUM,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "CREATE_TIME,",
            "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='appRuleList' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
            "#{item.name} AS NAME,",
            "#{item.name} AS RULE,",
            "#{item.behaviorNumSum} AS BEHAVIOR_NUM_SUM,",
            "NULL AS DESCRIPTION,",
            "1 AS STATUS,",
            "#{item.type} AS TYPE,",
            "1 AS SOURCE_TYPE,",
            "1 AS USER_ID,",
            "#{updateTime} AS CREATE_TIME,",
            "#{updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM BASE_APP_RULE_TABLE T WHERE T.NAME = TT.NAME)",
            "</script>"
    })
    int insertAutoUpdateAppRule(@Param("appRuleList") List<RuleBo> appRuleList, @Param("updateTime") Long updateTime);


    @Select({
            "<script>",
            "SELECT ID FROM  BASE_APP_RULE_TABLE WHERE SOURCE_TYPE in (0,1)",
            "</script>"
    })
    List<Long> selectInitIds();


    @Select({
            "<script>",
            "SELECT id FROM BASE_APP_RULE_TABLE",
            "where 1=1 and",
            "<foreach collection='matchRule' item='item' index='index' separator='or' open='(' close=')'>",
            "<if test='item==0'>",
            "lower(name)=#{appName}",
            "</if>",
            "<if test='item==1'>",
            "lower(name) like '%'|| #{appName} || '%'",
            "</if>",
            "<if test='item==2'>",
            "instr(#{appName},name) &gt; 0",
            "</if>",
            "</foreach>",
            "and source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<Map<String, Object>> isHot(@Param("appName") String appName,
                                    @Param("matchRule") List<Integer> matchRule,
                                    @Param("queryRange") List<Integer> queryRange);

    @Update({
            "<script>",
            "UPDATE BASE_APP_RULE_TABLE",
            "SET UPDATE_TIME = #{updateTime},",
            " BEHAVIOR_NUM_SUM = #{behaviorNumSum}",
            "WHERE ID =#{id} ",
            "</script>"
    })
    int updateDataTime(@Param("id") Long id, @Param("updateTime") Long updateTime, @Param("behaviorNumSum") Integer behaviorNumSum);
}
