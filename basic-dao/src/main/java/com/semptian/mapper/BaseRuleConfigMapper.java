package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.BaseRuleConfigEntity;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @date 2022-05-20 09:38
 **/
@Mapper
public interface BaseRuleConfigMapper extends BaseMapper<BaseRuleConfigEntity> {

    /**
     * 更新知识库自动更新状态
     *
     * @param ruleType 知识库类型 1-域名知识库，2-应用知识库
     * @param status 自动更新状态 0-关闭，1-开启
     */
    @Update({
            "<script>",
            "UPDATE BASE_RULE_CONFIG_TABLE BRC",
            "SET",
            "BRC.AUTO_UPDATE_STATUS = #{status}, BRC.UPDATE_TIME = #{updateTime}",
            "WHERE BRC.RULE_TYPE = #{ruleType}",
            "</script>"
    })
    int updateAutoUpdateStatus(@Param("ruleType") Integer ruleType, @Param("status") Integer status, @Param("updateTime") Long updateTime);

    /**
     * 更新知识库更新任务执行时间
     *
     * @param ruleType 知识库类型 1-域名知识库，2-应用知识库
     */
    @Update({
            "<script>",
            "UPDATE BASE_RULE_CONFIG_TABLE BRC",
            "SET",
            "BRC.LAST_EXEC_TIME = #{execTime}",
            "WHERE BRC.RULE_TYPE = #{ruleType}",
            "</script>"
    })
    int updateExecTime(@Param("ruleType") Integer ruleType, @Param("execTime") Long execTime);

    @Select({
            "<script>",
            "SELECT ID, RULE_TYPE ruleType, AUTO_UPDATE_STATUS autoUpdateStatus, CREATE_TIME createTime, UPDATE_TIME updateTime, LAST_EXEC_TIME lastExecTime",
            "FROM BASE_RULE_CONFIG_TABLE BRC",
            "WHERE BRC.RULE_TYPE = #{ruleType}",
            "</script>"
    })
    @Options(useCache = false)
    BaseRuleConfigEntity queryAutoUpdateStatus(@Param("ruleType") Integer ruleType);

}
