package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.BaseStationLocationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 基站位置信息库表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-03-20
 */
@Mapper
public interface BaseStationLocationMapper extends BaseMapper<BaseStationLocationEntity> {
    @Select({
            "<script>",
            "SELECT * FROM tb_base_station_location_info WHERE LATITUDE BETWEEN #{downLeftLat} AND #{topRightLat} AND LONGITUDE BETWEEN #{downLeftLon} AND #{topRightLon} ORDER BY station_no limit #{total}",
            "</script>"
    })
    List<BaseStationLocationEntity> queryBaseStationInfoByLocation(@Param("downLeftLat") float downLeftLat, @Param("downLeftLon") float downLeftLon, @Param("topRightLat") float topRightLat, @Param("topRightLon") float topRightLon, @Param("total") Integer total);

    @Select({
            "<script>",
            "SELECT * FROM tb_base_station_location_info WHERE station_no like CONCAT('%',#{keyword},'%') order by station_no desc limit #{size}",
            "</script>"
    })
    List<BaseStationLocationEntity> recommendStationByNumber(@Param("keyword")String keyword,@Param("size")Integer size);

    @Select({
            "<script>",
            "INSERT INTO tb_base_station_location_info(station_no,cgi,latitude,longitude,coverage_radius,station_address,description,cell_name,network_type,grade,network_operator,create_time,modify_time) VALUES",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.stationNo},#{item.cgi},#{item.latitude},#{item.longitude},#{item.coverageRadius},#{item.stationAddress},#{item.description},#{item.cellName},#{item.networkType},#{item.grade},#{item.networkOperator},#{item.createTime},#{item.modifyTime})",
            "</foreach>",
            "</script>"

    })
    void insertBatch(@Param("list") List<BaseStationLocationEntity> batchList);
}
