package com.semptian.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.CallClueEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


/**
 * call线索sql管理处
 *
 * @Author: sk
 * @date: 2020/12/15
 * @params:
 */
public interface CallClueMapper extends BaseMapper<CallClueEntity> {


    /**
     * 修改号码信息
     *
     * @param callClueEntity
     */
    @Update({
            "<script>",
            "UPDATE CLUECALLTABLE CC",
            "SET",
            "CC.ID=#{callClueEntity.id}",
            "WHERE CC.CLUEID = #{callClueEntity.clueId}",
            "</script>"
    })
    int modifyCallNumber(@Param("callClueEntity")CallClueEntity callClueEntity);

}
