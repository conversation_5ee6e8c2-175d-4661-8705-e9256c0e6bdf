package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.ClueIpFilterTableEntity;
import com.semptian.entity.IpDetailModel;
import com.semptian.entity.IpExpireModel;
import com.semptian.entity.IpWhiteMsgModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @author: ZC
 * @date: 2020/12/15 15:00
 */
@Mapper
public interface ClueIpFilterTableMapper extends BaseMapper<ClueIpFilterTableEntity> {

    /**
     * 查询返回
     */
    String SELECT_NAME = "C1.expireStartTime ,C1.expireEndTime ,C1.ipState ,C1.IPADDR ipAddr,C1.IPMASK ipMask,C2.CLUESTATE clueState,C2.UPDATETIME updateTime,C1.CLUEID clueId";
    String SELECT_NAMES = "C1.IPADDR ipAddr,C1.IPMASK ipMask,C2.CLUESTATE clueState,C2.UPDATETIME updateTime,C2.CREATETIME createTime";
    /**
     * 查询表
     */
    String SELECT_FIELDS = "CLUEIPFILTERTABLE c1 ,CLUETABLE c2";


    /**
     *  查询   desc排序
     * @param ipAddr
     * @param startTime
     * @param endTime
     * @param startIndex
     * @param endIndex
     * @return
     */
    @Select({
            "<script>",
            "select TT.* from (",
            "select T.*,ROWNUM AS rowno FROM (",
            "select",
            SELECT_NAME,
            "from ",
            SELECT_FIELDS,
            "where C1.CLUEID = C2.CLUEID ",
            "<if test = 'ipAddr != null and ipAddr != \"\" '>",
            "and c1.IPADDR like   '%'|| #{ipAddr} || '%'",
            "</if>",
            "<if test = 'startTime != null '>",
            "and c2.CREATETIME >= #{startTime}",
            "</if>",
            "<if test = 'endTime != null '>",
            "and c2.CREATETIME <![CDATA[ <= ]]> #{endTime}",
            "</if>",
            "order by ${orderField} ${orderType}, CLUEID DESC",
            ") T WHERE ROWNUM &lt;= #{endIndex} ) TT WHERE rowno &gt; #{startIndex}",
            "</script>"
    })
    List<IpWhiteMsgModel> selectAllIpMsg(@Param("ipAddr") String ipAddr,
                                         @Param("startTime") Long startTime,
                                         @Param("endTime") Long endTime,
                                         @Param("startIndex") Integer startIndex,
                                         @Param("endIndex") Integer endIndex,
                                         @Param("orderType") String orderType,
                                         @Param("orderField")String orderField);



    /**
     * 查询条数   默认desc排序
     *
     * @param ipAddr
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @return
     */
    @Select({
            "<script>",
            "select ",
            SELECT_NAME,
            "from ",
            SELECT_FIELDS,
            "where C1.CLUEID = C2.CLUEID ",
            "<if test = 'ipAddr != null and ipAddr != \"\" '>",
            "and c1.IPADDR like '%'|| #{ipAddr} || '%'",
            "</if>",
            "<if test = 'startTime != null '>",
            "and c2.CREATETIME >= #{startTime}",
            "</if>",
            "<if test = 'endTime != null '>",
            "and c2.CREATETIME <![CDATA[ <= ]]> #{endTime}",
            "</if>",
            "order by c2.UPDATETIME desc",
            "</script>"
    })
    List<IpWhiteMsgModel> selectAllIp(
            @Param(value = "ipAddr") String ipAddr,
            @Param(value = "startTime") Long startTime,
            @Param(value = "endTime") Long endTime
    );

    /**
     * 根据ids 导出
     *
     * @param clueIds
     * @return
     */
    @Select({
            "<script>",
            "select ",
            SELECT_NAME,
            "from ",
            SELECT_FIELDS,
            "where C1.CLUEID = C2.CLUEID ",
            "and c1.IPADDR = c2.CLUENAME",
            "and C1.CLUEID in ",
            "<foreach collection='clueIds' item='CLUEID' separator=',' open='(' close=')'> ",
            "#{CLUEID}",
            "</foreach>",
            "order by c2.UPDATETIME desc",
            "</script>"
    })
    List<IpWhiteMsgModel> selectExport(@Param("clueIds") List<String> clueIds);


    /**
     * 全部查询导出
     *
     * @return
     */
    @Select(
            "SELECT\n" +
                    "\tC1.IPADDR ipAddr,\n" +
                    "\tC1.IPMASK ipMask,\n" +
                    "\tC2.CLUESTATE clueState,\n" +
                    "\tC2.UPDATETIME updateTime\n" +
                    "FROM\n" +
                    "\tCLUEIPFILTERTABLE c1,\n" +
                    "\tCLUETABLE c2\n" +
                    "WHERE\n" +
                    "\tC1.CLUEID = C2.CLUEID\n" +
                    "AND C1.IPADDR = C2.CLUENAME\n" +
                    "ORDER BY\n" +
                    "\tC2.UPDATETIME DESC"
    )
    List<IpWhiteMsgModel> selectAllExport();

    /**
     * 查下看详情
     *
     * @param clueId
     * @return
     */
    @Select("SELECT\n" +
            "\tC1.IPADDR ipAddr,\n" +
            "\tC1.IPMASK ipMask,\n" +
            "\tC2.CLUESTATE clueState,\n" +
            "\tC2.UPDATETIME updateTime,\n" +
            "\tC2.CREATETIME createTime,\n" +
            "\tC1.CLUEID clueId,\n" +
            "\tC1.EXPIRESTARTTIME expireStartTime,\n" +
            "\tC1.EXPIREENDTIME expireEndTime,\n" +
            "\tC1.IPSTATE ipState\n" +
            "FROM\n" +
            "\tCLUEIPFILTERTABLE c1,\n" +
            "\tCLUETABLE c2\n" +
            "WHERE\n" +
            "\tC1.CLUEID = C2.CLUEID\n" +
            "AND C1.CLUEID = #{clueId}")
    IpDetailModel selectDetailMsg(@Param("clueId") Integer clueId);

    /**
     * 编辑ip
     *
     * @param clueId
     * @param ipAddr
     * @param ipMask
     * @return
     */
    @Update("update CLUEIPFILTERTABLE set IPADDR = #{ipAddr},IPMASK = #{ipMask} where CLUEID = #{clueId}")
    int updateCheckIp(@Param("clueId") Integer clueId, @Param("ipAddr") String ipAddr, @Param("ipMask") String ipMask);

    /**
     * 编辑短期ip
     *
     * @param clueId
     * @param ipAddr
     * @param ipMask
     * @return
     */
    @Update("update CLUEIPFILTERTABLE set IPADDR = #{ipAddr},IPMASK = #{ipMask},EXPIRESTARTTIME =#{expireStartTime},EXPIREENDTIME = #{expireEndTime} where CLUEID = #{clueId}")
    int updateShotCheckIp(@Param("clueId") Integer clueId, @Param("ipAddr") String ipAddr, @Param("ipMask") String ipMask);


    /**
     *  扫描ip停控的
     * @return
     */
    @Select("SELECT\n" +
            "\tC1.expireEndTime,\n" +
            "\tC2.CLUESTATE clueState,\n" +
            "\tC1.CLUEID clueId\n" +
            "FROM\n" +
            "\tCLUEIPFILTERTABLE c1,\n" +
            "\tCLUETABLE c2\n" +
            "WHERE\n" +
            "\tC1.CLUEID = C2.CLUEID\n" +
            "AND C1.ipstate = 1\n" +
            "AND C2.CLUESTATE = 16")
    List<IpExpireModel> ipExpireMonitor();

}
