package com.semptian.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.ClueEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;


/**
 * 线索sql管理
 *
 * @Author: sk
 * @date: 2020/12/15
 * @params:
 */
@Mapper
public interface ClueMapper extends BaseMapper<ClueEntity> {

    /**
     * 查询返回
     */

    String SELECT_NAME = "a.clueid id,a.id fullNum, b.CLUEINFO remark , b.cluestate phoneState, b.updatetime updateTime,a.country_code countryCode,a.telephone_num telephoneNum,b.createTime,1 phoneType ";

    String SELECT_EXPORT_NAME = "a.id fullNum, b.cluestate phoneState, b.updatetime updateTime ,1 phoneType ";
    /**
     * 查询表
     */
    String SELECT_FIELDS = "CLUECALLTABLE a,CLUETABLE b";

    /**
     * 查询号码白名单总数
     *
     * @param keyword
     * @return
     */
    @Select({
            "<script>",
            "SELECT sum (cnt) FROM (",
            "select count(1) cnt from ",
            SELECT_FIELDS ,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32",
            "<if test='keyword != null' >",
            " AND LOWER(a.id) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and b.updatetime &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and b.updatetime &lt;= #{endTime}",
            "</if>",
            "union all",
            "SELECT count(1) FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1",
            "<if test='keyword != null' >",
            " AND LOWER(s.phoneNum) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and s.update_Time &gt; #{startTime}",
            "</if>",
            "<if test='endTime != null'>",
            "and s.update_Time &lt;= #{endTime}",
            "</if>",
            ")",
            "</script>"
    })
    int queryWhiteNumberCount(@Param("keyword") String keyword,@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    /**
     * 查询号码白名单列表
     *
     * @param keyword
     * @param startIndex
     * @param endIndex
     * @param orderField
     * @param orderType
     * @return
     */
    @Select({
            "<script>",
            "SELECT TT.*",
            "FROM (",
            "SELECT T.*,ROWNUM AS rowno",
            "FROM (",
            "select ",
            SELECT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",
            "<if test='keyword != null' >",
            " AND LOWER(a.id) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and b.updatetime &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and b.updatetime &lt;= #{endTime}",
            "</if>",
            "union all",
            "SELECT id, s.phoneNum fullNum, remark, s.PHONESTATE phoneState ,s.update_time updateTime ,s.country_code countryCode,s.telephone_num telephoneNum,s.createTime,2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 ",

            "<if test='keyword != null' >",
            " AND LOWER(s.PHONENUM) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and s.update_Time &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and s.update_Time &lt;= #{endTime}",
            "</if>",

            "ORDER BY",
            "<if test='orderField != null' >",
            " ${orderField} ",
            "<if test='orderType != null &amp;&amp; orderType == 1' >",
            " ASC, ",
            "</if>",
            "<if test='orderType != null &amp;&amp; orderType == 0' >",
            " DESC, ",
            "</if>",
            "</if>",
            "id ASC",

            ") T WHERE ROWNUM &lt;= #{endIndex} ",
            ") TT WHERE TT.rowno &gt; #{startIndex}",
            "</script>"
    })
    List<ClueDto> queryWhiteNumberList(
            @Param("keyword") String keyword,
            @Param("startIndex") int startIndex,
            @Param("endIndex") int endIndex,
            @Param("orderField") String orderField,
            @Param("orderType") int orderType,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime

    );

    /**
     * 查询普通号码列表
     * @param keyword
     * @param startIndex
     * @param endIndex
     * @param orderField
     * @param orderType
     * @param startTime
     * @return
     */

    @Select({
            "<script>",
            "SELECT TT.*",
            "FROM (",
            "SELECT T.*,ROWNUM AS rowno",
            "FROM (",
            "select ",
            SELECT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",
            "<if test='keyword != null' >",
            " AND LOWER(a.id) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and b.updatetime &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and b.updatetime &lt;= #{endTime}",
            "</if>",
            "ORDER BY",
            "<if test='orderField != null' >",
            " ${orderField} ",
            "<if test='orderType != null &amp;&amp; orderType == 1' >",
            " ASC, ",
            "</if>",
            "<if test='orderType != null &amp;&amp; orderType == 0' >",
            " DESC, ",
            "</if>",
            "</if>",
            "id ASC",

            ") T WHERE ROWNUM &lt;= #{endIndex} ",
            ") TT WHERE TT.rowno &gt; #{startIndex}",
            "</script>"
    })
    List<ClueDto> queryOrdinaryNumberList(
            @Param("keyword") String keyword,
            @Param("startIndex") int startIndex,
            @Param("endIndex") int endIndex,
            @Param("orderField") String orderField,
            @Param("orderType") int orderType,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime

    );


    /**
     * 查询普通号码总数
     *
     * @param keyword
     * @param startTime
     * @return
     */
    @Select({
            "<script>",
            "select count(1) from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 ",
            "<if test='keyword != null' >",
            " AND LOWER(a.id) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and b.updatetime &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and b.updatetime &lt;= #{endTime}",
            "</if>",
            "</script>"
    })
    int queryOrdinaryNumberCount(@Param("keyword") String keyword, @Param("startTime") Long startTime,@Param("endTime") Long endTime);


    /**
     * 校验号码是否重名
     * @param telephoneNum
     * @param countryCode
     * @param id
     * @return
     */

    @Select({
            "<script>",
            "select count(1) from cluecalltable c ",
            "where TELEPHONE_NUM = #{telephoneNum}  and COUNTRY_CODE = #{countryCode}",
            "<if test='id!= null'>",
            "and ",
            "c.clueId != #{id}",
            "</if>",
            "</script>"
    })
    int checkOrdinaryNumber(@Param("telephoneNum") String telephoneNum, @Param("countryCode") String countryCode,@Param("id") Long id);


    /**
     * 获取线索id的下一个序列L
     * @return
     */
    @Select({
            "<script>",
            "SELECT SEQ_CLUETABLE.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long nextVal();


    /**
     * 查看号码详情
     *
     * @param id
     * @return
     */
    @Select({
            "<script>",

            "select",
            SELECT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100  and a.clueid= #{id}",

            "</script>"
    })
    ClueDto queryOrdinaryNumberDetail(@Param("id")Long id);


    /**
     * 修改状态
     * @param ids
     * @param toStatus
     * @return
     */
    @Update({
            "<script>",
            "UPDATE CLUETABLE CT",
            "SET",
            "CLUESTATE = #{toStatus}",
            "<where>",
            "<if test='ids != null  &amp;&amp; ids.size>0' >",
            "and",
            "clueId in",
            "<foreach collection='ids' item='id' separator=',' open='(' close=')'>",
            "       #{id} ",
            "</foreach>",
            "</if>",

            "<if test=' ids == null or ids.size==0' >",
            "and 1 = 0",
            "</if>",
            "</where>",
            "</script>"
    })
    int updateBatchByStatus(@Param("ids")List<Long> ids,@Param("toStatus") Integer toStatus);





    /**
     * 查询线索集合
     * @param ids
     * @return
     */
    @Select({
            "<script>",
            "select",
            SELECT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",
            "<if test='ids != null  &amp;&amp; ids.size>0' >",
            "and",
            "a.clueId in",
            "<foreach collection='ids' item='id' separator=',' open='(' close=')'>",
            "       #{id} ",
            "</foreach>",
            "</if>",
            "</script>"
    })
    List<ClueDto> selectListByClueIds(@Param("ids")List<Long> ids);



    @Select({
            "<script>",
            "select",
            SELECT_EXPORT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",

            "union all",
            "SELECT s.phoneNum fullNum, s.PHONESTATE phoneState ,s.update_time updateTime ,2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 ",
            "ORDER BY",
            "updateTime DESC",

            "</script>"
    })
    List<ClueDto> selectAllExport();


    @Select({
            "<script>",
            "select",
            SELECT_EXPORT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",
            "ORDER BY",
            "updateTime DESC",

            "</script>"
    })
    List<ClueDto> selectOrdinaryExport();


    @Select({
            "<script>",
            "select",
            SELECT_EXPORT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",
            "<if test='numberIds != null  &amp;&amp; numberIds.size>0' >",
            "and",
            "a.clueId in",
            "<foreach collection='numberIds' item='id' separator=',' open='(' close=')'>",
            "       #{id} ",
            "</foreach>",
            "</if>",
            "ORDER BY",
            "b.updatetime DESC",

            "</script>"
    })
    List<ClueDto> selectOrdinaryByIdsExport(@Param("numberIds") List<Long> numberIds);



    @Select({
            "<script>",
            "select",
            SELECT_EXPORT_NAME,
            "from",
            SELECT_FIELDS,
            "where a.clueid=b.clueid and b.objectid=-100 and b.cluestate != 32 ",
            "<if test='numberIds != null  &amp;&amp; numberIds.size>0' >",
            "and",
            "clueId in",
            "<foreach collection='numberIds' item='idItem' separator=',' open='(' close=')'>",
            "       #{idItem} ",
            "</foreach>",
            "</if>",

            "union all",

            "SELECT  s.phoneNum fullNum, s.PHONESTATE phoneState ,s.update_time updateTime , 2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 ",
            "<if test='specialIds != null  &amp;&amp; specialIds.size>0' >",
            "and",
            "id in",
            "<foreach collection='specialIds' item='idItem' separator=',' open='(' close=')'>",
            "       #{idItem} ",
            "</foreach>",
            "</if>",

            "ORDER BY",
            "updateTime DESC",

            "</script>"
    })
    List<ClueDto> getByIdsExport(@Param("numberIds") List<Long> numberIds,@Param("specialIds")List<Long> specialIds);
}
