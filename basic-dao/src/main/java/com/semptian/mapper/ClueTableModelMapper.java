package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.ClueEntity;
import com.semptian.entity.ClueTableModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @author: ZC
 * @date: 2020/12/17 12:16
 */
public interface ClueTableModelMapper extends BaseMapper<ClueTableModel> {

    /**
     * 修改返回
     */
    String BASE_NAME = "CLUETABLE c1";
    /**
     * 修改表
     */
    String BASE_FIELDS = "c1.CLUESTATE = #{clueState},c1.UPDATE_TIME = #{updateTime}";

    String BASE_INSERT = "(OBJECTID,CREATETIME,CLUESTATE,DATACOUNT,CLUELEVEL,PROCMETHOD,CLUEFROM,UPDATE_TIME,CLUENAME) values(#{clueTableModel.objectId},#{clueTableModel.createTime},#{clueTableModel.clueState},#{clueTableModel.dataCount},#{clueTableModel.clueLevel},#{clueTableModel.procMethod},#{clueTableModel.clueFrom},#{clueTableModel.updateTime},#{clueTableModel.clueName})";


    /**
     * 批量修改
     *
     * @param clueIds
     * @param clueState
     * @param updateTime
     * @return
     */

    @Update({
            "<script>",
            "update ",
            BASE_NAME,
            " set ",
            BASE_FIELDS,
            "where c1.CLUEID in",
            "<foreach collection='clueIds' item='CLUEID' separator=',' open='(' close=')'> ",
            "#{CLUEID}",
            "</foreach>",
            "</script>"
    })
    boolean updateStateByIds(@Param(value = "clueIds") List<Integer> clueIds, @Param(value = "clueState") Integer clueState, @Param(value = "updateTime") long updateTime);


    /**
     * 添加线索表的ip
     *
     * @param clueTableModel
     * @return
     */
    @Insert(
            "insert into CLUETABLE " +
                    "(CLUEID,OBJECTID,CREATETIME,CLUESTATE,DATACOUNT,CLUELEVEL,PROCMETHOD,CLUEFROM,UPDATE_TIME,CLUENAME,CLUETYPE) " +
                    "values(#{clueTableModel.clueId},#{clueTableModel.objectId},#{clueTableModel.createTime},#{clueTableModel.clueState}," +
                    "#{clueTableModel.dataCount},#{clueTableModel.clueLevel},#{clueTableModel.procMethod},#{clueTableModel.clueFrom}," +
                    "#{clueTableModel.updateTime},#{clueTableModel.clueName},#{clueTableModel.clueType}) "

    )
    int insertMsgIpClueTable(@Param("clueTableModel") ClueTableModel clueTableModel);

    /**
     * 修改时间
     *
     * @param updateTime
     * @param clueId
     * @param ipAddr
     * @return
     */
    @Update(
            "update CLUETABLE set UPDATE_TIME = #{updateTime},CLUENAME = #{ipAddr},CLUESTATE=8 where CLUEID = #{clueId} "
    )
    int updateTimeIpWhiteName(@Param("updateTime") long updateTime, @Param("clueId") Integer clueId ,@Param("ipAddr") String ipAddr);


}
