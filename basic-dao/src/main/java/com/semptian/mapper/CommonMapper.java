package com.semptian.mapper;

import com.semptian.dto.common.HomePageInfoDto;
import com.semptian.dto.common.StatisticsDto;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonMapper {

    @Select({
            "<script>",
            "SELECT COUNT(1) as count, 1 as item FROM TB_ADSL",
            "UNION ALL",
            "SELECT COUNT(1) as count, 2 as item FROM TB_BASE_STATION",
            "UNION ALL",
            "SELECT COUNT(1) as count, 3 as item FROM TB_IP_LIBRARY",
            "UNION ALL",
            "SELECT COUNT(1) as count, 4 as item FROM TB_MOBILE_REGISTER",
            "UNION ALL",
            "SELECT COUNT(1) as count, 5 as item FROM TB_IDCARD",
            "UNION ALL",
            "SELECT COUNT(1) as count, 6 as item FROM TB_MOBILE_SECTION",
            "UNION ALL",
            "SELECT COUNT(1) as count, 7 as item FROM TB_COUNTRY_CODE",
            "UNION ALL",
            "SELECT COUNT(1) as count, 8 as item FROM TB_INVALID_TELEPHONE",
            "</script>"
    })
    List<StatisticsDto> statistics();

    @Select({
            "<script>",
            "SELECT ID AS TYPE,DESCRIPTION  FROM TB_HOME_PAGE_INFO",
            "</script>"
    })
    List<HomePageInfoDto> queryInfo();
}
