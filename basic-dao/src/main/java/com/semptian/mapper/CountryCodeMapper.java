package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.CountryCodeEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CountryCodeMapper extends BaseMapper<CountryCodeEntity> {
    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_COUNTRY_CODE.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    /**
     * 判断countryCode是否存在
     *
     * @param id          主键ID
     * @param countryCode 国家码
     * @return int
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM TB_COUNTRY_CODE WHERE COUNTRY_CODE = #{countryCode}",
            "<if test='id!= null'>",
            "AND ID != #{id}",
            "</if>",
            "</script>"
    })
    int checkCountryCodeExist(
            @Param("id") Long id,
            @Param("countryCode") String countryCode
    );

    @Insert({
            "<script>",
            "INSERT INTO TB_COUNTRY_CODE (",
                    "ID,",
                    "COUNTRY_CODE,",
                    "COUNTRY_NAME,",
                    "COUNTRY_EN_NAME,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_COUNTRY_CODE.nextval,",
                    "COUNTRY_CODE,",
                    "COUNTRY_NAME,",
                    "COUNTRY_EN_NAME,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
                    "#{item.countryCode} AS COUNTRY_CODE,",
                    "#{item.countryName} AS COUNTRY_NAME,",
                    "#{item.countryEnName} AS COUNTRY_EN_NAME,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_COUNTRY_CODE T WHERE T.COUNTRY_CODE = TT.COUNTRY_CODE)",
            "</script>"
    })
    void batchInsert(List list);
}
