package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.DictionaryEntity;
import com.semptian.entity.I18nModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author: SunQi
 * @create: 2021/01/04
 * desc:
 **/
@Component
@Mapper
public interface DictionaryMapper extends BaseMapper<DictionaryEntity> {

    /**
     * 根据value值批量查询获取国际化后的值
     *
     * @param type        应用标识
     * @param defaultLang 默认语言
     * @param lang        需要国际化的语言
     * @param values      需要国际化的值
     * @return
     */
    @Select({"<script> " +
            "SELECT dict.value as i18nValue, tmp.value as oriValue FROM `tb_sys_national_dict` dict, ",
            "(SELECT code, value FROM `tb_sys_national_dict`" +
            " WHERE type=#{type}  AND lang=#{defaultLang} AND value IN " ,
            "<foreach collection='values' item='value' separator=',' open='(' close=')'>",
            "#{value}",
            "</foreach>",
            ")  tmp " +
            "WHERE lang = #{lang} AND dict.code = tmp.code" +
            "</script>"})
    List<I18nModel> getIn18nValues(
            @Param("type") String type,
            @Param("defaultLang") String defaultLang,
            @Param("lang") String lang,
            @Param("values") List<String> values);


    /**
     * 根据请求的url获取JSON的解析格式
     *
     * @param url 请求url
     * @return JSON解析格式
     */
    @Select("SELECT parse_format FROM tb_sys_national_conf WHERE url = #{url} ")
    String getParseFormat(@Param("url") String url);

    /**
     * 查询应用分类
     *
     */
    @Select({
            "select app_type from dw_dim.app_dim_info group by app_type"
    })
    List<Map<String,Object>> getAppTypeList();
}
