package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.DomainHotEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-09 19:38
 **/
@Mapper
public interface DomainHotMapper extends BaseMapper<DomainHotEntity> {

    @Select({
            "<script>",
            "SELECT NAME FROM BASE_DOMAIN_HOT_TABLE",
            "where 1=1 and",
            "<foreach collection='matchRule' item='item' index='index' separator='or' open='(' close=')'>",
            "<if test='item==0'>",
            "name=#{domain}",
            "</if>",
            "<if test='item==1'>",
            "name like '%'|| #{domain} || '%'",
            "</if>",
            "<if test='item==2'>",
            "instr(#{domain},name) &gt; 0",
            "</if>",
            "</foreach>",
            "</script>"
    })
    List<Map<String, Object>> isHot(@Param("domain") String domain,
                                    @Param("matchRule")List<Integer> matchRule);

}
