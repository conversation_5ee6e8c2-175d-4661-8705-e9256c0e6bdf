package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.entity.DomainIpRuleEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@Mapper
public interface DomainIpRuleMapper extends BaseMapper<DomainIpRuleEntity> {


    @Select({
            "<script>",
            "select NAME from BASE_DOMAIN_RULE_TABLE domain where ",
            "exists (select 1 from BASE_DOMAIN_RULE_IP_TABLE di where (ip=#{ip} " ,
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or ip=#{abbrIPv6}" ,
            "</if>",
            ") " ,
            "and di.rule_id = domain.id)",
            "and source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "AND EXISTS (SELECT 1 FROM BASE_DOMAIN_HOT_TABLE ah WHERE ah.NAME = domain.NAME)",
            "ORDER BY ID ASC",
            "</script>"
    })
    List<String> isHot(@Param("ip") String ip,
                       @Param("abbrIPv6") String abbrIPv6,
                       @Param("queryRange") List<Integer> queryRange);

    @Select({
            "<script>",
            "SELECT NAME FROM BASE_DOMAIN_HOT_TABLE WHERE ",
            "NAME IN ",
            "<foreach collection='domainList' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "ORDER BY NAME ASC",
            "</script>"
    })
    List<String> hotFilter(@Param("domainList") List<String> domainList);

    //where tt.r &lt; #{top}+1
    @Select({
            "<script>",
            "SELECT  tb.rule_id ruleId, LISTAGG(tb.ip, ',') WITHIN GROUP(ORDER BY tb.ip) ip FROM",
            "(SELECT tt.rule_id ,tt.ip FROM (SELECT ROW_NUMBER() OVER (PARTITION BY RULE_ID ORDER BY ip ) r , t.rule_id , t.ip   FROM BASE_DOMAIN_RULE_IP_TABLE t " ,
            "where rule_id in ",
            "<foreach collection='ids' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            ") tt where r &lt;= #{top}) tb group by tb.rule_id" ,
            "</script>"
    })
    List<Map<String,Object>> selectTop(@Param("ids") Set<String> ids, @Param("top")int top);


    @Select({
            "select * from BASE_DOMAIN_RULE_IP_TABLE where rule_id = #{id} order by id desc"
    })
    List<DomainIpRuleEntity> queryIpList(@Param("id") String id, Page<DomainIpRuleEntity> page);

    @Select({
            "select * from BASE_DOMAIN_RULE_IP_TABLE where rule_id = #{id} order by ip asc"
    })
    List<DomainIpRuleEntity> nweQueryIpList(@Param("id") Long id, Page<DomainIpRuleEntity> page);


    @Insert({
            "<script>",
            "insert into BASE_DOMAIN_RULE_IP_TABLE (ID, RULE_ID, IP, IP_TYPE, PORT, UPDATE_TIME)",
            "SELECT ",
            "SEQ_BASE_DOMAIN_RULE_IP_ID.nextval,",
            "ruleId,",
            "ip,",
            "ipType,",
            "port,",
            "updateTime",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
            "#{item.ruleId} AS ruleId,",
            "#{item.ip} AS ip,",
            "#{item.ipType} AS ipType,",
            "#{item.port} AS port,",
            "#{item.updateTime} AS updateTime",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "</script>"

    })
    int insertList(@Param("list") List<DomainIpRuleEntity> list);
}
