package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.bo.DomainBo;
import com.semptian.entity.DomainRuleEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-09 19:38
 **/
@Mapper
public interface DomainRuleMapper extends BaseMapper<DomainRuleEntity> {

    @Select({
            "<script>",
            "SELECT",
            "count(*)",
            "FROM ",
            "BASE_DOMAIN_RULE_TABLE",
            "WHERE 1=1 ",
            "<if test = 'status  != null and status != -1 '>",
            "AND STATUS = #{status}",
            "</if>",
            "<if test = 'domainLevel != null'>",
            "AND DOMAIN_LEVEL = #{domainLevel}",
            "</if>",
            "<if test = 'maxUpdateTime != null'>",
            "AND UPDATE_TIME &lt;= #{maxUpdateTime}",
            "</if>",
            "AND source_type IN ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    int queryDomainRuleCount(@Param("status") Integer status, @Param("queryRange") List<Integer> queryRange, @Param("domainLevel") Integer domainLevel, @Param("maxUpdateTime") Long maxUpdateTime);


    @Select({
            "<script>",
            "select",
            "ID , NAME , RULE , DESCRIPTION , STATUS, DOMAIN_LEVEL type , SOURCE_TYPE sourceType ,USER_ID userId,CREATE_TIME createTime,UPDATE_TIME updateTime",
            "from ",
            "BASE_DOMAIN_RULE_TABLE bdt",
            "where  1=1 ",
            "<if test = 'status  != null and status != -1 '>",
            " and bdt.STATUS = #{status}",
            "</if>",
            "and bdt.source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test = 'keyword  != \"\" '>",
            "and (lower(bdt.NAME) like   '%'|| #{lowerName} || '%'  or  bdt.RULE like '%'|| #{keyword} || '%' or bdt.DESCRIPTION like '%'|| #{keyword} || '%'",
            "<if test='domains != null and domains.size() > 0 '>",
            "or exists (",
            "select name from",
            "<foreach collection='domains' item='item' open='(' close=')' separator='UNION ALL'>",
            "select #{item} as name from dual",
            "</foreach>",
            "bdit where bdit.name = bdt.name",
            ")",
            "</if>",
            ")",
            "</if>",
            "<if test = 'lowerRuleName  != \"\" '>",
            "and (lower(bdt.NAME) like   '%'|| #{lowerRuleName} || '%' )",
            "</if>",
            " order by bdt.source_type desc,${orderField} ",
            "<if test = 'orderType  == 0'>",
            "desc",
            "</if>",
            "<if test = 'orderType  == 1'>",
            "asc",
            "</if>",
            "</script>"
    })
    List<DomainRuleEntity> queryDomainRuleListWeb(@Param("keyword") String keyword,
                                                  @Param("lowerName") String lowerName,
                                                  @Param("lowerRuleName") String lowerRuleName,
                                                  @Param("domains") List<String> domains,
                                                  @Param("status") Integer status,
                                                  Page<DomainRuleEntity> page,
                                                  @Param("orderField") String orderField,
                                                  @Param("orderType") Integer orderType,
                                                  @Param("queryRange") List<Integer> queryRange);


    @Select({
            "<script>",
            "SELECT",
            "t1.ID, t1.NAME, t1.RULE, t1.DESCRIPTION, t1.STATUS, t1.DOMAIN_LEVEL type, t1.SOURCE_TYPE sourceType, t1.USER_ID userId, t1.CREATE_TIME createTime, t1.UPDATE_TIME updateTime",
            "FROM ",
            "(SELECT",
            "ID, NAME, RULE, DESCRIPTION, STATUS, DOMAIN_LEVEL, SOURCE_TYPE, USER_ID, CREATE_TIME, UPDATE_TIME",
            "FROM ",
            "BASE_DOMAIN_RULE_TABLE bdt",
            "WHERE 1=1 ",
            "AND bdt.source_type IN ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test = 'lowerRuleName  != \"\" '>",
            "AND (lower(bdt.NAME) like '%' || #{lowerRuleName} || '%' )",
            "</if>",
            "ORDER BY bdt.ID asc) t1",
            "LEFT JOIN BASE_DOMAIN_HOT_TABLE t2 ON lower(t1.NAME) = lower(t2.NAME)",
            "WHERE t2.NAME IS NULL",
            "</script>"
    })
    List<DomainRuleEntity> queryDomainRuleListExcludeHot(@Param("lowerRuleName") String lowerRuleName,
                                                         Page<DomainRuleEntity> page,
                                                         @Param("queryRange") List<Integer> queryRange);

    @Select({
            "<script>",
            "select",
            "ID , NAME , RULE , DESCRIPTION , STATUS, DOMAIN_LEVEL type , SOURCE_TYPE sourceType ,USER_ID userId,CREATE_TIME createTime,UPDATE_TIME updateTime",
            "from ",
            "BASE_DOMAIN_RULE_TABLE bdt",
            "where 1=1 ",
            "<if test='domainList != null and domainList.size() > 0 '>",
            "and bdt.name in ",
            "<foreach collection='domainList' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            " order by  bdt.source_type desc, ${orderField}",
            "<if test = 'orderType  == 0'>",
            "desc",
            "</if>",
            "<if test = 'orderType  == 1'>",
            "asc",
            "</if>",
            "</script>"
    })
    List<DomainRuleEntity> selectDomainByNames(@Param("domainList") List<String> domainList, @Param("orderField") String orderField,
                                               @Param("orderType") Integer orderType);

    @Select({
            "<script>",
            "select",
            "ID , NAME , RULE , DESCRIPTION , STATUS, TYPE , SOURCE_TYPE sourceType ,USER_ID userId,CREATE_TIME createTime,UPDATE_TIME updateTime",
            "from ",
            "BASE_DOMAIN_RULE_TABLE bdt",
            "where  1=1 ",
            "<if test = 'status  != null and status != -1 '>",
            " and bdt.STATUS = #{status}",
            "</if>",
            "and bdt.source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test = 'domainLevel != null'>",
            "and DOMAIN_LEVEL = #{domainLevel}",
            "</if>",
            "<if test = 'maxUpdateTime != null'>",
            "AND UPDATE_TIME &lt;= #{maxUpdateTime}",
            "</if>",
            "order by ${orderField}",
            "<if test = 'orderType  == 0'>",
            "desc",
            "</if>",
            "<if test = 'orderType  == 1'>",
            "asc",
            "</if>",
            ",ID ASC",
            "</script>"
    })
    List<DomainRuleEntity> queryDomainRuleList(@Param("status") Integer status,
                                               Page<DomainRuleEntity> page,
                                               @Param("orderField") String orderField,
                                               @Param("orderType") Integer orderType,
                                               @Param("queryRange") List<Integer> queryRange,
                                               @Param("domainLevel") Integer domainLevel,
                                               @Param("maxUpdateTime") Long maxUpdateTime);

    @Insert({
            "<script>",
            "INSERT INTO BASE_DOMAIN_RULE_TABLE (",
            "ID,",
            "NAME,",
            "RULE,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "CREATE_TIME,",
            "UPDATE_TIME,",
            "DOMAIN_LEVEL",
            ")",
            "SELECT ",
            "SEQ_BASE_DOMAIN_RULE_ID.nextval,",
            "NAME,",
            "RULE,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "CREATE_TIME,",
            "UPDATE_TIME,",
            "DOMAIN_LEVEL",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
            "#{item.name} AS NAME,",
            "#{item.rule} AS RULE,",
            "#{item.description} AS DESCRIPTION,",
            " 1 AS STATUS,",
            "#{item.type} AS TYPE,",
            " 2 AS SOURCE_TYPE,",
            "#{item.userId} AS USER_ID,",
            "#{item.updateTime} AS UPDATE_TIME,",
            "#{item.createTime} AS CREATE_TIME,",
            "#{item.level} AS DOMAIN_LEVEL",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM BASE_DOMAIN_RULE_TABLE T WHERE T.NAME = TT.NAME)",
            "</script>"
    })
    int insertBatch(@Param("list") List<Map<String, Object>> list);


    @Select({
            "<script>",
            "SELECT id FROM BASE_DOMAIN_RULE_TABLE",
            "where 1=1 and",
            "<foreach collection='matchRule' item='item' index='index' separator='or' open='(' close=')'>",
            "<if test='item==0'>",
            "lower(name)=#{domain}",
            "</if>",
            "<if test='item==1'>",
            "lower(name) like '%'|| #{domain} || '%'",
            "</if>",
            "<if test='item==2'>",
            "instr(#{domain},name) &gt; 0",
            "</if>",
            "</foreach>",
            " and source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<Map<String, Object>> isHot(@Param("domain") String domain,
                                    @Param("matchRule") List<Integer> matchRule,
                                    @Param("queryRange") List<Integer> queryRange);

    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_BASE_DOMAIN_RULE_ID.NEXTVAL FROM DUAL",
            "</script>"
    })
    @Options(useCache = false)
    Long nextId();

    @Select({
            "<script>",
            "SELECT ID FROM  BASE_DOMAIN_RULE_TABLE WHERE SOURCE_TYPE =2 and ID in",
            "<if test='ids != null and ids.size() > 0 '>",
            "<foreach collection='ids' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "</script>"
    })
    List<Long> selectInitId(@Param("ids") List<Long> ids);

    @Insert({
            "<script>",
            "INSERT INTO BASE_DOMAIN_RULE_TABLE (",
            "ID,",
            "NAME,",
            "RULE,",
            "BEHAVIOR_NUM_SUM,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "CREATE_TIME,",
            "UPDATE_TIME,",
            "DOMAIN_LEVEL",
            ")",
            "SELECT ",
            "SEQ_BASE_DOMAIN_RULE_ID.nextval,",
            "NAME,",
            "RULE,",
            "BEHAVIOR_NUM_SUM,",
            "DESCRIPTION,",
            "STATUS,",
            "TYPE,",
            "SOURCE_TYPE,",
            "USER_ID,",
            "CREATE_TIME,",
            "UPDATE_TIME,",
            "DOMAIN_LEVEL",
            "FROM",
            "(",
            "<foreach collection='domainList' item='domainBo' index='index' separator='UNION ALL'>",
            "SELECT",
            "#{domainBo.domain} AS NAME,",
            "#{domainBo.domain} AS RULE,",
            "#{domainBo.behaviorNumSum} AS BEHAVIOR_NUM_SUM,",
            "NULL AS DESCRIPTION,",
            "1 AS STATUS,",
            "'visit web site' AS TYPE,",
            "1 AS SOURCE_TYPE,",
            "1 AS USER_ID,",
            "#{updateTime} AS CREATE_TIME,",
            "#{updateTime} AS UPDATE_TIME,",
            "#{domainLevel} AS DOMAIN_LEVEL",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM BASE_DOMAIN_RULE_TABLE T WHERE T.NAME = TT.NAME)",
            "</script>"
    })
    int insertAutoUpdateDomainRule(@Param("domainList") List<DomainBo> domainList, @Param("domainLevel") Integer domainLevel, @Param("updateTime") Long updateTime);

    @Update({
            "<script>",
            "UPDATE BASE_DOMAIN_RULE_TABLE",
            "SET UPDATE_TIME = #{updateTime},",
            "BEHAVIOR_NUM_SUM = #{behaviorNumSum}",
            "WHERE ID = #{id}",
            "</script>"
    })
    int updateDataTime(@Param("id") Long id, @Param("updateTime") Long updateTime, @Param("behaviorNumSum") Integer behaviorNumSum);

    @Select({
            "<script>",
            "select distinct name from BASE_DOMAIN_RULE_TABLE d1  INNER JOIN",
            "(select DISTINCT rule_id from BASE_DOMAIN_RULE_IP_TABLE  where  (lower(ip) like  '%'|| lower(#{keyword}) || '%'",
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or lower(ip)=lower(#{abbrIPv6})",
            "</if> )) d2",
            "ON d1.id=d2.rule_id",
            "order by name asc",
            "</script>"
    })
    List<String> selectDoMainListByKeyword(@Param("keyword") String keyword, @Param("abbrIPv6") String abbrIPv6, Page<String> namePage);


    @Select({
            "<script>",
            "select count(distinct name) from BASE_DOMAIN_RULE_TABLE d1  INNER JOIN",
            "(select DISTINCT rule_id from BASE_DOMAIN_RULE_IP_TABLE  where  (lower(ip) like  '%'|| lower(#{keyword}) || '%'",
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or lower(ip)=lower(#{abbrIPv6})",
            "</if> )) d2",
            "ON d1.id=d2.rule_id",
            "</script>"
    })
    Integer selectDoMainListByKeywordCount(@Param("keyword") String keyword, @Param("abbrIPv6") String abbrIPv6);


    @Select({
            "<script>",
            "select",
            "ID , NAME , RULE , DESCRIPTION , STATUS, DOMAIN_LEVEL type , SOURCE_TYPE sourceType ,USER_ID userId,CREATE_TIME createTime,UPDATE_TIME updateTime",
            "from ",
            "BASE_DOMAIN_RULE_TABLE bdt",
            "where  1=1 ",
            "<if test='queryRange != null and queryRange.size() > 0 '>",
            "and bdt.source_type in ",
            "<foreach collection='queryRange' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test = 'keyword  != \"\" '>",
            "and (lower(bdt.NAME) like   '%'|| #{lowerName} || '%'  or  bdt.RULE like '%'|| #{keyword} || '%' or bdt.DESCRIPTION like '%'|| #{keyword} || '%'",
            ")",
            "</if>",
            " order by bdt.source_type desc,${orderField} ",
            "<if test = 'orderType  == 0'>",
            "desc",
            "</if>",
            "<if test = 'orderType  == 1'>",
            "asc",
            "</if>",
            "</script>"
    })
    List<DomainRuleEntity> queryDomainRuleListWebV2(@Param("keyword") String keyword, @Param("lowerName") String lowerName, Page<DomainRuleEntity> page, @Param("orderField") String orderField, @Param("orderType") Integer orderType, @Param("queryRange") List<Integer> queryRange);
}
