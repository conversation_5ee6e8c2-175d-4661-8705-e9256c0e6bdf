package com.semptian.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.bo.DomainBo;
import com.semptian.bo.IpRuleBo;
import com.semptian.bo.RuleBo;
import com.semptian.entity.DomainIpRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Clickhouse DwsBehaviorDetail Mapper
 *
 * <AUTHOR>
 * @date 2022-05-25 16:27
 **/
@Mapper
public interface DwsBehaviorDetailMapper {

    @Select({
            "<script>",
            "SELECT COUNT(1) AS app_num FROM(",
            "SELECT  app_name,app_action",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "AND app_name != '' AND app_type != ''",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND position(app_name,'Other') = 0 GROUP BY app_name,app_action)",
            "</script>"
    })
    Integer queryAppNum(@Param("days") Integer days, @Param("onlyHttps") Boolean onlyHttps, @Param("dataTypeRange") List<Integer> dataTypeRange);

    @Select({
            "<script>",
            "SELECT IF(app_action='',app_name,concat(app_name,'[',app_action,']')) AS name, any(app_type) AS type ,sum(behavior_num) as behaviorNumSum",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "AND app_name != '' AND app_type != ''",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND position(app_name,'Other') = 0",
            "GROUP BY name",
            "ORDER BY name ASC",
            "LIMIT #{startNum},#{pageNum}",
            "</script>"
    })
    List<RuleBo> queryApp(@Param("days") Integer days, @Param("onlyHttps") Boolean onlyHttps, @Param("dataTypeRange") List<Integer> dataTypeRange, @Param("startNum") Integer startNum, @Param("pageNum") Integer pageNum);

    @Select({
            "<script>",
            "SELECT",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{tertiaryDomainExtractRule}) tertiary_domain",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{tertiaryDomainExtractRule}) tertiary_domain",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{tertiaryDomainExtractRule}) tertiary_domain",
            "</if>",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND tertiary_domain != '' AND strdst_ip != '' AND tertiary_domain != extract(tertiary_domain,#{secondaryDomainExtractRule})",
            "GROUP BY tertiary_domain",
            "ORDER BY sum(behavior_num) DESC, tertiary_domain ASC",
            "LIMIT #{limitNum}",
            "</script>"
    })
    List<String> queryHotTertiaryDomain(@Param("days") Integer days, @Param("onlyHttps") Boolean onlyHttps, @Param("tertiaryDomainExtractRule") String tertiaryDomainExtractRule, @Param("secondaryDomainExtractRule") String secondaryDomainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange);

    @Select({
            "<script>",
            "SELECT",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{secondaryDomainExtractRule}) secondary_domain",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{secondaryDomainExtractRule}) secondary_domain",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{secondaryDomainExtractRule}) secondary_domain",
            "</if>",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND secondary_domain != ''",
            "GROUP BY secondary_domain",
            "ORDER BY sum(behavior_num) DESC, secondary_domain ASC",
            "LIMIT #{limitNum}",
            "</script>"
    })
    List<String> queryHotSecondaryDomain(@Param("days") Integer days, @Param("onlyHttps") Boolean onlyHttps, @Param("secondaryDomainExtractRule") String secondaryDomainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange);

    @Select({
            "<script>",
            "SELECT t.name AS ruleName, t.strdst_ip AS ip, t.ip_type AS ipType, t.dst_port AS port",
            "FROM",
            "(SELECT IF(app_action='',app_name,concat(app_name,'[',app_action,']')) AS name, strdst_ip, ip_type, dst_port, sum(behavior_num) behavior_num_sum",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "AND app_type != ''",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND name IN",
            "<foreach collection ='appNameList' item = 'appName' separator=',' open='(' close=')'>",
            "#{appName}",
            "</foreach>",
            "GROUP BY name, strdst_ip, ip_type, dst_port) t",
            "ORDER BY t.behavior_num_sum desc",
            "LIMIT #{limitNum} BY t.name",
            "</script>"
    })
    List<IpRuleBo> queryHotIpForApp(@Param("days") Integer days, @Param("appNameList") List<String> appNameList, @Param("onlyHttps") Boolean onlyHttps, @Param("limitNum") Integer limitNum, @Param("dataTypeRange") List<Integer> dataTypeRange);

    @Select({
            "<script>",
            "SELECT t.extract_domain AS ruleName, t.strdst_ip AS ip, t.ip_type AS ipType, t.dst_port AS port",
            "FROM",
            "(SELECT",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{domainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{domainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{domainExtractRule}) extract_domain,",
            "</if>",
            "strdst_ip, ip_type, dst_port, sum(behavior_num) behavior_num_sum",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND extract_domain IN",
            "<foreach collection ='domainList' item = 'domain' separator=',' open='(' close=')'>",
            "#{domain}",
            "</foreach>",
            "GROUP BY extract_domain, strdst_ip, ip_type, dst_port) t",
            "ORDER BY t.behavior_num_sum desc",
            "LIMIT #{limitNum} BY t.extract_domain",
            "</script>"
    })
    List<IpRuleBo> queryHotIpForDomain(@Param("days") Integer days, @Param("domainList") List<String> domainList, @Param("onlyHttps") Boolean onlyHttps, @Param("domainExtractRule") String domainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange);

    @Select({
            "<script>",
            "insert into dws.dws_base_domain_rule",
            "SELECT",
            "toYYYYMMDD(now()) as insertDay,",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{tertiaryDomainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{tertiaryDomainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{tertiaryDomainExtractRule}) extract_domain,",
            "</if>",
            "#{domainLevel} as domain_level,",
            "sum(behavior_num) as behavior_num",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND extract_domain != '' AND strdst_ip != '' AND extract_domain != extract(extract_domain,#{secondaryDomainExtractRule})",
            "GROUP BY extract_domain",
            "ORDER BY behavior_num DESC, extract_domain ASC",
            "LIMIT #{limitNum}",
            "</script>"
    })
    void insertHotTertiaryDomain(@Param("days") Integer days, @Param("onlyHttps") Boolean onlyHttps, @Param("tertiaryDomainExtractRule") String tertiaryDomainExtractRule, @Param("secondaryDomainExtractRule") String secondaryDomainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange, @Param("domainLevel") int domainLevel);


    @Select({
            "<script>",
            "insert into dws.dws_base_domain_rule",
            "SELECT",
            "toYYYYMMDD(now()) as insertDay,",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{secondaryDomainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{secondaryDomainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{secondaryDomainExtractRule}) extract_domain,",
            "</if>",
            "#{domainLevel} as domain_level,",
            "sum(behavior_num) as behavior_num",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND extract_domain != ''",
            "GROUP BY extract_domain",
            "ORDER BY behavior_num DESC, extract_domain ASC",
            "LIMIT #{limitNum}",
            "</script>"
    })
    void insertHotSecondaryDomain(@Param("days") Integer days, @Param("onlyHttps") Boolean onlyHttps, @Param("secondaryDomainExtractRule") String secondaryDomainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange, @Param("domainLevel") int domainLevel);

        @Select({
                "<script>",
                "insert into dws.dws_base_domain_rule_ip",
                "SELECT toYYYYMMDD(now()) AS insert_day, t.extract_domain AS domain, t.strdst_ip AS ip, t.ip_type AS ip_type, t.dst_port AS port,t.behavior_num_sum AS behavior_num,#{insertTime}",
                "FROM",
                "(SELECT",
                "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
                "extract(sub_domain,#{domainExtractRule}) extract_domain,",
                "</if>",
                "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
                "extract(domain,#{domainExtractRule}) extract_domain,",
                "</if>",
                "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
                "extract(dns,#{domainExtractRule}) extract_domain,",
                "</if>",
                "strdst_ip, ip_type, dst_port, sum(behavior_num) behavior_num_sum",
                "FROM dws.dws_nf_behavior_detail_all",
                "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
                "AND data_type IN ",
                "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
                "#{dataType}",
                "</foreach>",
                "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
                "AND sub_domain != ''",
                "</if>",
                "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
                "AND domain != ''",
                "</if>",
                "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
                "AND dns != ''",
                "</if>",
                "<if test = 'onlyHttps != null and onlyHttps == true'>",
                "AND dst_port = 443",
                "</if>",
                "AND strdst_ip != '' " ,
                "GROUP BY extract_domain, strdst_ip, ip_type, dst_port) t",
                " GLOBAL JOIN (select domain from dws.dws_base_domain_rule_all where insert_day=toYYYYMMDD(now()) and domain_level = #{domainLevel}) d on d.domain = t.extract_domain",
                "ORDER BY t.behavior_num_sum desc",
                "LIMIT #{limitNum} BY t.extract_domain",
                "</script>"
        })
        void insertHotIpForDomain(@Param("days") Integer days, @Param("domainLevel") int domainLevel, @Param("onlyHttps") Boolean onlyHttps, @Param("domainExtractRule") String domainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange,@Param("insertTime") Long insertTime);
/*    @Select({
            "<script>",
            "insert into dws.dws_base_domain_rule_ip_all",
            "SELECT toYYYYMMDD(now()) AS insert_day, t.extract_domain AS domain, t.strdst_ip AS ip, t.ip_type AS ip_type, t.dst_port AS port,t.behavior_num_sum AS behavior_num, toUnixTimestamp(now()) AS insert_time",
            "FROM",
            "(SELECT",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{domainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{domainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{domainExtractRule}) extract_domain,",
            "</if>",
            "strdst_ip, ip_type, dst_port, sum(behavior_num) behavior_num_sum",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' ",
            "and extract_domain GLOBAL in (select domain from dws.dws_base_domain_rule_all where insert_day = toYYYYMMDD(now()) and domain_level = #{domainLevel})",
            "GROUP BY extract_domain, strdst_ip, ip_type, dst_port) t",
            "ORDER BY t.behavior_num_sum desc",
            "LIMIT #{limitNum} BY t.extract_domain",
            "</script>"
    })
    void insertHotIpForDomain(@Param("days") Integer days, @Param("domainLevel") int domainLevel, @Param("onlyHttps") Boolean onlyHttps, @Param("domainExtractRule") String domainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange);*/

    @Select({"select",
            "count(DISTINCT bdri.`domain`) num",
            "from",
            "(",
            "select",
            "`domain`",
            "from",
            "dws.dws_base_domain_rule_ip_all",
            "where",
            "insert_day = toYYYYMMDD(now())) bdri GLOBAL",
            "JOIN (",
            "select",
            "`domain`",
            "from",
            "dws.dws_base_domain_rule_all",
            "where",
            "insert_day = toYYYYMMDD(now())",
            "and domain_level = #{domainLevel}) bdr on",
            "bdr.`domain` = bdri.`domain`"})
    long selectSaveDomainCount(@Param("domainLevel") int domainLevel);

    @Select({"select domain as domain,SUM(behavior_num) as behaviorNumSum from dws.dws_base_domain_rule_all where insert_day=toYYYYMMDD(now()) and domain_level=#{domainLevel} GROUP BY domain ORDER BY behaviorNumSum desc,`domain` ASC limit #{onPage},#{size}"})
    List<DomainBo> selectSaveDomainList(@Param("domainLevel") int domainLevel, @Param("onPage") int onPage, @Param("size") int size);

    @Select({
            "<script>",
            "insert into dws.dws_base_domain_rule_ip",
            "SELECT toYYYYMMDD(now()) AS insert_day, t.extract_domain AS domain, t.strdst_ip AS ip, t.ip_type AS ip_type, t.dst_port AS port,t.behavior_num_sum AS behavior_num, #{insertTime}",
            "FROM",
            "(SELECT",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "extract(sub_domain,#{domainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "extract(domain,#{domainExtractRule}) extract_domain,",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "extract(dns,#{domainExtractRule}) extract_domain,",
            "</if>",
            "strdst_ip, ip_type, dst_port, sum(behavior_num) behavior_num_sum",
            "FROM dws.dws_nf_behavior_detail_all",
            "PREWHERE insert_day BETWEEN toDate(subtractDays(now(),#{days})) AND toDate(subtractDays(now(),1))",
            "AND data_type IN ",
            "<foreach collection ='dataTypeRange' item = 'dataType' separator=',' open='(' close=')'>",
            "#{dataType}",
            "</foreach>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"sub_domain\"'>",
            "AND sub_domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"domain\"'>",
            "AND domain != ''",
            "</if>",
            "<if test = 'domainExtractSource != null and domainExtractSource == \"dns\"'>",
            "AND dns != ''",
            "</if>",
            "<if test = 'onlyHttps != null and onlyHttps == true'>",
            "AND dst_port = 443",
            "</if>",
            "AND strdst_ip != '' AND extract_domain IN",
            "<foreach collection ='domainList' item = 'domain' separator=',' open='(' close=')'>",
            "#{domain}",
            "</foreach>",
            "GROUP BY extract_domain, strdst_ip, ip_type, dst_port) t",
            "ORDER BY t.behavior_num_sum desc",
            "LIMIT #{limitNum} BY t.extract_domain",
            "</script>"
    })
    void insertHotIpForDomainByDomainList(@Param("days") Integer days, @Param("domainList") List<String> domainList, @Param("onlyHttps") Boolean onlyHttps, @Param("domainExtractRule") String domainExtractRule, @Param("limitNum") Integer limitNum, @Param("domainExtractSource") String domainExtractSource, @Param("dataTypeRange") List<Integer> dataTypeRange, @Param("insertTime") Long insertTime);


    /**
     * ck删除分区
     *
     * @param insertDay
     * @param tableName
     */
    @Select({
            "alter table ${tableName}  on cluster warehouse_cluster drop partition (#{insertDay})"
    })
    void deleteCkPartitionByCaptureDay(
            @Param("insertDay") Integer insertDay,
            @Param("tableName") String tableName);

    @Select({"select count(1) from dws.dws_base_domain_rule_ip_all where insert_day=#{insertDay}"})
    long selectTodayDomainIpCount(@Param("insertDay") Integer insertDay);

    @Select({
            "<script>",
            "select DISTINCT domain from dws.dws_base_domain_rule_ip_all  where  (lower(ip) like concat('%',lower(#{keyword}),'%')",
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or lower(ip)=lower(#{abbrIPv6})",
            "</if> )",
            "order by domain asc",
            "limit #{onPage},#{pageSize}",
            "</script>"
    })
    List<String> selectDoMainListByKeyword(@Param("keyword") String keyword, @Param("abbrIPv6") String abbrIPv6, @Param("onPage") Integer onPage, @Param("pageSize") Integer pageSize);

    @Select({
            "<script>",
            "select count(DISTINCT domain) from dws.dws_base_domain_rule_ip_all  where  (lower(ip) like concat('%',lower(#{keyword}),'%')",
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or lower(ip)=lower(#{abbrIPv6})",
            "</if> )",
            "</script>"
    })
    Integer selectDoMainListByKeywordCount(@Param("keyword") String keyword, @Param("abbrIPv6") String abbrIPv6);

    @Select({
            "<script>",
            "select DISTINCT domain from dws.dws_base_domain_rule_ip_all",
            "where (ip = #{ip}",
            "<if test='abbrIPv6!=null and abbrIPv6!=\"\"'>",
            "or ip=#{abbrIPv6}",
            "</if> )",
            "ORDER BY behavior_num desc",
            "LIMIT 10000",
            "</script>"
    })
    List<String> selectDomainListByIp(@Param("ip") String ip, @Param("abbrIPv6") String abbrIPv6);

    @Select({
            "<script>",
            "SELECT",
            "t.`domain` as `name`, arrayStringConcat(groupArray(t.ip),',') as ipStr",
            "from",
            "(",
            "select",
            "`domain`,",
            "ip",
            "from",
            "dws.dws_base_domain_rule_ip_all",
            "where domain in",
            "<foreach collection='names' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "ORDER BY",
            "behavior_num desc,ip asc",
            "limit #{top} by `domain` ) t GROUP by `name`",
            "</script>"})
    List<Map<String, String>> selectTop(@Param("names") Set<String> names, @Param("top") int top);

    @Select({"SELECT  ip, any(ip_type) as ipType, any(port)",
            "FROM dws.dws_base_domain_rule_ip_all  where `domain`=#{name} GROUP by ip ORDER BY sum(behavior_num) desc,ip asc LIMIT #{top}"})
    List<DomainIpRuleEntity> queryIpList(@Param("name") String name, @Param("top") Integer top);

    @Select({"<script>",
            "select DISTINCT `domain` from dws.dws_base_domain_rule_ip_all where  insert_day= toYYYYMMDD(now())   and `domain` in ",
            "<foreach collection='domainList' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    List<String> selectDomainsByDomainList(@Param("domainList") List<String> domainList);

    @Select({
            "<script>",
            "insert into dws.dws_base_domain_rule_ip",
            "SELECT toYYYYMMDD(now()) AS insert_day,  domain,  ip, ip_type,  port, behavior_num,#{insertTime}",
            "FROM",
            "dws.dws_base_domain_rule_ip_all where `domain` in ",
            "<foreach collection='newList' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    void insertHistoryDomainIp(@Param("newList") List<String> newList, @Param("insertTime") Long insertTime);

    @Select({"select partition from cluster('warehouse_cluster', `system`, parts) where database=#{database} and table=#{table}"})
    List<String> selectAllPartition(@Param("database") String database, @Param("table") String table);

    @Select({"select count(1) from dws.dws_base_domain_rule_all "})
    long selectDomainCount();

    @Select({"<script>",
            "select domain,ip,port from dws.dws_base_domain_rule_ip_all where  `domain` in ",
            "<foreach collection='domainList' item='item' index='index' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    List<DomainIpRuleEntity> queryIpByNameList(@Param("domainList") List<String> domainList);
}
