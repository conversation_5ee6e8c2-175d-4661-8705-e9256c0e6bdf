package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.semptian.entity.FixedIpEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-09 10:52
 */
@Mapper
public interface FixedIpMapper extends BaseMapper<FixedIpEntity> {


    /**
     * 插入固定Ip知识库个人
     *
     * @param fixedIpEntity
     * @return
     */
    @Insert({
            "<script>",
            "insert into fixed_ip_archive (id,IP_NAME, IP_ADDRESS, ORIGINAL_IP_ADDRESS, ENTITY_TYPE, REMARK, CREATE_TIME,MODIFY_TIME,INDIVIDUAL_NAME,PERSON_SEX,BIRTH,CARD_ENTERPRISE_TYPE,CARD_NUM_CODE,ADDRESS,WORK_ADDRESS,POSITION,PERSON_DEGREE,PHONE_NUM,PERSON_POLITICAL_STATUS,NATIONALITY_INDUSTRY)",
            "SELECT ",
            "#{id},#{ipName},#{ipAddress},#{originalIpAddress},1,#{remark},#{createTime},#{modifyTime},#{individualName},#{personSex},#{birth},#{cardEnterpriseType},#{cardNumCode},#{address},#{workAddress},#{position},#{personDegree},#{phoneNum},#{personPoliticalStatus},#{nationalityIndustry}",
            "FROM DUAL",
            "WHERE not exists (",
            "SELECT id, IP_NAME, IP_ADDRESS, ENTITY_TYPE, REMARK, CREATE_TIME,MODIFY_TIME,INDIVIDUAL_NAME,PERSON_SEX,BIRTH,CARD_ENTERPRISE_TYPE,CARD_NUM_CODE,ADDRESS,WORK_ADDRESS,POSITION,PERSON_DEGREE,PHONE_NUM,PERSON_POLITICAL_STATUS,NATIONALITY_INDUSTRY FROM fixed_ip_archive WHERE IP_ADDRESS = #{ipAddress}",
            "AND DELETE_STATUS = 0)",
            "</script>"

    })
    int insertFixedIpPersonal(FixedIpEntity fixedIpEntity);

    /**
     * 插入固定Ip知识库企业
     *
     * @param fixedIpEntity
     * @return
     */
    @Insert({
            "<script>",
            "insert into fixed_ip_archive (id,IP_NAME, IP_ADDRESS, ORIGINAL_IP_ADDRESS, ENTITY_TYPE, REMARK, CREATE_TIME,MODIFY_TIME,INDIVIDUAL_NAME,BIRTH,CARD_ENTERPRISE_TYPE,CARD_NUM_CODE,ADDRESS,WORK_ADDRESS,JURIDICAL_PERSON,PHONE_NUM,ENTERPRISE_SCALE,ENTERPRISE_EMPLOYEE_NUM,ENTERPRISE_REGISTER_CAPITAL,NATIONALITY_INDUSTRY)",
            "SELECT ",
            "#{id},#{ipName},#{ipAddress},#{originalIpAddress},0,#{remark},#{createTime},#{modifyTime},#{individualName},#{birth},#{cardEnterpriseType},#{cardNumCode},#{address},#{workAddress},#{juridicalPerson},#{phoneNum},#{enterpriseScale},#{enterpriseEmployeeNum},#{enterpriseRegisterCapital},#{nationalityIndustry}",
            "FROM DUAL",
            "WHERE not exists (",
            "SELECT id, IP_NAME, IP_ADDRESS, ENTITY_TYPE, REMARK, CREATE_TIME,MODIFY_TIME,INDIVIDUAL_NAME,BIRTH,CARD_ENTERPRISE_TYPE,CARD_NUM_CODE,ADDRESS,WORK_ADDRESS,JURIDICAL_PERSON,PHONE_NUM,ENTERPRISE_SCALE,ENTERPRISE_EMPLOYEE_NUM,ENTERPRISE_REGISTER_CAPITAL,NATIONALITY_INDUSTRY FROM fixed_ip_archive WHERE IP_ADDRESS = #{ipAddress}",
            "AND DELETE_STATUS = 0)",
            "</script>"

    })
    int insertFixedIpEnterprise(FixedIpEntity fixedIpEntity);


    /**
     * 列表查询
     *
     * @param deleteStatus
     * @param sortSql
     * @param entityType
     * @param keyword
     * @param archiveStatus
     * @return
     */
    @Select({
            "<script>",
            "SELECT ",
            "IP_NAME as ipName,",
            "IP_ADDRESS as ipAddress,",
            "ORIGINAL_IP_ADDRESS as originalIpAddress,",
            "ENTITY_TYPE as entityType,",
            "REMARK as remark,",
            "CREATE_TIME as createTime,",
            "MODIFY_TIME as modifyTime,",
            "INDIVIDUAL_NAME as individualName,",
            "PERSON_SEX as personSex,",
            "BIRTH as birth,",
            "CARD_ENTERPRISE_TYPE as cardEnterpriseType,",
            "CARD_NUM_CODE as cardNumCode,",
            "ADDRESS as address,",
            "WORK_ADDRESS as workAddress,",
            "JURIDICAL_PERSON as juridicalPerson,",
            "POSITION as position,",
            "PERSON_DEGREE as personDegree,",
            "PHONE_NUM as phoneNum,",
            "PERSON_POLITICAL_STATUS as personPoliticalStatus,",
            "NATIONALITY_INDUSTRY as nationalityIndustry,",
            "ENTERPRISE_SCALE as enterpriseScale,",
            "ENTERPRISE_EMPLOYEE_NUM as enterpriseEmployeeNum,",
            "ENTERPRISE_REGISTER_CAPITAL as enterpriseRegisterCapital ",
            "FROM fixed_ip_archive f WHERE DELETE_STATUS = #{deleteStatus}",
            " <if test = 'entityType != 2'> AND ENTITY_TYPE = #{entityType}</if>",
            " <if test = 'archiveStatus != null'> AND ARCHIVE_STATUS = #{archiveStatus}</if>",
            " <if test = \"keyword != null and keyword != ''\"> AND (IP_ADDRESS LIKE '%${keyword}%' OR IP_NAME LIKE '%${keyword}%' OR INDIVIDUAL_NAME LIKE '%${keyword}%') </if>",
            " ${sortSql} ",
            "</script>"
    })
    List<FixedIpEntity> getFixedIpList(IPage<FixedIpEntity> page, @Param("deleteStatus") Integer deleteStatus, @Param("sortSql") String sortSql, @Param("entityType") Integer entityType, @Param("keyword") String keyword, @Param("archiveStatus") Integer archiveStatus);

    /**
     * 查询列表总数
     *
     * @param deleteStatus
     * @param entityType
     * @param keyword
     * @param archiveStatus
     * @return
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM (",
            "SELECT IP_NAME,IP_ADDRESS,ENTITY_TYPE,REMARK,MODIFY_TIME,INDIVIDUAL_NAME FROM fixed_ip_archive WHERE DELETE_STATUS = #{deleteStatus}",
            " <if test = 'entityType != 2'> AND ENTITY_TYPE = #{entityType}</if>",
            " <if test = 'archiveStatus != null'> AND ARCHIVE_STATUS = #{archiveStatus}</if>",
            " <if test = \"keyword != null and keyword != ''\"> AND (IP_ADDRESS LIKE '%${keyword}%' OR IP_NAME LIKE '%${keyword}%' OR INDIVIDUAL_NAME LIKE '%${keyword}%') </if>",
            ")",
            "</script>"
    })
    Long countFixedIpList(@Param("deleteStatus") Integer deleteStatus, @Param("entityType") Integer entityType, @Param("keyword") String keyword, @Param("archiveStatus") Integer archiveStatus);

    /**
     * 批量删除知识库
     * @param modifyTime
     * @param addressList
     * @return
     */
    @Update({
            "<script>",
            "UPDATE fixed_ip_archive SET DELETE_STATUS = 1,MODIFY_TIME = #{modifyTime} WHERE DELETE_STATUS = 0 AND ORIGINAL_IP_ADDRESS IN ",
            "<foreach collection ='addressList' item = 'address' separator=',' open='(' close=')'>",
            "'${address}'",
            "</foreach>",
            "</script>"
    })
    int deleteBatchIp(@Param("addressList") List<String> addressList,@Param("modifyTime") Long modifyTime);

    /**
     * 对知识库个人用户进行修改
     * @param sql
     * @param ipAddress
     * @param entityType
     * @param birth
     * @return
     */
    @Update({
            "<script>",
            "UPDATE fixed_ip_archive SET ",
            "${sql}",
            "WHERE ORIGINAL_IP_ADDRESS = #{ipAddress} AND ENTITY_TYPE = #{entityType} AND DELETE_STATUS = 0",
            "</script>"
    })
    int updateFixedIpPersonal(@Param("ipAddress") String ipAddress, @Param("entityType") Integer entityType, @Param("sql") String sql, @Param("birth") Date birth);

    /**
     * 对知识库企业用户进行修改
     * @param birth
     * @param entityType
     * @param ipAddress
     * @param sql
     * @return
     */
    @Update({
            "<script>",
            "UPDATE fixed_ip_archive SET ",
            "${sql}",
            "WHERE ORIGINAL_IP_ADDRESS = #{ipAddress} AND ENTITY_TYPE = #{entityType} AND DELETE_STATUS = 0",
            "</script>"
    })
    int updateFixedIpEnterprise(@Param("ipAddress") String ipAddress, @Param("entityType") Integer entityType, @Param("sql") String sql, @Param("birth") Date birth);

    /**
     * 查询删除时间是之前的固定Ip
     * @param startTime
     * @param size
     * @return
     */
    @Select({
            "<script>",
            "select",
            "ip_address" ,
            "from(" ,
            "select" ,
            "ip_address,MAX(modify_time) as modify_time" ,
            "from fixed_ip_archive" ,
            "where delete_status = 1",
            "group by ip_address ORDER BY MODIFY_TIME ASC,IP_ADDRESS DESC) m" ,
            "where m.modify_time &lt; #{startTime}",
            "AND ROWNUM BETWEEN 0 AND #{size}",
            "</script>"
    })
    List<String> selectIpAddressList(@Param("size") Integer size,@Param("startTime") Long startTime);

    /**
     * 查询近一小时最新数据
     * @param ipStr
     * @param offsetPoint
     * @param offsetPage
     * @param lastScheduledTime
     * @return
     */
    @Select({
            "<script>",
            "SELECT " ,
            "IP_NAME as ipName,",
            "IP_ADDRESS as ipAddress,",
            "ENTITY_TYPE as entityType,",
            "DELETE_STATUS as deleteStatus,",
            "REMARK as remark,",
            "CREATE_TIME as createTime,",
            "MODIFY_TIME as modifyTime,",
            "INDIVIDUAL_NAME as individualName,",
            "PERSON_SEX as personSex,",
            "BIRTH as birth,",
            "CARD_ENTERPRISE_TYPE as cardEnterpriseType,",
            "CARD_NUM_CODE as cardNumCode,",
            "ADDRESS as address,",
            "WORK_ADDRESS as workAddress,",
            "JURIDICAL_PERSON as juridicalPerson,",
            "POSITION as position,",
            "PERSON_DEGREE as personDegree,",
            "PHONE_NUM as phoneNum,",
            "PERSON_POLITICAL_STATUS as personPoliticalStatus,",
            "NATIONALITY_INDUSTRY as nationalityIndustry,",
            "ENTERPRISE_SCALE as enterpriseScale,",
            "ENTERPRISE_EMPLOYEE_NUM as enterpriseEmployeeNum,",
            "ENTERPRISE_REGISTER_CAPITAL as enterpriseRegisterCapital ",
            "FROM(",
            "SELECT ROWNUM rn,a.* FROM (",
            "SELECT",
            "IP_ADDRESS,",
            "MAX(MODIFY_TIME) maxRelationTime",
            "FROM",
            "fixed_ip_archive ",
            "GROUP BY",
            "IP_ADDRESS) b,",
            "fixed_ip_archive a",
            "WHERE b.IP_ADDRESS =a.IP_ADDRESS AND b.maxRelationTime = a.MODIFY_TIME",
            "<if test = 'lastScheduledTime != null and lastScheduledTime !=0 '> AND a.MODIFY_TIME &gt;= ${lastScheduledTime} </if>",
            "<if test = \"ipStr != null and ipStr != ''\">AND b.IP_ADDRESS IN (${ipStr})</if>",
            "AND ROWNUM &lt;= #{offsetPage}) fia",
            "WHERE fia.rn &gt;= #{offsetPoint}",
            "</script>"
    })
    List<FixedIpEntity> selectFixedIpNew(@Param("ipStr") String ipStr,@Param("offsetPoint") int offsetPoint,@Param("offsetPage") int offsetPage,@Param("lastScheduledTime") Long lastScheduledTime);

    /**
     *统计总数
     * @param ipStr
     * @param lastScheduledTime
     * @return
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM (",
            "SELECT",
            "IP_ADDRESS,",
            "MAX(MODIFY_TIME) maxRelationTime",
            "FROM",
            "fixed_ip_archive ",
            "GROUP BY",
            "IP_ADDRESS) b,",
            "fixed_ip_archive a",
            "WHERE b.IP_ADDRESS =a.IP_ADDRESS AND b.maxRelationTime = a.MODIFY_TIME",
            "<if test = 'lastScheduledTime != null and lastScheduledTime !=0 '> AND a.MODIFY_TIME &gt;= ${lastScheduledTime} </if>",
            "<if test = \"ipStr != null and ipStr != ''\">AND b.IP_ADDRESS IN (${ipStr})</if>",
            "</script>"
    })
    Integer countFixedIpNew(@Param("ipStr") String ipStr,@Param("lastScheduledTime") Long lastScheduledTime);

    /**
     * 获取未删除的ip
     * @return
     */
    @Select({
            "<script>",
            "SELECT",
            "IP_ADDRESS",
            "FROM",
            "fixed_ip_archive ",
            "WHERE DELETE_STATUS = 0",
            "GROUP BY",
            "IP_ADDRESS",
            "</script>"
    })
    List<String> selectFixedIpList();

    /**
     * 查询已删除的最大建档时间，用于数据修复
     * @param ipAddress
     * @return
     */
    @Select({
            "<script>",
            "SELECT CREATE_TIME FROM (",
            "SELECT ",
            "ROWNUM,fia.* ",
            "FROM fixed_ip_archive fia ",
            "WHERE ORIGINAL_IP_ADDRESS = #{ipAddress} ",
            "AND DELETE_STATUS = 1",
            "ORDER BY MODIFY_TIME DESC,ID ASC)",
            "WHERE ROWNUM = 1",
            "</script>"
    })
    Long selectFixedIpRepair(@Param("ipAddress") String ipAddress);

    /**
     * 查询固定IP是否存在
     * @param ipAddress
     * @return
     */
    @Select({
            "<script>",
            "SELECT ",
            "IP_ADDRESS",
            "FROM fixed_ip_archive fia ",
            "WHERE IP_ADDRESS = #{ipAddress} ",
            "AND DELETE_STATUS = 0",
            "</script>"
    })
    List<FixedIpEntity> selectFixedIp(@Param("ipAddress") String ipAddress);
}
