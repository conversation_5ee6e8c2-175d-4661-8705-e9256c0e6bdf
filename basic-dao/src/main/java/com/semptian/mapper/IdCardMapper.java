package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.IdCardEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IdCardMapper extends BaseMapper<IdCardEntity> {
    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_IDCARD.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    @Insert({
            "<script>",
            "INSERT INTO TB_IDCARD (",
                    "ID,",
                    "NET_SITE_ID,",
                    "NET_SITE_NAME,",
                    "CARD_ID,",
                    "USERNAME,",
                    "SEX,",
                    "CERTIFICATE_CODE,",
                    "CERTIFICATE_TYPE,",
                    "CITY_CODE,",
                    "CERTIFICATION_UNIT,",
                    "NATIONALITY,",
                    "COMPANY_NAME,",
                    "MOBI<PERSON>,",
                    "ROOM_ID,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_IDCARD.nextval,",
                    "NET_SITE_ID,",
                    "NET_SITE_NAME,",
                    "CARD_ID,",
                    "USERNAME,",
                    "SEX,",
                    "CERTIFICATE_CODE,",
                    "CERTIFICATE_TYPE,",
                    "CITY_CODE,",
                    "CERTIFICATION_UNIT,",
                    "NATIONALITY,",
                    "COMPANY_NAME,",
                    "MOBILE,",
                    "ROOM_ID,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
                    "#{item.netSiteId} AS NET_SITE_ID,",
                    "#{item.netSiteName} AS NET_SITE_NAME,",
                    "#{item.cardId} AS CARD_ID,",
                    "#{item.userName} AS USERNAME,",
                    "to_char(#{item.sex}) AS SEX,",
                    "#{item.certificateCode} AS CERTIFICATE_CODE,",
                    "#{item.certificateType} AS CERTIFICATE_TYPE,",
                    "#{item.cityCode} AS CITY_CODE,",
                    "#{item.certificationUnit} AS CERTIFICATION_UNIT,",
                    "#{item.nationality} AS NATIONALITY,",
                    "#{item.companyName} AS COMPANY_NAME,",
                    "#{item.mobile} AS MOBILE,",
                    "#{item.roomId} AS ROOM_ID,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_IDCARD T WHERE T.NET_SITE_ID = TT.NET_SITE_ID)",
            "</script>"
    })
    void batchInsert(List list);
}
