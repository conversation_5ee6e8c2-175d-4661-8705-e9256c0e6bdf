package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.InvalidTelEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InvalidTelMapper extends BaseMapper<InvalidTelEntity> {

    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_INVALID_TELEPHONE.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    /**
     * 判断手机号码是否存在
     *
     * @param id          主键ID
     * @param phoneNumber 手机号码
     * @return int
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM TB_INVALID_TELEPHONE WHERE PHONE_NUMBER = #{phoneNumber}",
            "<if test='id!= null'>",
            "AND ID != #{id}",
            "</if>",
            "</script>"
    })
    int checkPhoneNumberExist(
            @Param("id") Long id,
            @Param("phoneNumber") String phoneNumber
    );

    @Insert({
            "<script>",
            "INSERT INTO TB_INVALID_TELEPHONE (",
                    "ID,",
                    "PHONE_NUMBER,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_INVALID_TELEPHONE.nextval,",
                    "PHONE_NUMBER,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
                    "#{item.phoneNumber} AS PHONE_NUMBER,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_INVALID_TELEPHONE T WHERE T.PHONE_NUMBER = TT.PHONE_NUMBER)",
            "</script>"
    })
    void batchInsert(List list);
}
