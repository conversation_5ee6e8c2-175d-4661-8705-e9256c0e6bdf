package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.IpLibraryEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IpLibraryMapper extends BaseMapper<IpLibraryEntity> {
    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_IP_LIBRARY.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    @Insert({
            "<script>",
            "INSERT INTO TB_IP_LIBRARY (",
                    "ID,",
                    "START_IP,",
                    "END_IP,",
                    "IP_ID,",
                    "COMPANY_NAME,",
                    "ADDRESS,",
                    "CONTACT_NAME,",
                    "CONTACT_NUMBER,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_IP_LIBRARY.nextval,",
                    "START_IP,",
                    "END_IP,",
                    "IP_ID,",
                    "COMPANY_NAME,",
                    "ADDRESS,",
                    "CONTACT_NAME,",
                    "CONTACT_NUMBER,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
                    "#{item.startIp} AS START_IP,",
                    "#{item.endIp} AS END_IP,",
                    "#{item.ipId} AS IP_ID,",
                    "#{item.companyName} AS COMPANY_NAME,",
                    "#{item.address} AS ADDRESS,",
                    "#{item.contactName} AS CONTACT_NAME,",
                    "#{item.contactNumber} AS CONTACT_NUMBER,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_IP_LIBRARY T WHERE T.IP_ID = TT.IP_ID)",
            "</script>"
    })
    void batchInsert(List list);
}
