package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.MobileRegisterEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @author: ZC
 * @date: 2021/3/12 9:34
 */
public interface MobileRegisterMapper extends BaseMapper<MobileRegisterEntity> {

    /**
     * 获取线索id的下一个序列L
     *
     * @return
     */
    @Select({
            "<script>",
            "SELECT SEQ_MOBILE_REGISTER.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long nextVal();

    @Insert({
            "<script>",
            "INSERT INTO TB_MOBILE_REGISTER (",
                    "ID,",
                    "IMSI,",
                    "MSISDN,",
                    "NAME,",
                    "SEX,",
                    "AGE,",
                    "NATIONALITY,",
                    "CERTIFICATE_CODE,",
                    "CERTIFICATE_TYPE,",
                    "COMPANY_NAME,",
                    "REGISTERED_ADDRESS,",
                    "NOW_ADDRESS,",
                    "EDUCATION,",
                    "EMAIL,",
                    "ROOM_ID,",
                    "RESIDENCE_TYPE,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_MOBILE_REGISTER.nextval,",
                    "IMSI,",
                    "MSISDN,",
                    "NAME,",
                    "SEX,",
                    "AGE,",
                    "NATIONALITY,",
                    "CERTIFICATE_CODE,",
                    "CERTIFICATE_TYPE,",
                    "COMPANY_NAME,",
                    "REGISTERED_ADDRESS,",
                    "NOW_ADDRESS,",
                    "EDUCATION,",
                    "EMAIL,",
                    "ROOM_ID,",
                    "RESIDENCE_TYPE,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
                    "#{item.imsi} AS IMSI,",
                    "#{item.msisdn} AS MSISDN,",
                    "#{item.name} AS NAME,",
                    "to_char(#{item.sex}) AS SEX,",
                    "to_char(#{item.age}) AS AGE,",
                    "#{item.nationality} AS NATIONALITY,",
                    "#{item.certificateCode} AS CERTIFICATE_CODE,",
                    "#{item.certificateType} AS CERTIFICATE_TYPE,",
                    "#{item.companyName} AS COMPANY_NAME,",
                    "#{item.registeredAddress} AS REGISTERED_ADDRESS,",
                    "#{item.nowAddress} AS NOW_ADDRESS,",
                    "#{item.education} AS EDUCATION,",
                    "#{item.email} AS EMAIL,",
                    "#{item.roomId} AS ROOM_ID,",
                    "to_char(#{item.residenceType}) AS RESIDENCE_TYPE,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_MOBILE_REGISTER T WHERE T.MSISDN = TT.MSISDN)",
            "</script>"
    })
    void batchInsert(List list);
}
