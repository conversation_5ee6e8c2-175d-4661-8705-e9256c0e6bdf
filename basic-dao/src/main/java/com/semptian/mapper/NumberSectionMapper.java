package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.NumberSectionEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface NumberSectionMapper extends BaseMapper<NumberSectionEntity> {
    /**
     * 获取下一个序列
     */
    @Select({
            "<script>",
            "SELECT SEQ_MOBILE_SECTION.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    /**
     * 判断电话号码是否存在
     *
     * @param id     主键ID
     * @param msisdn 电话号码
     * @return int
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM TB_MOBILE_SECTION WHERE MSISDN = #{msisdn}",
            "<if test='id!= null'>",
            "AND ID != #{id}",
            "</if>",
            "</script>"
    })
    int checkMsisdnExist(
            @Param("id") Long id,
            @Param("msisdn") String msisdn
    );

    @Insert({
            "<script>",
            "INSERT INTO TB_MOBILE_SECTION (",
                    "ID,",
                    "MSISDN,",
                    "CITY_CODE,",
                    "CITY_NAME,",
                    "NET_TYPE,",
                    "POST_CODE,",
                    "AREA_CODE,",
                    "ISP_CODE,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            ")",
            "SELECT ",
                    "SEQ_MOBILE_SECTION.nextval,",
                    "MSISDN,",
                    "CITY_CODE,",
                    "CITY_NAME,",
                    "NET_TYPE,",
                    "POST_CODE,",
                    "AREA_CODE,",
                    "ISP_CODE,",
                    "REMARK,",
                    "STATUS,",
                    "CREATE_TIME,",
                    "UPDATE_TIME",
            "FROM",
            "(",
            "<foreach collection='list' item='item' index='index' separator='UNION ALL'>",
            "SELECT",
                    "#{item.msisdn} AS MSISDN,",
                    "#{item.cityCode} AS CITY_CODE,",
                    "#{item.cityName} AS CITY_NAME,",
                    "#{item.netType} AS NET_TYPE,",
                    "#{item.postCode} AS POST_CODE,",
                    "#{item.areaCode} AS AREA_CODE,",
                    "to_char(#{item.ispCode}) AS ISP_CODE,",
                    "#{item.remark} AS REMARK,",
                    "#{item.status} AS STATUS,",
                    "#{item.createTime} AS CREATE_TIME,",
                    "#{item.updateTime} AS UPDATE_TIME",
            "FROM DUAL",
            "</foreach>",
            ") TT",
            "WHERE NOT EXISTS(SELECT 1 FROM TB_MOBILE_SECTION T WHERE T.MSISDN = TT.MSISDN)",
            "</script>"
    })
    void batchInsert(List list);
}
