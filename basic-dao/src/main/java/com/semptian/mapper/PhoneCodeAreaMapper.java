package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.PhoneCodeAreaEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 号码国际区号实体类Mapper接口
 * <AUTHOR>
 * @since  2024/5/10
 */
@Mapper
public interface PhoneCodeAreaMapper extends BaseMapper<PhoneCodeAreaEntity> {

    /**
     * 查询号码国际区号列表
     * @return 号码国际区号列表
     */
    @Select("select phone_code as phoneCode from tb_phone_code_area order by id")
    List<PhoneCodeAreaEntity> selectList();
}
