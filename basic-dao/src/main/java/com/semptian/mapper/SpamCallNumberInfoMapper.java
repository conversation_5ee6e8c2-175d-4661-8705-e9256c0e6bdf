package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.SpamCallNumberInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 骚扰号码信息库表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since  2024/3/20
 */
@Mapper
public interface SpamCallNumberInfoMapper extends BaseMapper<SpamCallNumberInfoEntity> {

    /**
     * 判断号码是否存在重复
     * @param phoneNumber 号码
     * @param id 号码id
     * @return 号码是否存在重复
     */
    @Select({
            "<script>",
            "SELECT name FROM tb_spam_call_number_info",
            "where concat(',', phone_number, ',') like concat('%,',#{phoneNumber},',%') and is_del=0 ",
            "<if test='id !=null '>",
            "and id != #{id}",
            "</if>",
            "</script>"
    })
    String phoneNumIsExist(@Param("phoneNumber") String phoneNumber, @Param("id") Integer id);
}
