package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.SpecialNumberEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 特殊号码SQL管理处
 *
 * @Author: sk
 * @Date: 2020/12/17 14:49
 */
public interface SpecialNumberMapper extends BaseMapper<SpecialNumberEntity> {



    /**
     * 查询特殊号码总数
     *
     * @param keyword
     * @param startTime
     * @param endTime
     * @return
     */

    @Select({
            "<script>",

            "SELECT count(1) FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1",
            "<if test='keyword != null' >",
            " AND LOWER(s.id) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and s.update_Time &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and s.update_Time &lt;= #{endTime}",
            "</if>",

            "</script>"
    })

    int querySpecialNumberCount(@Param("keyword") String keyword, @Param("startTime") Long startTime, @Param("endTime") Long endTime);



    /**
     * 查询特殊号码列表
     * @param keyword
     * @param startIndex
     * @param endIndex
     * @param orderField
     * @param orderType
     * @param startTime
     * @param endTime
     * @return
     */

    @Select({
            "<script>",
            "SELECT TT.*",
            "FROM (",
            "SELECT T.*,ROWNUM AS rowno",
            "FROM (",
            "SELECT id, s.phoneNum fullNum, remark, s.PHONESTATE phoneState ,s.update_time updateTime ,s.country_code countryCode,s.telephone_num telephoneNum,s.createTime,2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 ",
            "<if test='keyword != null' >",
            " AND LOWER(s.PHONENUM) LIKE  '%' || LOWER(#{keyword}) || '%' ",
            "</if>",
            "<if test='startTime != null'>",
            "and s.update_Time &gt; #{startTime}",
            "</if>",

            "<if test='endTime != null'>",
            "and s.update_Time &lt;= #{endTime}",
            "</if>",

            "ORDER BY",
            "<if test='orderField != null' >",
            " ${orderField} ",
            "<if test='orderType != null &amp;&amp; orderType == 1' >",
            " ASC, ",
            "</if>",
            "<if test='orderType != null &amp;&amp; orderType == 0' >",
            " DESC, ",
            "</if>",
            "</if>",
            "id ASC",

            ") T WHERE ROWNUM &lt;= #{endIndex} ",
            ") TT WHERE TT.rowno &gt; #{startIndex}",
            "</script>"
    })
    List<ClueDto> querySpecialNumberList(
            @Param("keyword") String keyword,
            @Param("startIndex") int startIndex,
            @Param("endIndex") int endIndex,
            @Param("orderField") String orderField,
            @Param("orderType") int orderType,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime

    );

    /**
     * 校验特殊号码
     *
     * @param telephoneNum
     * @param countryCode
     * @param id
     * @return
     */
    @Select({
            "<script>",
            "select count(1) from SPECIALTELEFILTERTABLE s ",
            "where  TELEPHONE_NUM = #{telephoneNum}  and COUNTRY_CODE = #{countryCode}",
            "<if test='id!= null'>",
            "and ",
            " s.id != #{id} ",
            "</if>",
            "</script>"
    })
    int checkSpecialNumber(@Param("telephoneNum") String telephoneNum,@Param("countryCode") String countryCode,@Param("id") Long id);

    /**
     * 查询特殊号码详情
     *
     * @param id
     * @return
     */
    @Select({
            "SELECT id, s.phoneNum fullNum, remark, s.PHONESTATE phoneState ,s.update_time updateTime ,s.country_code countryCode,s.telephone_num telephoneNum,s.createTime,2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 and id = #{id} ",
    })
    ClueDto querySpecialNumberDetail(@Param("id")Long id);


    /**
     * 获取特殊号码id的下一个序列
     *
     * @return
     */
    @Select({
            "<script>",
            "SELECT SEQ_CLUETABLE.NEXTVAL FROM DUAL",
            "</script>"
    })
    Long getNextVal();

    /**
     * 修改特殊号码状态
     *
     * @param specialList
     * @param toStatus
     * @return
     */
    @Update({
            "<script>",
            "UPDATE SPECIALTELEFILTERTABLE S",
            "SET",
            "S.PHONESTATE = #{toStatus}",
            "<where>",
            "<if test='specialList != null  &amp;&amp; specialList.size>0' >",
            "and",
            "id in",
            "<foreach collection='specialList' item='idItem' separator=',' open='(' close=')'>",
            "       #{idItem} ",
            "</foreach>",
            "</if>",

            "<if test=' specialList == null or specialList.size==0' >",
            "and 1 = 0",
            "</if>",
            "</where>",
            "</script>"
    })
    int updateBatchByStatus(@Param("specialList") List<Long> specialList, @Param("toStatus") Integer toStatus);

    @Select({
            "<script>",
            "SELECT  s.phoneNum fullNum, s.PHONESTATE phoneState ,s.update_time updateTime , 2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 ",
            "ORDER BY",
            "updateTime DESC",
            "</script>"
    })
    List<ClueDto> selectOrdinaryExport();


    @Select({
            "<script>",
            "SELECT  s.phoneNum fullNum, s.PHONESTATE phoneState ,s.update_time updateTime , 2 phoneType FROM SPECIALTELEFILTERTABLE s WHERE 1 = 1 ",
            "<if test='specialIds != null  &amp;&amp; specialIds.size>0' >",
            "and",
            "id in",
            "<foreach collection='specialIds' item='idItem' separator=',' open='(' close=')'>",
            "       #{idItem} ",
            "</foreach>",
            "</if>",
            "ORDER BY",
            "updateTime DESC",
            "</script>"
    })
    List<ClueDto> selectOrdinaryByIdsExport(@Param("specialIds") List<Long> specialIds);
}
