package com.semptian.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.UserCategoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 上网用户分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@DS("mysql")
@Mapper
public interface UserCategoryMapper extends BaseMapper<UserCategoryEntity> {

    /**
     * 获取所有最后一级分类
     * @return List<UserCategoryEntity>
     */
    @Select("SELECT * from tb_user_category where is_del = 0 and id not in (SELECT DISTINCT pid from tb_user_category where is_del = 0)")
    List<UserCategoryEntity> getAllLastUserCategoryList();
}
