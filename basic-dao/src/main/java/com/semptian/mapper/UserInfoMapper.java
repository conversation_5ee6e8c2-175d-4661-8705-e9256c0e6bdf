package com.semptian.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.dto.usercategory.UserCategoryCountDto;
import com.semptian.entity.UserInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 上网用户信息库表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@DS("mysql")
@Mapper
public interface UserInfoMapper extends BaseMapper<UserInfoEntity> {

    @Select({
            "<script>",
            "SELECT id,user_name,user_type,user_category_id from tb_user_info where is_del = 0 ",
            "and (user_name, user_type) in",
            "<foreach collection='list' item='item' index='index' separator=',' open='(' close=')' >",
            "(#{item.userName, jdbcType=VARCHAR}, #{item.userType, jdbcType=INTEGER})",
            "</foreach>",
            "</script>"
    })
    List<UserInfoEntity> getImportantTargetCategoryByBatch(@Param("list") List<UserInfoEntity> importantTargetList);

    @Select("SELECT user_category_id userCategoryId,count(*) num from tb_user_info where is_del = 0 group by user_category_id")
    List<UserCategoryCountDto> getAllUserCategoryList();
}
