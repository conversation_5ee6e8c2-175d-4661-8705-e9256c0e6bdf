package com.semptian.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * BaseStationLocationInfoVo对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */

@Data
public class BaseStationLocationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 基站编号
     */
    private String stationNo;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 覆盖半径
     */
    private BigDecimal coverageRadius;

    /**
     * 网络类型,1=2G基站,2=3G基站,3=4G基站,4=5G基站,5=混合基站
     */
    private Integer networkType;

    /**
     * 网络类型名称
     */
    private String networkTypeName;

    /**
     * 基站地址
     */
    private String stationAddress;

    /**
     * 等级,1=宏站,2=微站,3=分布式天线站,4=小区基站,5=微小区基站
     */
    private Integer grade;

    /**
     * 等级名称
     */
    private String gradeName;

    /**
     * 归属运营商,1=Mobilis,2=Ooredoo,3=Djezzy
     */
    private Integer networkOperator;

    /**
     * 归属运营商名称
     */
    private String networkOperatorName;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long modifyTime;

    /**
     * 更新时间格式化
     */
    private String modifyTimeName;

    /**
     * 基站CELL编号
     */
    private String cellName;
}
