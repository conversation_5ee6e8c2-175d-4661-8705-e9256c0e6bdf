<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>deye-basic-library</artifactId>
        <groupId>com.semptian</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>basic-service</artifactId>
    <version>1.0-SNAPSHOT</version>

    <dependencies>

        <!--module-->
        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>basic-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>basic-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
        </dependency>



        <!--excel-->

        <!--swagger-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <!--swagger-->

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.semptian.base</groupId>
            <artifactId>base-redis</artifactId>
            <version>v1.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>base-tianhe</artifactId>
            <version>${base-tianhe.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>base-core</artifactId>
            <version>${base-core.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>

    </dependencies>


</project>