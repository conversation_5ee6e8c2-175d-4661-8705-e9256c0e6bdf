package com.semptian.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 获取Redis分布式锁
 * */
@Component
public class RedisHelper {

    private static final int LOCK_EXPIRE = 10 * 1000;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 尝试获取分布式锁
     * */
    public boolean lock(String lock) {
        return redisTemplate.execute((RedisCallback<Boolean>) redisConnection -> {
            long expire = System.currentTimeMillis() + LOCK_EXPIRE;
            boolean acquire = redisConnection.setNX(lock.getBytes(), String.valueOf(expire).getBytes());
            if (acquire) {
                return true;
            } else {
                byte[] value = redisConnection.get(lock.getBytes());
                if (value != null && value.length > 0) {
                    long expireTime = Long.parseLong(new String(value));
                    // 锁已经过期
                    if (expireTime < System.currentTimeMillis()) {
                        redisConnection.del(lock.getBytes());
                    }
                }
            }

            return false;
        });
    }

    /**
     * 释放分布式锁
     * */
    public void releaseLock(String lock) {
        redisTemplate.delete(lock);
    }
}