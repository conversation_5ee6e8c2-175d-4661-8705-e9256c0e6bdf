package com.semptian.external;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.param.IcsLoginModel;
import com.semptian.param.IcsWhitelistRuleModel;
import com.semptian.utils.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description:
 * @Date: Created on 2024/7/24.
 * @Author: Lizhonghua
 */
@Component
@Slf4j
public class YJApiService {

    private volatile String sessionId;

    @Resource
    private RestTemplateUtil restTemplateUtil;

    /**
     * ICS系统地址
     */
    @Value("${ics.service.url}")
    private String icsUrl;

    @Value("${ics.login.username}")
    private String username;

    @Value("${ics.login.password}")
    private String password;

    /**
     * ICS login
     * @param user
     * @param password
     * @return sessionid
     */
    public String login(String user, String password) {
        IcsLoginModel loginModel = new IcsLoginModel(user, password);
        String url = icsUrl + "/ics/global/icslogin.do";

        ResponseEntity<ReturnModel> responseEntity = null;
        try {
            responseEntity  = restTemplateUtil.post(url, loginModel, ReturnModel.class);
        }catch (Exception e) {
            log.error("ics login error, request url:{} \r\n ", url, e);
        }

        if (responseEntity == null || responseEntity.getBody() == null) {
            log.error("ics login error! No response!");
            return null;
        }
        if (responseEntity.getBody().getCode() != CommonConstant.ICS_SUCCESS) {
            log.error("ics login error: {}", JSONUtil.toJsonStr(responseEntity));
            return null;
        }
        if (responseEntity.getHeaders() != null && !responseEntity.getHeaders().get(HttpHeaders.SET_COOKIE).isEmpty()) {

            for (String cookie : responseEntity.getHeaders().get(HttpHeaders.SET_COOKIE)) {
                if (cookie.contains("sessionid")){
                    return cookie.split(";")[0];
                }
            }
        }
        return null;
    }

    /**
     * ICS退出登录
     * @param sessionId
     * @return
     */
    public ReturnModel logout(String sessionId) {
        // 构造退出登录的URL
        String url = icsUrl + "/ics/global/icsexit.do";
        Map<String, String> headers = Maps.newHashMap();
        headers.put(HttpHeaders.COOKIE, sessionId);
        ResponseEntity<ReturnModel> responseEntity = restTemplateUtil.get(url, headers, ReturnModel.class);
        if (responseEntity == null || responseEntity.getBody() == null) {
            log.error("ics logout error! No response!");
            return null;
        }
        if (responseEntity.getBody().getCode() != CommonConstant.ICS_SUCCESS) {
            log.error("ics logout error: {}", JSONUtil.toJsonStr(responseEntity));
        }
        return responseEntity.getBody();
    }


    /**
     * 白名单规则操作接口
     * @param whitelistRuleModel
     * @return
     */
    public ReturnModel whitelistRule(IcsWhitelistRuleModel whitelistRuleModel) {
        // 构造下发规则的URL
        String url = icsUrl + "/ics/api/exclusion.do";
        Map<String, String> headers = Maps.newHashMap();
        headers.put(HttpHeaders.COOKIE, sessionId);
        ResponseEntity<ReturnModel> responseEntity = restTemplateUtil.post(url, headers, whitelistRuleModel, ReturnModel.class);
        if (responseEntity == null || responseEntity.getBody() == null) {
            log.error("ics whitelistRule error! No response!");
            return null;
        }
        if (responseEntity.getBody().getCode() != CommonConstant.ICS_SUCCESS) {
            log.error("ics whitelistRule error: {}", JSONUtil.toJsonStr(responseEntity));
        }
        return responseEntity.getBody();
    }

    @PostConstruct
    public void initSessionId(){
        if (StringUtils.isEmpty(sessionId)){
            synchronized (this){
                if (StringUtils.isEmpty(sessionId)){
                    sessionId = this.login(username, password);
                    if (StringUtils.isEmpty(sessionId)){
                        log.error("ICS login failed! ");
                    }
                }
            }
        }
    }
}
