package com.semptian.feign.archive;

import com.semptian.base.service.ReturnModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(name = "deye-archives-web",path = "/archives_web")
public interface ArchiveFeignClient {

    /**
     * 查询档案信息
     *
     * @param ipList
     * @return
     */
    @GetMapping(value = "/fixed_ip/query_fixed_ip_status.json")
    ReturnModel queryFixedIpStatus(@RequestParam(name = "ipList") String ipList);


    /**
     * 全息档案查询接口
     * @param arcId
     * @param arcAccount
     * @param arcType
     * @param arcAccountType
     * @return
     */
    @GetMapping(value = "/common_arc_detail/get_arc_info_by_id.json")
    ReturnModel getArcInfo(
            @RequestParam(name = "arcId", required = false) String arcId,
            @RequestParam(name = "arcAccount", required = false) String arcAccount,
            @RequestParam(name = "arcType", required = false) Integer arcType,
            @RequestParam(name = "arcAccountType", required = false) String arcAccountType,
            @RequestHeader(name = "userId") String userId,
            @RequestHeader(name = "lang") String lang);


    /**
     * 全息档案查询待修复的ip
     * @return
     */
    @GetMapping(value = "/fixed_ip/query_fixed_ip_repair.json")
    ReturnModel queryFixedIpRepair();

    /**
     * 删除固定IP档案统计表信息
     * @return
     */
    @GetMapping(value = "/ext_inter_api/fixIpAccountDelete.json")
    ReturnModel fixIpAccountDelete(@RequestParam(name = "fixIpAccount") String fixIpAccount);
}
