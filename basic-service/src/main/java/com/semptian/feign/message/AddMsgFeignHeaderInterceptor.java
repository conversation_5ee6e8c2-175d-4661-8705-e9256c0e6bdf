package com.semptian.feign.message;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @date 2021/3/19 18:11
 * @description 添加消息feign接口配置
 */
@Component
public class AddMsgFeignHeaderInterceptor implements RequestInterceptor {

    @Value("${basic.library.app.id:102}")
    private String appId;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes==null){
            requestTemplate.header("userId","-1");
            requestTemplate.header("appId",appId);
            return;
        }

        HttpServletRequest httpServletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        if (headerNames != null) {
            String userId = httpServletRequest.getHeader("userId");
            String lang = httpServletRequest.getHeader("lang");
            requestTemplate.header("userId", userId);
            requestTemplate.header("appId", appId);
            requestTemplate.header("serverName", "deye-basic-service");
            requestTemplate.header("lang", lang);
        }
    }
}