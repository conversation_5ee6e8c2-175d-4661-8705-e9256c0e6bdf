package com.semptian.feign.message;

import com.semptian.base.service.ReturnModel;
import com.semptian.param.AddMessageModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 消息服务Feign接口
 *
 * <AUTHOR>
 * @since 2024/12/30
 */
@FeignClient(value = "deye-common-message",configuration = AddMsgFeignHeaderInterceptor.class)
public interface MsgFeignClient {

    /**
     * Feign调用消息系统发送短信和页面通知
     * @param addMessageModel
     * @return
     */
    @PostMapping("/message/app_message/send_message_batch_user.json")
    ReturnModel sendMessageBatchUser(@RequestBody AddMessageModel addMessageModel);

}