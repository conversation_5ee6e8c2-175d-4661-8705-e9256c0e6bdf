package com.semptian.feign.portal;

import com.semptian.base.service.ReturnModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * Date: 2020/9/23 11:23
 * Description: Feign远程调用类
 */
@Lazy
@FeignClient(name = "deye-portal-service", fallback = PortalFeignFallBackFactory.class)
public interface PortalFeignClient {

    /**
     * Feign调用门户:根据用户id查询组织结构(已调试)                                  ----3.7
     *
     * @param userId 用户标识
     * @return ReturnModel
     */
    @GetMapping("/portal/organization/case_query_org_by_userid.json")
    ReturnModel queryOrgByUserId(
            @RequestParam("userId") String userId
    );

    /**
     * Feign调用门户:根据用户id查询用户信息(已调试)                                  -----3.19
     *
     * @param userId 用户标识
     * @return ReturnModel
     */
    @GetMapping("/portal/user/query_user_by_id.json")
    ReturnModel queryUserById(
            @RequestParam("userId") String userId
    );


    /**
     * Feign调用门户:根据用户id查询用户所属职位(已调试)                               -----6.4
     *
     * @param userId 用户标识
     * @return ReturnModel
     */
    @GetMapping("/portal/position/query_position_by_userid.json")
    ReturnModel queryPositionByUserId(
            @RequestParam("userId") String userId
    );

    /**
     * Feign调用门户:根据组织id(部门/用户组) 以及职位id查询用户列表(已调试)            -------3.4
     *
     * @param orgId       组织id
     * @param positionIds 职位id
     * @return ReturnModel
     */
    @GetMapping("/portal/organization/query_user_by_orgid.json")
    ReturnModel queryUserByOrgIdAndPostId(
            @RequestParam("orgId") String orgId,
            @RequestParam("positionIds") String positionIds
    );


    /**
     * Feign调用门户:根据职位id查询职位权限(未调试)
     *
     * @param postId 职位id
     * @return ReturnModel
     */
    @GetMapping("/portal/functionAuthority/find_post_permission.json")
    ReturnModel queryPermissionByPostId(
            @RequestParam("postId") String postId

    );

    /**
     * Feign调用门户：根据组织id(部门/用户组) 以及职位id查询可管理的审批用户列表(已调试)        --------3.8
     *
     * @param orgId       组织id
     * @param positionIds 职位id
     * @return ReturnModel
     * <AUTHOR>
     * @date 2020/9/24 17:37
     */
    @GetMapping("/portal/organization/query_manager.json")
    ReturnModel queryManagerId(
            @RequestParam("orgId") String orgId,
            @RequestParam("positionIds") String positionIds
    );

    /**
     * Feign调用门户：根据职位id和组织id(部门/用户组)获取用户(已调试)     有待考察               ---------6.5
     *
     * @param postId 职位id
     * @param orgIds 组织id
     * @return ReturnModel
     * <AUTHOR>
     * @date 2020/9/24 17:37
     */
    @GetMapping("/portal/position/find_user_by_postid.json")
    ReturnModel queryUserIdByPostIdAndOrgId(
            @RequestParam("postId") Integer postId,
            @RequestParam("orgIds") String orgIds
    );

    /**
     * Feign调用门户：根据用户组id查询部门leader(已调试)                    ---------6.5
     *
     * @param orgId 用户组id
     * @return ReturnModel
     */
    @GetMapping("/portal/organization/query_leader_by_orgid.json")
    ReturnModel queryLeaderByOrgId(
            @RequestParam("orgId") String orgId
    );

    /**
     * Feign调用门户：根据组织id查询组织信息(已调试)                    ---------3.5
     *
     * @param orgId 组织id
     * @return ReturnModel
     */
    @GetMapping("/portal/organization/query_org_by_id.json")
    ReturnModel queryOrgMsgByOrgId(
            @RequestParam("orgId") String orgId
    );

    /**
     * Feign调用门户：用户基础信息批量查询(已调试)                    -------3.22
     *
     * @param userIds 用户id
     * @return ReturnModel
     */
    @GetMapping("/portal/user/query_batch_user_info.json")
    ReturnModel queryMoreUserInfo(
            @RequestParam("userIds") String userIds
    );

    /**
     * Feign调用门户：获取审批审计用户可管理的组织列表(已调试)                    -------3.21
     *
     * @param userId 用户id
     * @return ReturnModel
     */
    @GetMapping("/portal/user/get_manage_org.json")
    ReturnModel getManageOrg(
            @RequestParam("userId") Long userId
    );

    /**
     * Feign调用门户：获取审批领导管理操作部门下的用户(已调试)                    -------3.26
     *
     * @param userId 用户id
     * @return ReturnModel
     */
    @GetMapping("/portal/user/get_approve_manage_org_leader.json")
    ReturnModel getApprovalLeaderManageOrg(
            @RequestParam("userId") Long userId
    );

    /**
     * Feign调用门户：根据职位id获取用户
     *
     * @param postId 用户id
     * @return ReturnModel
     */
    @GetMapping("/portal/position/find_user_by_postid.json")
    ReturnModel getUserByPostId(
            @RequestParam("postId") Integer postId
    );



}
