package com.semptian.feign.portal;

import com.semptian.base.service.ReturnModel;
import com.semptian.enums.ErrorCodeEnum;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Date: 2020/9/23 12:11
 * Description: Feign降级处理逻辑
 */
@Component
public class PortalFeignFallBackFactory implements PortalFeignClient {

    @Override
    public ReturnModel queryOrgByUserId(String userId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryUserById(String userId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryPositionByUserId(String userId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryUserByOrgIdAndPostId(String orgId, String positionIds) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryPermissionByPostId(String postId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryManagerId(String orgId, String positionIds) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryUserIdByPostIdAndOrgId(Integer postId, String orgIds) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryLeaderByOrgId(String orgId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryOrgMsgByOrgId(String orgId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel queryMoreUserInfo(String userId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel getManageOrg(Long userId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel getApprovalLeaderManageOrg(Long userId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }

    @Override
    public ReturnModel getUserByPostId(Integer postId) {
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SYSTEM_INNER_ERROR.getCode()).setMsg(ErrorCodeEnum.SYSTEM_INNER_ERROR.getMsg());
    }


}
