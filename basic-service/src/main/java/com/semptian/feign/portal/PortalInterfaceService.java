package com.semptian.feign.portal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.enums.UserPositionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 门户接口服务
 *
 * <AUTHOR>
 * @date 2020-09-24 11:26
 **/
@Slf4j
@Service
public class PortalInterfaceService {

    private static final int ZERO = 0;

    private static final int ONE = 1;

    private static final String ID = "id";

    private static final String LAST_ORG_ID = "lastOrgId";

    @Autowired
    private PortalFeignClient portalFeignClient;

    /**
     * 根据用户id查询用户信息
     *
     * @param userId
     * @return
     * <AUTHOR>
     * @date 2020/10/13
     */
    public Map<String, Object> getUserInfoByUserId(long userId) {
        ReturnModel returnModel = portalFeignClient.queryUserById(String.valueOf(userId));
        log.info("*****PortalInterfaceService:getUserInfoByUserId,result: {} ", JSON.toJSONString(returnModel));
        if (returnModel.getCode() == ONE && null != returnModel.getData()) {
            Map<String, Object> data = (Map<String, Object>) returnModel.getData();
            return data;
        }
        return null;
    }

    /**
     * 根据用户id获取用户所属职位
     *
     * @param userId 用户id
     * @return 职位
     * <AUTHOR>
     * @date 2020/9/24 11:53
     */
    public UserPositionEnum getUserPositionByUserId(long userId) {

        ReturnModel returnModel = portalFeignClient.queryPositionByUserId(String.valueOf(userId));
        log.info("*****PortalInterfaceService:getUserPositionByUserId,result: {} ", JSON.toJSONString(returnModel));
        if (returnModel == null || returnModel.getCode() != ONE || returnModel.getData() == null) {
            return null;
        }

        List<Map<String, Object>> mapList = (List<Map<String, Object>>) returnModel.getData();

        if (CollectionUtil.isEmpty(mapList)) {
            return null;
        }

        return UserPositionEnum.codeOf((Integer) mapList.get(ZERO).get(ID));

    }

    /**
     * 根据用户id获取组织id
     *
     * @param userId 用户id
     * @return 组织id
     * <AUTHOR>
     * @date 2020/9/24 17:02
     */
    public List<Long> getOrgLeaderIdByUserId(long userId) {

        ReturnModel returnModel = portalFeignClient.queryOrgByUserId(String.valueOf(userId));
        log.info("*****PortalInterfaceService:getOrgIdByUserId,result: {} ", JSON.toJSONString(returnModel));
        if (returnModel == null || returnModel.getCode() != ONE || returnModel.getData() == null) {
            return null;
        }

        List<Long> orgId = Lists.newArrayList();
        if (returnModel.getCode() == 1 && returnModel.getData() != null) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) returnModel.getData();
            for (Map<String, Object> map : data) {
                int type = (int) map.get("type");
                if (type == 1) {
                    orgId.add(Long.parseLong(String.valueOf(map.get("id"))));
                }
            }
        }

    return orgId;
    }

    /**
     *  根据用户id获取组织id
     *
     * @param userId
     * @return
     */
    public String getOrgIdByUserId(long userId) {

        ReturnModel returnModel = portalFeignClient.queryUserById(String.valueOf(userId));

        if (returnModel == null || returnModel.getCode() != ONE || returnModel.getData() == null) {
            return null;
        }

        Map<String, Object> map = (Map<String, Object>) returnModel.getData();

        if (CollectionUtil.isEmpty(map) || map.get(LAST_ORG_ID) == null) {
            return null;
        }

        return map.get(LAST_ORG_ID).toString();
    }

    /**
     * 根据组织id和职位获取具备审批权限的用户id
     *
     * @param orgId                组织id
     * @param userPositionEnumList 职位集合
     * @return 用户id集合
     * <AUTHOR>
     * @date 2020/9/24 17:05
     */
    public List<Long> getApprovalUserIdByOrgIdAndPositionList(
            String orgId,
            List<UserPositionEnum> userPositionEnumList
    ) {

        StringBuffer str = new StringBuffer();

        for (int i = 0; i < userPositionEnumList.size(); i++) {

            str.append(userPositionEnumList.get(i).getCode());

            if (i != userPositionEnumList.size() - 1) {
                str.append(",");
            }

        }

        ReturnModel returnModel = portalFeignClient.queryManagerId(orgId, str.toString());
        log.info("*****PortalInterfaceService:getApprovalUserIdByOrgIdAndPositionList,result: {} ", JSON.toJSONString(returnModel));
        if (returnModel == null || returnModel.getCode() != ONE || returnModel.getData() == null) {
            return null;
        }

        List<Map<String, Object>> mapList = (List<Map<String, Object>>) returnModel.getData();

        if (CollectionUtil.isEmpty(mapList)) {
            return null;
        }

        List<Long> userIds = Lists.newArrayList();

        for (Map<String, Object> map : mapList) {
            if (map.get(ID) != null) {
                userIds.add(Long.parseLong(String.valueOf(map.get(ID))));
            }
        }

        return userIds;

    }

    /**
     * 根据组织id和职位获取当前组织和职位下的用户id
     *
     * @param orgId            组织id
     * @param userPositionEnum 职位
     * @return 用户id集合
     * <AUTHOR>
     * @date 2020/9/24 17:05
     */
    public List<Long> getAgencyUserIdByOrgIdAndPosition(
            String orgId,
            UserPositionEnum userPositionEnum) {

        ReturnModel returnModel = portalFeignClient.queryUserIdByPostIdAndOrgId(userPositionEnum.getCode(), orgId);
        log.info("*****PortalInterfaceService:getAgencyUserIdByOrgIdAndPosition,result: {} ", JSON.toJSONString(returnModel));

        if (returnModel == null || returnModel.getCode() != ONE || returnModel.getData() == null) {
            return null;
        }

        List<Map<String, Object>> mapList = (List<Map<String, Object>>) returnModel.getData();

        if (CollectionUtil.isEmpty(mapList)) {
            return null;
        }

        List<Long> userIds = Lists.newArrayList();

        for (Map<String, Object> map : mapList) {
            if (map.get(ID) != null) {
                userIds.add(Long.parseLong(String.valueOf(map.get(ID))));
            }
        }

        return userIds;
    }

    /**
     * 根据组织ID获取组织信息
     * @param groupId
     * @return
     * <AUTHOR>
     * @date 2020/11/18
     */
    public Map<String, Object> getOrgInfoByOrgId(long groupId) {
        ReturnModel returnModel = portalFeignClient.queryOrgMsgByOrgId(String.valueOf(groupId));
        log.info("*****PortalInterfaceService:queryOrgMsgByOrgId, result: {} ", JSON.toJSONString(returnModel));
        if (returnModel.getCode() == ONE && null != returnModel.getData()) {
            Map<String, Object> data = (Map<String, Object>) returnModel.getData();
            return data;
        }
        return null;
    }

}
