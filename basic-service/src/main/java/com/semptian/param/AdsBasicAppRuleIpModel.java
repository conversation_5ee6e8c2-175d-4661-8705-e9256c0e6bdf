package com.semptian.param;

import com.semptian.entity.AppIpRuleEntity;
import lombok.Data;

/**
 * ads_basic_app_rule_ip表模型,通过DAT服务调用字段名称必须与数据库字段名称一致
 *
 * <AUTHOR>
 * @since 2024/10/24
 */

@Data
public class AdsBasicAppRuleIpModel {

    private String id;

    private String rule_id;

    private String ip;

    private Integer ip_type;

    private Integer port;

    private Integer behavior_num_sum = 0;

    private Long up_dateTime;

    private String insert_day;

    public AdsBasicAppRuleIpModel(AppIpRuleEntity appIpRuleEntity, String insertDay) {
        this.id = appIpRuleEntity.getId();
        this.rule_id = appIpRuleEntity.getRuleId();
        this.ip = appIpRuleEntity.getIp();
        this.ip_type = appIpRuleEntity.getIpType();
        this.port = appIpRuleEntity.getPort();
        this.up_dateTime = appIpRuleEntity.getUpdateTime();
        this.insert_day = insertDay;
    }
}
