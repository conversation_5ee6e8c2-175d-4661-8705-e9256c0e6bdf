package com.semptian.param;

import com.semptian.entity.ApplicationRuleEntity;
import lombok.Data;

/**
 * ads_basic_app_rule表模型,通过DAT服务调用字段名称必须与数据库字段名称一致
 *
 * <AUTHOR>
 * @since 2024/10/24
 */

@Data
public class AdsBasicAppRuleModel {

    private String id;

    private String name;

    private String rule;

    private String description;

    private Integer status;

    private String type;

    private Long source_type;

    private String user_id;

    private Integer behavior_num_sum = 0;

    private Long create_time;

    private Long update_time;

    private String insert_day;

    public AdsBasicAppRuleModel(ApplicationRuleEntity appRuleEntity, String insertDay) {
        this.id = appRuleEntity.getId();
        this.name = appRuleEntity.getName();
        this.rule = appRuleEntity.getRule();
        this.description = appRuleEntity.getDescription();
        this.status = appRuleEntity.getStatus();
        this.type = appRuleEntity.getType();
        this.source_type = Long.valueOf(appRuleEntity.getSourceType());
        this.user_id = String.valueOf(appRuleEntity.getUserId());
        this.create_time = appRuleEntity.getCreateTime();
        this.update_time = appRuleEntity.getUpdateTime();
        this.insert_day = insertDay;
    }
}
