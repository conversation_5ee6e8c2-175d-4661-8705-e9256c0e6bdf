package com.semptian.param;

import com.semptian.entity.DomainIpRuleEntity;
import lombok.Data;

/**
 * ads_basic_domain_rule_ip表模型,通过DAT服务调用字段名称必须与数据库字段名称一致
 *
 * <AUTHOR>
 * @since 2024/10/24
 */

@Data
public class AdsBasicDomainRuleIpModel {

    private String id;

    private String rule_id;

    private String ip;

    private Integer ip_type;

    private Integer port;

    private Integer behavior_num_sum = 0;

    private Long up_dateTime;

    private String insert_day;

    public AdsBasicDomainRuleIpModel(DomainIpRuleEntity domainIpRuleEntity, String insertDay) {
        this.id = domainIpRuleEntity.getId();
        this.rule_id = domainIpRuleEntity.getRuleId();
        this.ip = domainIpRuleEntity.getIp();
        this.ip_type = domainIpRuleEntity.getIpType();
        this.port = domainIpRuleEntity.getPort();
        this.up_dateTime = domainIpRuleEntity.getUpdateTime();
        this.insert_day = insertDay;
    }
}
