package com.semptian.param;

import com.semptian.entity.DomainRuleEntity;
import lombok.Data;

/**
 * ads_basic_domain_rule表模型,通过DAT服务调用字段名称必须与数据库字段名称一致
 *
 * <AUTHOR>
 * @since 2024/10/24
 */

@Data
public class AdsBasicDomainRuleModel {

    private String id;

    private String name;

    private String rule;

    private String description;

    private Integer status;

    private String type;

    private Long source_type;

    private String user_id;

    private Integer domain_level;

    private Integer behavior_num_sum = 0;

    private Long create_time;

    private Long update_time;

    private String insert_day;

    public AdsBasicDomainRuleModel(DomainRuleEntity domainRuleEntity, String insertDay) {
        this.id = domainRuleEntity.getId();
        this.name = domainRuleEntity.getName();
        this.rule = domainRuleEntity.getRule();
        this.description = domainRuleEntity.getDescription();
        this.status = domainRuleEntity.getStatus();
        this.type = domainRuleEntity.getType();
        this.source_type = Long.valueOf(domainRuleEntity.getSourceType());
        this.user_id = String.valueOf(domainRuleEntity.getUserId());
        this.domain_level = domainRuleEntity.getDomainLevel();
        this.create_time = domainRuleEntity.getCreateTime();
        this.update_time = domainRuleEntity.getUpdateTime();
        this.insert_day = insertDay;
    }
}
