package com.semptian.param;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 基站位置导入model
 *
 * <AUTHOR>
 * @since 2024/7/24
 */
@Data
public class BaseStationExcelModel {

    @ExcelProperty(value = "CGI", order = 1)
    private String cgi;

    @ExcelProperty(value = "Latitude", order = 2)
    private String latitude;

    @ExcelProperty(value = "Longitude", order = 3)
    private String longitude;

    @ExcelProperty(value = "CELLNAME", order = 4)
    private String cellName;

    @ExcelProperty(value = "Address", order = 5)
    private String stationAddress;

    @ExcelProperty(value = "Radius(m)", order = 6)
    private String coverageRadius;

    @ExcelProperty(value = "Description", order = 7)
    private String description;
}
