package com.semptian.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基站位置库导出参数
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseStationExportModel extends BaseStationQueryParamModel{

    /**
     * 0=csv;1=Excel;3=txt
     */
    @ApiModelProperty(value = "导出文件格式类型, 0=csv;1=Excel;3=txt", name = "exportType", required = true)
    private Integer exportType;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "导出数据ID, 如果是全部则为-1", name = "ids", required = true)
    private String ids;
}
