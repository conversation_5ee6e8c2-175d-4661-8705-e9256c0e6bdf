package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 基站位置信息库表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@ApiModel(value="BaseStationLocationInfo对象", description="基站位置信息库表")
public class BaseStationLocationModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "基站id")
    private Integer id;

    @ApiModelProperty(value = "基站编号")
    private String stationNo;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "覆盖半径")
    private BigDecimal coverageRadius;

    @ApiModelProperty(value = "网络类型,1=2G基站,2=3G基站,3=4G基站,4=5G基站,5=混合基站")
    private Integer networkType = 0;

    @ApiModelProperty(value = "基站地址")
    private String stationAddress;

    @ApiModelProperty(value = "等级,1=微小区基站,2=小区基站,3=分布式天线站,4=微站,5=宏站")
    private Integer grade = 0;

    @ApiModelProperty(value = "归属运营商,1=Mobilis,2=Ooredoo,3=Djezzy")
    private Integer networkOperator;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "删除状态,0=未删除,1=已删除,默认为0")
    private Integer isDel;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "更新时间")
    private Long modifyTime;

    @ApiModelProperty(value = "cellName")
    private String cellName;
}
