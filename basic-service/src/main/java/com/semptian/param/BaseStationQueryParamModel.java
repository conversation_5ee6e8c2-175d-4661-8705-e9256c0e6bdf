package com.semptian.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 基站位置信息列表查询参数
 * <AUTHOR>
 * @since  2024/3/20
 */

@Data
public class BaseStationQueryParamModel {

    @ApiModelProperty(value = "关键字,支持基站编号、基站地址、CELLNAME、描述字段进行模糊查询")
    private String keyword;

    @ApiModelProperty(value = "等级,1=微小区基站,2=小区基站,3=分布式天线站,4=微站,5=宏站")
    private Integer grade;

    @ApiModelProperty(value = "网络类型,1=2G基站,2=3G基站,3=4G基站,4=5G基站,5=混合基站")
    private Integer networkType;

    @ApiModelProperty(value = "更新时间开始")
    private Long modifyTimeStart;

    @ApiModelProperty(value = "更新时间结束")
    private Long modifyTimeEnd;

    @ApiModelProperty(value = "每页条数")
    private Integer size = 10;

    @ApiModelProperty(value = "当前页")
    private Integer onPage = 1;

    @ApiModelProperty(value = "排序字段")
    private String sortField = "modify_time";

    @ApiModelProperty(value = "排序类型 1=降序0=升序")
    private Integer sortType = 1;

    @ApiModelProperty(value = "运营商, 1=Mobilis,2=Ooredoo,3=Djezzy,0=未知。-1=全部")
    private Integer networkOperator;
}
