package com.semptian.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量更新状态参数
 * <AUTHOR>
 * @since  2024/3/20
 */

@Data
public class BatchUpdateStatusModel {

    /**
     * 主键ids
     */
    @ApiModelProperty(value = "主键id集合")
    private List<Integer> ids;

    /**
     * 状态,1=启用,2=停用
     */
    @ApiModelProperty(value = "状态,1=启用,2=停用")
    private Integer status;
}
