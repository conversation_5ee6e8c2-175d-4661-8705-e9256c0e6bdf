package com.semptian.param;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/12 14:57
 * Description: 固定IP档案基础模型
 */
@Data
@Builder
public class FixIpArchiveModel {

    /**
     * es文档ID
     */
    private String _id;

    /**
     * ID信息
     */
    private String id;

    /**
     * 名称
     */
    private String archive_name;

    /**
     * 认证账号类型
     */
    private String auth_account_type;

    /**
     * 档案类型
     */
    private Integer archive_type;

    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 默认排序评分（活跃度）
     */
    private Long sort_score;

    /**
     * 活跃次数
     */
    private Long behavior_num;

    /**
     * 固定IP实体类型：1:个人,2:企业
     */
    private Integer account_type;

    /**
     * 最近关联时间
     */
    private Long latest_relation_time;

    /**
     * 是否有文件,默认为0
     */
    private Integer file_flag;
}
