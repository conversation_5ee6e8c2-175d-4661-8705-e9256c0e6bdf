package com.semptian.param;

import lombok.Data;

import java.util.Date;

/**
 * @author: lmz
 * @description: 固定IP企业模板
 * @date: 2022/9/1 11:57
 */
@Data
public class FixedIpEnterpriseModel {

    /**
     * 固定IP 公共成员
     */
    /**
     * ip名称
     */
    private String ipName;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 实体类型：政企
     */
    private final static Integer entityType = 0;

    /**
     * 备注
     */
    private String remark;

    /**
     * 个人 私有成员
     */
    /**
     * 个人/政企名称
     */
    private String individualName;


    /**
     * 成立日期
     */
    private Date birth;

    /**
     * 证件/政企类型，0：护照/有限责任公司，1：户口本/股份责任公司
     */
    private Integer cardEnterpriseType;

    /**
     * 证件号码/政企代码
     */
    private String cardNumCode;

    /**
     * 住址/注册地址
     */
    private String address;

    /**
     * 工作地址/官网地址
     */
    private String workAddress;

    /**
     * 法人
     */
    private String juridicalPerson;

    /**
     * 联系电话/办公电话
     */
    private String phoneNum;

    /**
     * 政企规模，0：特大型、1：大型、2中型、3：小型、4：微型、5：其他
     */
    private Integer enterpriseScale;

    /**
     * 员工人数
     */
    private Integer enterpriseEmployeeNum;

    /**
     * 注册资本，单位：万
     */
    private Integer enterpriseRegisterCapital;
}
