package com.semptian.param;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: lmz
 * @description:
 * @date: 2022/9/1 12:02
 */
@Data
@ApiModel(value = "域名规则", description = "域名规则")
public class FixedIpModel {

    /**
     * 固定IP 公共成员
     */
    /**
     * ip名称
     */
    private String ipName;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 实体类型：1:个人,0:企业
     */
    private Integer entityType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 个人 私有成员
     */
    /**
     * 个人/政企名称
     */
    private String individualName;

    /**
     * 性别
     */
    private Integer personSex;

    /**
     * 出生日期
     */
    private Date birth;

    /**
     * 证件/政企类型，0：护照/有限责任公司，1：户口本/股份责任公司
     */
    private Integer cardEnterpriseType;

    /**
     * 证件号码/政企代码
     */
    private String cardNumCode;

    /**
     * 住址/注册地址
     */
    private String address;

    /**
     * 工作地址/官网地址
     */
    private String workAddress;

    /**
     * 法人
     */
    private String juridicalPerson;

    /**
     * 职位
     */
    private String position;

    /**
     * 学位，0：本科，1：硕士，2：博士，3：其他
     */
    private Integer personDegree;

    /**
     * 联系电话/办公电话
     */
    private String phoneNum;

    /**
     * 政治地位
     */
    private String personPoliticalStatus;

    /**
     * 国籍
     */
    private String nationalityIndustry;

    /**
     * 政企规模，0：特大型、1：大型、2中型、3：小型、4：微型、5：其他
     */
    private Integer enterpriseScale;

    /**
     * 员工人数
     */
    private Integer enterpriseEmployeeNum;

    /**
     * 注册资本，单位：万
     */
    private Integer enterpriseRegisterCapital;


    /**
     * 系统参数
     */

    /**
     * 入库时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 档案数据状态，0：统计中，1：为统计，默认0
     */
    private Integer archiveDataStatus;

    /**
     * 档案状态，0：已建档，1：建档失败，2：建档中，3：已删除，4：删除中，5：删除失败
     */
    private Integer archiveStatus;

    /**
     * 删除状态，0：未删除，1：已删除
     */
    private Integer deleteStatus;

}
