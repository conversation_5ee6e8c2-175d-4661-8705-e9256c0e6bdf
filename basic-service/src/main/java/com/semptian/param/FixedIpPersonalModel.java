package com.semptian.param;

import lombok.Data;

import java.util.Date;

/**
 * @author: lmz
 * @description: 固定IP个人模板
 * @date: 2022/9/1 11:56
 */
@Data
public class FixedIpPersonalModel {

    /**
     * 固定IP 公共成员
     */
    /**
     * ip名称
     */
    private String ipName;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 实体类型：个人
     */
    private final static Integer entityType = 1;

    /**
     * 备注
     */
    private String remark;

    /**
     * 个人 私有成员
     */
    /**
     * 个人/政企名称
     */
    private String individualName;

    /**
     * 性别
     */
    private Integer personSex;

    /**
     * 出生日期
     */
    private Date birth;

    /**
     * 证件/政企类型，0：护照/有限责任公司，1：户口本/股份责任公司
     */
    private Integer cardEnterpriseType;

    /**
     * 证件号码/政企代码
     */
    private String cardNumCode;

    /**
     * 住址/注册地址
     */
    private String address;

    /**
     * 工作地址/官网地址
     */
    private String workAddress;

    /**
     * 职位
     */
    private String position;

    /**
     * 学位，0：本科，1：硕士，2：博士，3：其他
     */
    private Integer personDegree;

    /**
     * 联系电话/办公电话
     */
    private String phoneNum;

    /**
     * 政治地位
     */
    private String personPoliticalStatus;

    /**
     * 国籍
     */
    private String nationalityIndustry;
}
