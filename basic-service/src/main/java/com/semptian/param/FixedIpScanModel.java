package com.semptian.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: lmz
 * @description:
 * @date: 2022/9/15 21:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FixedIpScanModel {

    private Long lastScheduledTime;
    private List<String> ipList;
    private Integer onPage;
    private Integer pageSize;
}
