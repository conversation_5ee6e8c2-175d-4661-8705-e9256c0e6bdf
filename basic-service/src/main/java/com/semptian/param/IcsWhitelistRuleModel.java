package com.semptian.param;

import lombok.Data;

import java.util.List;

/**
 * @Description: ICS白名单规则参数模型
 * @Date: Created on 2024/7/24.
 * @Author: <PERSON><PERSON><PERSON>
 */
@Data
public class IcsWhitelistRuleModel {

    /**
     * 操作类型。add-使能; del-去能; ls-列示
     */
    private String action;

    /**
     * 白名单号码
     */
    private List<String> id;

    public IcsWhitelistRuleModel() {}

    public IcsWhitelistRuleModel(String action, List<String> id) {
        this.action = action;
        this.id = id;
    }
}
