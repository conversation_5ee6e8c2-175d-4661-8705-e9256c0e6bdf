package com.semptian.param;

import com.semptian.entity.WhiteListEntity;
import com.semptian.enums.WhiteListScopeEnum;
import com.semptian.enums.WhiteListStatusEnum;
import com.semptian.enums.WhiteListTypeEnum;
import com.semptian.utils.Excel;
import com.semptian.utils.IPv6ParseUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 白名单导入模板
 * @date 2024/10/15
 */
@Data
public class IpWhiteListExcelModel {

    @Excel(name = "白名单内容",nameFr = "Contenu", nameEn = "Content")
    private String rule;

    @Excel(name = "备注",nameFr = "Remarques", nameEn = "Remarks")
    private String remark;

    public WhiteListEntity toEntity(String userId) {
        long currentTimeMillis = System.currentTimeMillis();
        String formatRule = rule;
        // 默认状态为在控
        int status = WhiteListStatusEnum.IN_CONTROL.getCode();
        if (rule.matches(IPv6ParseUtil.IPv6Reg)){
            // ipv6转小写和简写
            formatRule = IPv6ParseUtil.getShortIPv6(rule).toLowerCase();
        }

        return WhiteListEntity.builder()
                .rule(rule)
                .formatRule(formatRule)
                .type(WhiteListTypeEnum.IP.getCode())
                .effectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString())
                .remark(remark)
                .createUser(userId)
                .modifyUser(userId)
                .status(status)
                .createTime(currentTimeMillis)
                .modifyTime(currentTimeMillis)
                .build();
    }
}
