package com.semptian.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 号码参数接收对象
 *
 * @Author: sk
 * @Date: 2020/12/17 12:20
 */

@Data
public class NumberExportModel {


    private String countryCode;


    private String telephoneNum;


    private Integer phoneType;

    private String phoneTypeStr;

    private String fullNum;

    private String updateTime;


    private Integer phoneState;


    private String phoneStateStr;


    private String remark;







}
