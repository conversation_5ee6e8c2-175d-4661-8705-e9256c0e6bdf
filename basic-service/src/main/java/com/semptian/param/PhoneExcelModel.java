package com.semptian.param;

import com.semptian.utils.Excel;
import lombok.Data;

/**
 * @author: ZC
 * @date: 2021/2/26 14:34
 * 电话号码/特殊号码导入
 */
@Data
public class PhoneExcelModel {


    /**
     *  国家号
     */
    @Excel(name = "国家号",nameFr = "Le code du pays")
    private String countryCode;

    /**
     *  电话号码
     */
    @Excel(name = "电话号码",nameFr = "Numéro de téléphone")
    private String telephoneNum;

    /**
     *  号码类型-1：白名单  2：特殊号码
     */
    @Excel(name = "号码类型",nameFr = "Type de numéro")
    private Integer phoneType;

    /**
     *  备注
     */
    @Excel(name = "备注",nameFr = "Remarques")
    private String remark;



}
