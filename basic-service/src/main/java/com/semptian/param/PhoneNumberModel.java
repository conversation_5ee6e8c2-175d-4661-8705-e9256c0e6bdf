package com.semptian.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 号码参数接收对象
 *
 * @Author: sk
 * @Date: 2020/12/17 12:20
 */

@Data
@ApiModel(value = "号码对象", description = "号码对象")
public class PhoneNumberModel {

    /**
     * 国家区号
     */
    @ApiModelProperty(value = "国家区号", name = "countryCode",required = true)
    private String countryCode;

    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码", name = "telephoneNum",required = true)
    private String telephoneNum;

    /**
     * 号码类型 1:电话号码 2:特殊号码
     */
    @ApiModelProperty(value = "号码类型", name = "phoneType",required = true)
    private Integer phoneType;

    private String phoneTypeStr;

    /**
     * id号
     */
    @ApiModelProperty(value = "id号", name = "id",required = false)
    private Long id;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间",name = "createTime",required = false)
    private Long createTime;

    @ApiModelProperty(value = "更新时间",name = "updateTime",required = false)
    private Long updateTime;


    /**
     * 号码状态
     */
    @ApiModelProperty(value = "号码状态",name = "phoneState",required = false)
    private Integer phoneState;

    /**
     * 号码状态
     */
    @ApiModelProperty(value = "号码状态",name = "phoneStateStr",required = false)
    private String phoneStateStr;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注",name = "remark",required = false)
    private String remark;


    /**
     * 用户账号
     */
    @ApiModelProperty(value = "所属模块",name = "clueAddName",required = false)
    private String clueAddName;

    /**
     * 用户id
     */
    @JsonIgnore
    private Long userId;

    private Map<String,String> map ;

    private Integer toStatus;


}
