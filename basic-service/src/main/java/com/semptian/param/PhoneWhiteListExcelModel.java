package com.semptian.param;

import com.semptian.entity.WhiteListEntity;
import com.semptian.enums.WhiteListScopeEnum;
import com.semptian.enums.WhiteListStatusEnum;
import com.semptian.utils.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 白名单导入模板
 * @date 2024/10/15
 */
@Data
public class PhoneWhiteListExcelModel {

    @Excel(name = "国家码",nameFr = "Code pays", nameEn = "Country code")
    private String countryCode;

    @Excel(name = "白名单内容",nameFr = "Contenu", nameEn = "Content")
    private String rule;

    /**
     *  白名单类型: 1.ip  2.radius 3.mobile phone 4. fixed phone
     */
    @Excel(name = "白名单类型",nameFr = "Type", nameEn = "Type")
    private Integer type;

    /**
     *  生效范围：1：上网数据；2：Phone; 多个用逗号隔开
     */
    @Excel(name = "生效范围",nameFr = "Portée d'entrée en vigueur", nameEn = "Effective scope")
    private String effectiveScope;

    @Excel(name = "备注",nameFr = "Remarques", nameEn = "Remarks")
    private String remark;

    public WhiteListEntity toEntity(String userId) {
        long currentTimeMillis = System.currentTimeMillis();
        // 默认状态为在控
        int status = WhiteListStatusEnum.IN_CONTROL.getCode();
        // 手机号码格式化，需要加上国家码
        String formatRule =  countryCode + rule;
        if (effectiveScope.contains(WhiteListScopeEnum.PHONE.getCode().toString())){
            // 生效范围包含phone，则状态为在控中，等待YJ下发成功后设置为在控
            status = WhiteListStatusEnum.TO_BE_START.getCode();
        }

        return WhiteListEntity.builder()
                .countryCode(countryCode)
                .rule(rule)
                .formatRule(formatRule)
                .type(type)
                .effectiveScope(effectiveScope)
                .remark(remark)
                .createUser(userId)
                .modifyUser(userId)
                .status(status)
                .createTime(currentTimeMillis)
                .modifyTime(currentTimeMillis)
                .build();
    }
}
