package com.semptian.param;

import lombok.Data;

/**
 * 用户关联IP查询返回值model
 *
 * <AUTHOR>
 * @since  2024/5/20
 */
@Data
public class RelationUserIpModel {

    /**
     * 上网用户
     */
    private String userName;

    /**
     * 上网用户类型：1=移动Radius；2=固网Radius；3=固定IP；4=自定义账号
     */
    private String userType;

    /**
     * 查询端口
     */
    private String port;

    /**
     * 最早使用时间
     */
    private Long earliestRelationTime;

    /**
     * 最晚使用时间
     */
    private Long latestRelationTime;
}
