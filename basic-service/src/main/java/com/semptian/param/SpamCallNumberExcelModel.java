package com.semptian.param;

import com.semptian.utils.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Data
public class SpamCallNumberExcelModel {

    /**
     *  号码
     */
    @Excel(name = "号码" ,nameFr = "Numéro", nameEn = "Phone")
    private String phoneNumber;

    /**
     *  名称
     */
    @Excel(name = "名称",nameFr = "Nom", nameEn = "Name")
    private String name;

    /**
     * 号码类型
     */
    @Excel(name = "号码类型", nameFr = "Type de numéro", nameEn = "Phone type")
    private String type;

    /**
     * 备注
     */
    @Excel(name = "备注", nameFr = "Remarque", nameEn = "Remark")
    private String remark;
}
