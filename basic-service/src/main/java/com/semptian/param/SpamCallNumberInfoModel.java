package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 骚扰号码信息库表
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Data
@ApiModel(value="SpamCallNumberInfo对象", description="骚扰号码信息库表")
public class SpamCallNumberInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "号码,包含国家码,多个用逗号拼接,最多5个号码")
    private String phoneNumber;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "号码类型:1=服务商/公司公用电话,2=诈骗电话,3=广告推销电话,99=其它")
    private Integer type;

    @ApiModelProperty(value = "号码类型值")
    private String typeName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "删除状态,0=未删除,1=已删除,默认为0")
    private Integer isDel;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "更新时间")
    private Long modifyTime;
}
