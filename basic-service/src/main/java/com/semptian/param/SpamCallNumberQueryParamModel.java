package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 骚扰号码信息列表查询参数
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@ApiModel(value="SpamCallNumberInfo对象", description="骚扰号码信息库表")
public class SpamCallNumberQueryParamModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "号码")
    private String phoneNumber;

    @ApiModelProperty(value = "号码类型:1=服务商/公司公用电话,2=诈骗电话,3=广告推销电话,99=其它")
    private Integer type;

    @ApiModelProperty(value = "每页条数")
    private Integer size = 10;

    @ApiModelProperty(value = "当前页")
    private Integer onPage = 1;
}
