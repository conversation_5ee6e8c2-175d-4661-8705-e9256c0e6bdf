package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 上网用户分类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@ApiModel(value="UserCategory对象", description="上网用户分类")
public class UserCategoryModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "父级分类ID,默认为0")
    private Integer pid;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "关联上网用户数量")
    private Integer num;
}
