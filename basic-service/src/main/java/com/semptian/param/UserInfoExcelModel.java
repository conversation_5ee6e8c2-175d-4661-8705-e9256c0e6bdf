package com.semptian.param;

import com.semptian.utils.Excel;
import lombok.Data;

/**
 * 上网用户导入模板model
 *
 * <AUTHOR>
 * @since 2024/7/26
 */
@Data
public class UserInfoExcelModel {

    @Excel(name = "上网用户账号" ,nameFr = "Compte utilisateur Internet", nameEn = "Internet user account")
    private String userName;

    @Excel(name = "上网用户类型",nameFr = "Type d'internaute", nameEn = "Internet user type")
    private String userType;

    @Excel(name = "备注名称", nameFr = "Nom de la note", nameEn = "Remark name")
    private String remark;

    @Excel(name = "所属分类", nameFr = "catégorie", nameEn = "Belonging category")
    private String userCategoryId;
}
