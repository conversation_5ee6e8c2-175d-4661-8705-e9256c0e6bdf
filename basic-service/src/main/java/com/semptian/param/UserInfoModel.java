package com.semptian.param;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 上网用户信息库表
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Data
@ApiModel(value="UserInfo对象", description="上网用户信息库表")
public class UserInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "上网用户账号")
    private String userName;

    @ApiModelProperty(value = "用户类型 1=固网RADIUS账号,3=固定IP账号,2=移动网RADIUS账号,4=自定义用户（IP或者IP段）")
    private Integer userType;

    @ApiModelProperty(value = "用户所属分类名称")
    private String userTypeName;

    @ApiModelProperty(value = "备注名称")
    private String remark;

    @ApiModelProperty(value = "用户分类")
    private Integer userCategoryId;

    @ApiModelProperty(value = "用户分类名称")
    private String userCategoryName;

    @ApiModelProperty(value = "状态,1=启用,2=停用,默认为1")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;
}
