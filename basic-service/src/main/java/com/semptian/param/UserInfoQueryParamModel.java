package com.semptian.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上网用户信息列表查询参数
 * <AUTHOR>
 * @since  2024/3/20
 */

@Data
public class UserInfoQueryParamModel {

    /**
     * 模糊查询关键字，支持上网用户名称、备注名称
     */
    @ApiModelProperty(value = "模糊查询关键字，支持上网用户名称、备注名称")
    private String name;

    /**
     * 用户类型 1=固网RADIUS账号,3=固定IP账号,2=移动网RADIUS账号,4=自定义用户（IP或者IP段）
     */
    @ApiModelProperty(value = "用户类型 1=固网RADIUS账号,3=固定IP账号,2=移动网RADIUS账号,4=自定义用户（IP或者IP段）")
    private Integer userType;

    /**
     * 状态,1=启用,2=停用
     */
    @ApiModelProperty(value = "状态,1=启用,2=停用")
    private Integer status;

    /**
     * 用户分类ID
     */
    @ApiModelProperty(value = "用户分类ID")
    private Integer userCategoryId;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private Integer size = 10;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer onPage = 1;
}
