package com.semptian.param;

import com.semptian.entity.WhiteListEntity;
import com.semptian.enums.WhiteListScopeEnum;
import com.semptian.enums.WhiteListStatusEnum;
import com.semptian.enums.WhiteListTypeEnum;
import com.semptian.utils.IPv6ParseUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 白名单model
 * @date 2024/9/14
 */
@Data
@ApiModel(value = "白名单model", description = "新增、修改接口参数")
public class WhiteListModel {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "国家码")
    private String countryCode;

    @ApiModelProperty(value = "白名单内容")
    private String rule;

    @ApiModelProperty(value = "白名单类型：1.ip  2.radius 3.mobile phone 4. fixed phone")
    private Integer type;

    @ApiModelProperty(value = "生效范围：1：上网数据；2：Phone; 多个用逗号隔开")
    private String effectiveScope;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String createUser;

    @ApiModelProperty(value = "修改人", hidden = true)
    private String modifyUser;

    public WhiteListEntity toEntity() {
        long currentTimeMillis = System.currentTimeMillis();
        String formatRule = rule;
        // 默认状态为在控
        int status = WhiteListStatusEnum.IN_CONTROL.getCode();
        if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(type) || WhiteListTypeEnum.FIXED_PHONE.getCode().equals(type)){
            // 手机号码格式化，需要加上国家码
            formatRule =  countryCode + rule;
            if (effectiveScope.contains(WhiteListScopeEnum.PHONE.getCode().toString())){
                // 生效范围包含phone，则状态为在控中，等待YJ下发成功后设置为在控
                status = WhiteListStatusEnum.TO_BE_START.getCode();
            }
        }else if (WhiteListTypeEnum.IP.getCode().equals(type) && rule.matches(IPv6ParseUtil.IPv6Reg)){
            // ipv6转小写和简写
            formatRule = IPv6ParseUtil.getShortIPv6(rule).toLowerCase();
        }

        return WhiteListEntity.builder()
                .id(id)
                .countryCode(countryCode)
                .rule(rule)
                .formatRule(formatRule)
                .type(type)
                .effectiveScope(effectiveScope)
                .remark(remark)
                .createUser(createUser)
                .modifyUser(modifyUser)
                .status(status)
                .createTime(currentTimeMillis)
                .modifyTime(currentTimeMillis)
                .build();
    }
}
