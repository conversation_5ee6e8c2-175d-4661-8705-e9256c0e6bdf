package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 白名单查询接口参数
 * @date 2024/9/18
 */
@Data
@ApiModel(value = "白名单查询接口参数", description = "白名单查询接口参数")
public class WhiteListQueryParam {

    @ApiModelProperty(value = "查询关键字")
    private String keyword;

    @ApiModelProperty(value = "白名单类型：1.ip  2.radius 3.mobile phone 4. fixed phone", required = true)
    private List<Integer> type;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    @ApiModelProperty(value = "页码")
    private Integer onPage = 1;

    @ApiModelProperty(value = "每页大小")
    private Integer size = 20;

    @ApiModelProperty(value = "排序字段")
    private String orderField;

    @ApiModelProperty(value = "排序方式: 0.desc 1.asc")
    private Integer orderType;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String createUser;
}
