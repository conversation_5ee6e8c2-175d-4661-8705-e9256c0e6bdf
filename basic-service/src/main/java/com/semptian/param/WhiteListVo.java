package com.semptian.param;

import com.semptian.entity.WhiteListEntity;
import com.semptian.i18n.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 白名单vo
 * @date 2024/9/14
 */
@Data
@ApiModel(value = "白名单vo", description = "白名单列表页model")
public class WhiteListVo {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "国家码")
    private String countryCode;

    @ApiModelProperty(value = "白名单内容")
    private String rule;

    @ApiModelProperty(value = "白名单类型：1.ip  2.radius 3.mobile phone 4. fixed phone")
    private Integer type;

    @ApiModelProperty(value = "白名单状态")
    private Integer status;

    @ApiModelProperty(value = "白名单状态说明")
    private String statusStr;

    @ApiModelProperty(value = "生效范围：1：上网数据；2：Phone; 多个用逗号隔开")
    private String effectiveScope;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    public WhiteListVo() {}

    public WhiteListVo(WhiteListEntity entity) {
        this.id = entity.getId();
        this.countryCode = entity.getCountryCode();
        this.rule = entity.getRule();
        this.type = entity.getType();
        this.effectiveScope = entity.getEffectiveScope();
        this.createTime = entity.getCreateTime();
        this.remark = entity.getRemark();
        this.status = entity.getStatus();

        String statusStr = I18nUtils.getMessage("white.list.status.type." + entity.getStatus());
        this.statusStr = statusStr;
    }
}
