package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.AdslEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdslService extends IService<AdslEntity> {
    /**
     * 获取下一个序列
     * @return Long
     */
    Long getNextVal();

    /**
     * 判断上网账号是否存在
     * @return int
     */
    boolean checkOnlineAccountExist(Long id,String onlineAccount);

    void batchInsert(List<AdslEntity> list);
}
