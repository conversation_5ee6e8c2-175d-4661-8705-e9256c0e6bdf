package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.AppIpRuleEntity;
import com.semptian.entity.ApplicationRuleEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
public interface AppIpRuleService extends IService<AppIpRuleEntity> {
    List<String> isHot(String ip, String abbrIPv6, List<Integer> queryRange, Integer topSize);

    void insertIps(List<ApplicationRuleEntity> applicationRuleEntityList);

    void deleteByIdAndIP(String id, List<Map<String, Object>> needDeleteIps);
}
