package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.entity.AppIpRuleEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:07
 **/
public interface ApplicationRuleService {
    Object queryAppRuleList(String keyword, String ruleName, Integer status, Integer size, Integer onPage, String orderField, Integer orderType, List<Integer> queryRange, String lang);

    Object addAppRule(String name, String description, String ip, String appType, String userId);

    Object updateAppRule(String id, String name, String description, String rule, String type, String userId);

    Object deleteAppRule(String ids);

    Object importAppRules(MultipartFile file, String lang, String userId, HttpServletResponse response);

    List<Map<String, Object>> getAppTypeList();

    Page<AppIpRuleEntity> queryIpList(String id, Integer size, Integer onPage);
}
