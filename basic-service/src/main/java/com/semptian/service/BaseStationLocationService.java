package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.BaseStationLocationEntity;
import com.semptian.param.BaseStationExportModel;
import com.semptian.param.BaseStationLocationModel;
import com.semptian.param.BaseStationQueryParamModel;
import com.semptian.param.DeleteModel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 基站位置信息库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface BaseStationLocationService extends IService<BaseStationLocationEntity> {

    /**
     * 根据坐标点经纬度查询基站信息
     * @param downLeftLat 左下纬度
     * @param downLeftLon 左下经度
     * @param topRightLat 右上纬度
     * @param topRightLon 右上经度
     * @param size 查询数量
     * @return 查询结果
     */
    List<BaseStationLocationEntity> queryStationByLocation(Float downLeftLat, Float downLeftLon, Float topRightLat, Float topRightLon, Integer size);

    List<BaseStationLocationEntity> recommendStationByNumber(String keyword, Integer size);

    /**
     * 添加基站位置信息
     * @param baseStationModel 基站位置信息
     * @return 新增结果
     */
    Object add(BaseStationLocationModel baseStationModel);

    /**
     * 修改基站位置信息
     * @param baseStationModel 基站位置信息
     * @return 修改结果
     */
    Object update(BaseStationLocationModel baseStationModel);

    /**
     * 删除基站位置信息
     * @param deleteModel 删除参数
     * @return 删除结果
     */
    Object delete(DeleteModel deleteModel);

    /**
     * 查询基站位置信息
     * @param baseStationQueryParamModel 查询参数
     * @return 查询结果
     */
    Object list(BaseStationQueryParamModel baseStationQueryParamModel);

    /**
     * 导出基站位置信息
     * @param model 导出参数
     * @param response 响应
     */
    Object export(BaseStationExportModel model, HttpServletResponse response);

    /**
     *  批量导入骚扰号码
     * @param file 导入文件
     * @param userId 上传用户ID
     * @param type 语言类型
     * @return 导入结果
     */
    Object importBatch(MultipartFile file, Integer userId, Integer type);

    /**
     * 发送批量导入开始消息
     * @param userId 用户ID
     */
    void sendStartMessage(Integer userId);
}
