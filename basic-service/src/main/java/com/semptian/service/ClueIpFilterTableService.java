package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.ClueIpFilterTableEntity;
import com.semptian.param.UpdateIpModel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: ZC
 * @date: 2020/12/17 14:57
 */
public interface ClueIpFilterTableService extends IService<ClueIpFilterTableEntity> {

    /**
     * 新增ip白名单
     *
     * @param clueIpFilterTableEntity
     * @param userId
     * @return
     */
    Object insertIpWhite(ClueIpFilterTableEntity clueIpFilterTableEntity, Integer userId);

    /**
     * 删除ip白名单
     *
     * @param ids
     * @return
     */
    Object deleteIpWhite(String ids);

    /**
     * 修改ip白名单状态
     *
     * @param updateIpModel
     * @return
     */
    Object updateIpWhiteState(UpdateIpModel updateIpModel);

    /**
     * 查询
     * @param ipAddr
     * @param startTime
     * @param endTime
     * @param onPage
     * @param size
     * @param orderField
     * @param orderType
     * @return
     */
    Object selectIpWhiteMsg(String ipAddr, Long startTime, Long endTime, Integer onPage, Integer size, String orderField ,Integer orderType);

    /**
     * 编辑白名单
     *
     * @param clueIpFilterTableEntity
     * @return
     */
    Object updateIpWhiteMsg(ClueIpFilterTableEntity clueIpFilterTableEntity);

    /**
     * 查看详情
     *
     * @param clueId
     * @return
     */
    Object selectIpById(Integer clueId);

    /**
     * 校验ip/MAC
     *
     * @param ipAddr
     * @return
     */
    Object ipWhiteCheck( String ipAddr ,  String clueId);

    /**
     * 校验 Mask
     *
     * @param ipMask
     * @return
     */
    Object maskCheck(String ipMask);

    /**
     * 导出
     *
     * @param ids
     * @param type 0以CSV  1以Excel  2以TXT
     * @return
     */
    Object ipMsgExport(String ids, Integer type ,HttpServletResponse response);

    /**
     *  批量导入IP白名单
     * @param file
     * @param userId
     * @param type
     * @return
     */
    Object importBatch(MultipartFile file, Integer userId,Integer type);


    /**
     *  监控过期IP并设置成停控
     */
    void ipExpireMonitor();

}
