package com.semptian.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.ClueEntity;
import com.semptian.param.PhoneNumberModel;

import java.util.List;

/**
 * 线索数据库服务
 *
 * @Author: sk
 * @date: 2020/12/15
 * @params:
 */

public interface ClueService extends IService<ClueEntity> {

    int queryWhiteNumberCount(String keyword,Long startTime,Long endTime );

    List<ClueDto> queryWhiteNumberList(String keyword, int onPage, int size, String orderField, int orderType,Long startTime,Long endTime );

    List<ClueDto> queryOrdinaryNumberList(String keyword, int onPage, int size, String orderField, int orderType, Long startTime,Long endTime);

    int queryOrdinaryNumberCount(String keyword,Long startTime,Long endTime);


    boolean checkOrdinaryNumber(String telephoneNum, String countryCode, Long id);

    Long nextVal();

    ClueDto queryOrdinaryNumberDetail(Long id);


    void modifyNumber(PhoneNumberModel phoneNumberModel);


    boolean updateBatchByStatus(List<Long> ids,Integer toStatus);

    List<ClueDto> selectListByClueIds(List<Long> ids);

    List<ClueDto> selectAllExport();

    List<ClueDto> selectOrdinaryExport();


    List<ClueDto> selectOrdinaryByIdsExport(List<Long> numberIds);

    List<ClueDto> getByIdsExport(List<Long> numberIds,List<Long> specialIds);
}
