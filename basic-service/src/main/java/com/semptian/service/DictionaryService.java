package com.semptian.service;

import com.semptian.base.service.ReturnModel;
import com.semptian.entity.DictionaryEntity;
import com.semptian.entity.I18nDataModel;
import com.semptian.entity.I18nKeywordModel;
import com.semptian.entity.PageQueryModel;
import java.util.List;
import java.util.Map;

/**
 * @author: SunQi
 * @create: 2021/01/04
 * desc:
 **/
public interface DictionaryService {

    /**
     * 根据lang、type、model_type、code查询国际化值,多个code用逗号隔开
     *
     * @param pageQueryModel
     * @return
     */
    ReturnModel queryI18nValue(PageQueryModel pageQueryModel);

    /**
     * 根据关键字模糊查询国际化值
     *
     * @param pageQueryModel
     * @return
     */
    ReturnModel queryI18nValueByKeyword(PageQueryModel pageQueryModel);

    /**
     * 新增或更新国际化字典数据
     *
     * @param dictionaryEntity
     * @return
     */
    ReturnModel addOrUpdateDictionary(DictionaryEntity dictionaryEntity);


    /**
     * 根据返回值中的value和lang批量查询获取国际化后的值
     *
     * @param defaultLang 默认语言，与数据库中数据存储时语言一致。
     * @param lang        国际化语言
     * @param values      国际化前的值
     * @return 国际化后的值 key=国际化前的值，value=国际化后的值
     */
    Map<String, String> getValueByLang(String type, String defaultLang, String lang, List<String> values);

    /**
     * 获取国家化后的返回值
     *
     * @param model 国际化模型
     * @return 国际化后的返回值
     */
    Object getI18nData(I18nDataModel model);


    /**
     * 根据原始关键字的值和原始语言获取该关键字的所有语言的值
     * @param keywordModel
     * @return
     */
    ReturnModel getI18nValueByKeyword(I18nKeywordModel keywordModel);


    /**
     * 获取应用分类
     * @return
     */
    Object getAppTypeList();
}
