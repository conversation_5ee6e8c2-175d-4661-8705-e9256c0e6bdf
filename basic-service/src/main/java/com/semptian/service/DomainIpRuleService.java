package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.DomainIpRuleEntity;
import com.semptian.entity.DomainRuleEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
public interface DomainIpRuleService extends IService<DomainIpRuleEntity> {
    List<String> isHot(String ip, String abbrIpv6, List<Integer> queryRange, Integer topSize);

    List<String> hotFilter(List<String> domainList, Integer topSize);

    void insertIps(List<DomainRuleEntity> domainRuleList);
}
