package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.entity.DomainIpRuleEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-09 19:37
 **/
public interface DomainRuleService {
    Object newQueryDomainList(String keyword, String queryIp, Integer size, Integer onPage, String orderField, Integer orderType, Integer queryRange);

    Object addDomainRule(String name, String ip, Integer domainType, String userId);

    Object updateDomainRule(String id, String name, String ip, Integer domainType, String userId);

    Object importDomainRules(MultipartFile file, String s, String lang, HttpServletResponse response);

    Object deleteDomainRule(String ids);

    Object changeStatus(String id, Integer status);

    Page<DomainIpRuleEntity> queryIpList(String id, Integer size, Integer onPage);

    Object queryIpByNameList(List<String> nameList);

}
