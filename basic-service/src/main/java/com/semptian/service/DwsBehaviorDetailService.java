package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.bo.DomainBo;
import com.semptian.bo.IpRuleBo;
import com.semptian.bo.RuleBo;
import com.semptian.entity.DomainIpRuleEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Clickhouse DwsBehaviorDetail Service类
 * <AUTHOR>
 * @date 2022-05-25 16:27
 **/
public interface DwsBehaviorDetailService {

    /**
     * 查询应用数目
     */
    Integer queryAppNum();

    /**
     * 查询应用(会过滤掉包含Other的)
     */
    List<RuleBo> queryApp(Integer startNum, Integer pageNum);

    /**
     * 查询热门三级域名
     */
    List<String> queryHotTertiaryDomain();

    /**
     * 查询热门二级域名
     */
    List<String> queryHotSecondaryDomain();

    /**
     * 查询应用对应的热门IP
     */
    List<IpRuleBo> queryHotIpForApp(List<String> appNameList);

    /**
     * 查询三级域名对应的热门IP
     */
    List<IpRuleBo> queryHotIpForTertiaryDomain(List<String> domainList);

    /**
     * 查询二级域名对应的热门IP
     */
    List<IpRuleBo> queryHotIpForSecondaryDomain(List<String> domainList);

    /**
     * 插入三级域名数据到中间表
     */
    void insertHotTertiaryDomain(int domainLevel);

    /**
     * 插入二级域名数据到中间表
     */
    void insertHotSecondaryDomain(int domainLevel);

    /**
     * 插入ck 热门ip表 二级域名
     */
    void insertHotIpForSecondaryDomain(int domainLevel);

    /**
     * 插入ck 热门ip表 三级级域名
     */
    void insertHotIpForTertiaryDomain(int domainLevel);
    /**
     * 查询中间表待插入orale domain数量 与ip表关联
     */
    long selectSaveDomainCount(int domainLevel);

    /**
     * 查询需插入的domain信息
     * @param onPage
     * @param size
     */
    List<DomainBo> selectSaveDomainList(int domainLevel, int onPage, int size);

    /**
     * 根据domain数组更新三级域名ip
     */
    void insertHotIpForTertiaryDomainByDomainList(List<String> presetDomainList);

    /**
     *根据domain数组更新二级域名ip
     */
    void insertHotIpForSecondaryDomainByDomainList(List<String> presetDomainList);

    /**
     * 删除domain ck中间表数据
     */
    void deleteCkDomainDate();

    /**
     * 删除除今天所有的ip历史ck数据
     */
    void deleteCkDomainIpData();

    /**
     * 查询今天的域名ip数据
     */
    long selectTodayDomainIpCount();

    /**
     * 模糊查询ip命中的域名
     */
    List<String> selectDoMainListByKeyword(String keyword,String abbrIPv6,Integer onPage,Integer pageSize);

    /**
     * 查询域名对应的top ip
     */
    List<Map<String,String>> selectTop(Set<String> names, int top);

    /**
     * 查询域名对应的top100 ip
     * @param name
     * @return
     */
    List<DomainIpRuleEntity> queryIpList(String name);

    /**
     * 模糊查询ip命中的域名数量
     */
    Integer selectDoMainListByKeywordCount(String keyword, String abbrIPv6);

    /**
     * 精确查询ip对应的域名
     */
    List<String> selectDomainListByIp(String ip, String abbrIPv6);

    List<String> selectDomainsByDomainList(List<String> presetDomainList);

    void insertHistoryDomainIp(List<String> newList);

    /**
     * 查询domainck表数据count
     */
    long selectDomainCount();

    List<DomainIpRuleEntity> queryIpByNameList(List<String> nameList);
}
