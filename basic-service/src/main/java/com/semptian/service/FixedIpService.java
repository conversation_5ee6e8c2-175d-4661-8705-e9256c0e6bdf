package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.FixedIpEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 固定IP知识库接口
 * @date: 2022/9/9 11:47
 */
public interface FixedIpService extends IService<FixedIpEntity> {

    /**
     * 添加固定Ip
     * @param fixedIpEntity
     * @param lang
     * @return
     */
    ReturnModel saveFixedIp(FixedIpEntity fixedIpEntity,String lang);

    /**
     * 固定IP知识库列表查询
     * @param userId
     * @param onPage
     * @param size
     * @param deleteStatus
     * @param sortField
     * @param sortType
     * @param entityType
     * @param keyword
     * @param archiveStatus
     * @return
     */
    ReturnModel fixedIpList(String userId, Integer onPage, Integer size, Integer deleteStatus, String sortField, Integer sortType, Integer entityType, String keyword, Integer archiveStatus);

    /**
     * 批量删除
     * @param ipAddress
     * @return
     */
    ReturnModel deleteBatchIp(String ipAddress);

    /**
     * 知识库更新
     * @param fixedIpEntity
     * @param lang
     * @return
     */
    ReturnModel updateArcIp(FixedIpEntity fixedIpEntity,String lang);

    /**
     * 查询已删除的ip
     * @param size
     * @return
     */
    ReturnModel queryIpAddressList(Integer size);

    /**
     * 导出下载模板
     * @param entityType
     * @param lang
     * @param response
     * @return
     */
    void fixedIpModel(Integer entityType, String lang, HttpServletResponse response);

    /**
     * 批量导入接口
     * @param entityType
     * @param lang
     * @param file
     * @param response
     * @return
     */
    ReturnModel importFixedIps(Integer entityType, String lang, MultipartFile file,HttpServletResponse response);

    /**
     * 固定Ip定时1小时查询接口
     * @param lastScheduledTime
     * @param ipList
     * @param onPage
     * @param pageSize
     * @return
     */
    ReturnModel scanIpTaskInfo(Long lastScheduledTime, List<String> ipList, Integer onPage, Integer pageSize);

    /**
     * 固定IP知识库新增确认ip是否会关联历史数据
     * @param ipAddress
     * @return
     */
    ReturnModel repairIpDatabaseInfo(String ipAddress);

    /**
     * 实时查询ip状态---关注、已建档、未建档
     * @param ip
     * @return
     */
    ReturnModel queryFixedIpStatus(String ip);

    /**
     * 根据IP查询固定IP用户信息(2024.05.21 当前提供给统计分析系统使用)
     * @param ip ip信息
     * @param startDay 查询开始日期
     * @param endDay 查询结束日期
     * @return ip关联固定IP用户信息
     */
    ReturnModel<?> queryFixedIpUser(String ip, String startDay, String endDay);
}
