package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.NumberSectionEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface NumberSectionService extends IService<NumberSectionEntity> {
    /**
     * 获取序列值
     * @return Long
     */
    Long getNextVal();

    boolean checkMsisdnExist(Long id, String msisdn);

    void batchInsert(List list);
}
