package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.SpamCallNumberInfoEntity;
import com.semptian.param.DeleteModel;
import com.semptian.param.SpamCallNumberInfoModel;
import com.semptian.param.SpamCallNumberQueryParamModel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 骚扰号码信息库表 服务类
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
public interface SpamCallNumberInfoService extends IService<SpamCallNumberInfoEntity> {

    /**
     * 骚扰号码添加
     * @param spamCallNumberInfoModel 骚扰号码信息
     * @return 新增结果
     */
    Object add(SpamCallNumberInfoModel spamCallNumberInfoModel);

    /**
     * 骚扰号码修改
     * @param spamCallNumberInfoModel 骚扰号码信息
     * @return 修改结果
     */
    Object update(SpamCallNumberInfoModel spamCallNumberInfoModel);

    /**
     * 骚扰号码批量删除
     * @param deleteModel 批量删除参数
     * @return 删除结果
     */
    Object delete(DeleteModel deleteModel);

    /**
     * 骚扰号码列表
     * @param spamCallNumberQueryParamModel 骚扰号码查询参数
     * @return 骚扰号码列表
     */
    Object list(SpamCallNumberQueryParamModel spamCallNumberQueryParamModel);

    /**
     * 号码国际区号列表
     * @return 号码国际区号列表
     */
    Object phoneCodeList();

    /**
     *  批量导入骚扰号码
     * @param file 导入文件
     * @param userId 上传用户ID
     * @param type 语言类型
     * @return 导入结果
     */
    Object importBatch(MultipartFile file, Integer userId, Integer type, HttpServletResponse response);
}
