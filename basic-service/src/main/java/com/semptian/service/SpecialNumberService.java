package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.SpecialNumberEntity;


import java.util.List;

/**
 * 特殊号码数据库服务
 *
 * @Author: sk
 * @Date: 2020/12/17 14:55
 */
public interface SpecialNumberService extends IService<SpecialNumberEntity> {

    int querySpecialNumberCount(String keyword,Long startTime,Long endTime);

    List<ClueDto> querySpecialNumberList(String keyword, int onPage, int size, String orderField, int orderType, Long startTime,Long endTime);

    boolean checkSpecialNumber(String telephoneNum, String countryCode, Long id);

    ClueDto querySpecialNumberDetail(Long id);

    Long getNextVal();

    boolean updateBatchByStatus(List<Long> specialList, Integer toStatus);

    List<ClueDto> selectOrdinaryExport();

    List<ClueDto> selectOrdinaryByIdsExport(List<Long>  specialIds);

    List<String> getALLOn();
}
