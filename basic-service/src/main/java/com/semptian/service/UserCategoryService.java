package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.UserCategoryEntity;
import com.semptian.entity.UserInfoEntity;
import com.semptian.param.DeleteModel;
import com.semptian.param.UserCategoryModel;

import java.util.List;

/**
 * <p>
 * 上网用户分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface UserCategoryService extends IService<UserCategoryEntity> {

    /**
     * 添加用户分类
     * @param userCategoryModel 用户分类
     * @return 用户分类
     */
    Object add(UserCategoryModel userCategoryModel);

    /**
     * 修改用户分类
     * @param userCategoryModel 用户分类
     * @return 用户分类
     */
    Object update(UserCategoryModel userCategoryModel);

    /**
     * 删除用户分类
     * @param deleteModel 删除参数
     * @return 用户分类
     */
    Object delete(DeleteModel deleteModel);

    /**
     * 树形结构
     * @param name 名称
     * @return 用户分类
     */
    Object tree(String name);

    /**
     * 获取用户分类名称列表
     * @param userInfoEntity 用户信息
     * @return 用户分类名称列表
     */
    List<String> getUserCategoryNameList(UserInfoEntity userInfoEntity);

    /**
     * 获取用户分类名称列表
     * @param userInfoEntity 用户信息
     * @param onlyShowCurrentNode 是否只显示当前节点
     * @return 用户分类名称列表
     */
    List<String> getUserCategoryNameList(UserInfoEntity userInfoEntity, boolean onlyShowCurrentNode);

    /**
     * 获取所有最后一级用户分类
     * @return 用户分类
     */
    List<UserCategoryEntity> getAllLastUserCategoryList();
}
