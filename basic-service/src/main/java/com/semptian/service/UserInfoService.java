package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.dto.usercategory.UserCategoryCountDto;
import com.semptian.entity.UserInfoEntity;
import com.semptian.param.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 上网用户信息库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface UserInfoService extends IService<UserInfoEntity> {

    /**
     * 添加用户信息
     * @param userInfoModel 用户信息
     * @return 用户信息
     */
    Object add(UserInfoModel userInfoModel);

    /**
     * 修改用户信息
     * @param userInfoModel 用户信息
     * @return 用户信息
     */
    Object update(UserInfoModel userInfoModel);

    /**
     * 删除用户信息
     * @param deleteModel 删除参数
     * @return 用户信息
     */
    Object delete(DeleteModel deleteModel);

    /**
     * 批量启用/停用
     * @param batchUpdateStatusModel 批量修改用户状态
     * @return 用户信息
     */
    Object status(BatchUpdateStatusModel batchUpdateStatusModel);

    /**
     * 用户信息列表分页查询
     * @param userInfoQueryModel 用户信息查询参数
     * @return 用户信息
     */
    Object list(UserInfoQueryParamModel userInfoQueryModel);

    /**
     * 根据档案账号和档案类型查询是否为重点目标
     * @param arcType 档案类型
     * @param arcAccount 档案账号
     * @return 是否为重点目标
     */
    Object isImportantTarget(Integer arcType, String arcAccount);

    /**
     * 根据重要目标和重要目标类型查询重点目标分类
     * @param importantTargetList 重要目标和重要目标
     * @return 重点目标分类
     */
    Object getImportantTargetCategoryByBatch(List<ImportantTargetModel> importantTargetList);

    /**
     * 获取所有用户分类及数量
     * @return 用户分类及数量
     */
    List<UserCategoryCountDto> getAllUserCategoryList();

    /**
     * 根据关键字模糊查询用户信息
     * @param keyword 关键字
     * @return 上网用户信息
     */
    Object getUserInfoByKeyword(String keyword);

    /**
     *  批量导入骚扰号码
     * @param file 导入文件
     * @param userId 上传用户ID
     * @param type 语言类型
     * @return
     */
    Object importBatch(MultipartFile file, Integer userId, Integer type, HttpServletResponse response);
}
