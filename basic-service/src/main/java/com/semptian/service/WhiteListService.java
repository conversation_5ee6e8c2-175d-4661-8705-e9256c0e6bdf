package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.WhiteListEntity;
import com.semptian.param.BaseStationExportModel;
import com.semptian.param.WhiteListModel;
import com.semptian.param.WhiteListQueryParam;
import com.semptian.param.WhiteListVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 白名单service
 * @date 2024/9/14
 */
public interface WhiteListService extends IService<WhiteListEntity> {

    ReturnModel addOrUpdate(WhiteListModel param);

    ReturnModel updateStatus(List<Long> ids, Integer status);

    void issuedWhiteListToYJ(List<WhiteListEntity> whiteLists, Integer status);

    Map<String, Object> queryList(WhiteListQueryParam queryParam);

    Object importBatch(MultipartFile file, String userId, Integer whiteListType, Integer langType);

    /**
     * 导出白名单信息
     * @param exportType 导出文件格式类型, 0=csv;1=Excel;3=txt
     * @param ids 导出数据ID, 如果是全部则为-1
     */
    void export(Integer exportType, List<Integer> whiteListTypes,String ids, HttpServletResponse response);

    /**
     * 同步所有phone类型白名单到YJ
     */
    void syncAllWhiteList();
}
