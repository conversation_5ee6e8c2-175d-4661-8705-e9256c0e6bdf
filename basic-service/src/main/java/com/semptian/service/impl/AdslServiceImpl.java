package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.AdslEntity;
import com.semptian.mapper.AdslMapper;
import com.semptian.service.AdslService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AdslServiceImpl  extends ServiceImpl<AdslMapper, AdslEntity> implements AdslService {
    @Override
    public Long getNextVal() {
        return this.baseMapper.getNextVal();
    }

    @Override
    public boolean checkOnlineAccountExist(Long id,String onlineAccount) {
        return this.baseMapper.checkOnlineAccountExist(id,onlineAccount) > 0;
    }

    @Override
    public void batchInsert(List<AdslEntity> list) {
        this.baseMapper.batchInsert(list);
    }
}
