package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.util.JsonUtil;
import com.semptian.entity.AppIpRuleEntity;
import com.semptian.entity.ApplicationRuleEntity;
import com.semptian.mapper.AppIpRuleMapper;
import com.semptian.param.AdsBasicAppRuleIpModel;
import com.semptian.service.AppIpRuleService;
import com.semptian.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@Slf4j
@Service
public class AppIpRuleServiceImpl extends ServiceImpl<AppIpRuleMapper, AppIpRuleEntity> implements AppIpRuleService {

    private static final String ADS_APP_RULE_IP_TABLE = "ads_basic_app_rule_ip";

    @Resource
    private AppIpRuleMapper appIpRuleMapper;

    @Resource
    private DorisDatService dorisDatService;

    /**
     * 用户添加的数据,指定特定分区日期
     */
    @Value("${custom.data.partition:2024-01-01}")
    private String customDataPartition;

    @Override
    public List<String> isHot(String ip, String abbrIpv6, List<Integer> queryRange, Integer topSize) {
        List<String> res = Lists.newArrayList();
        try {
            res = appIpRuleMapper.isHot(ip.toLowerCase(), abbrIpv6, queryRange);
        } catch (Exception e) {
            log.error("query isHotApp error, ex:{}, ip:{}", e.getMessage(), ip);
        }
        return res.size() > topSize ? res.subList(0, topSize) : res;
    }

    /**
     * 根据对应的域名或者应用数据ID，插入对应的ip信息到表中
     *
     * @param applicationRuleEntityList    应用规则list
     */
    @Override
    public void insertIps(List<ApplicationRuleEntity> applicationRuleEntityList) {
        if (CollUtil.isEmpty(applicationRuleEntityList)) {
            return;
        }

        List<AppIpRuleEntity> list = new ArrayList<>();

        for (ApplicationRuleEntity applicationRuleEntity : applicationRuleEntityList) {
            String ips = applicationRuleEntity.getIp();
            String ruleId = applicationRuleEntity.getId();

            if (StringUtils.isBlank(ips)) {
                continue;
            }

            //解析ips，ip格式后面可能带有端口号

            String[] split = ips.split(",");
            Set<String> ipSet = new HashSet<>(Arrays.asList(split));

            //将IP插入到域名IP表中
            for (String s : ipSet) {
                AppIpRuleEntity appIpRuleEntity = new AppIpRuleEntity();
                appIpRuleEntity.setId(IdUtil.simpleUUID());
                appIpRuleEntity.setRuleId(ruleId);
                appIpRuleEntity.setUpdateTime(System.currentTimeMillis());
                String[] array = IpUtil.parseIPAndPort(s);
                if (array != null) {
                    String ip = array[0];
                    String port = array[1];
                    boolean b = IpUtil.isipv6(ip);
                    appIpRuleEntity.setIp(ip);
                    appIpRuleEntity.setPort(StringUtils.isBlank(port) ? -1 : Integer.parseInt(port));
                    if (b) {
                        appIpRuleEntity.setIpType(1);
                    } else {
                        appIpRuleEntity.setIpType(0);
                    }
                    list.add(appIpRuleEntity);
                }
            }
        }

        if (CollectionUtil.isNotEmpty(list)) {
            List<AdsBasicAppRuleIpModel> ruleIpModelList = list.stream().map(item -> new AdsBasicAppRuleIpModel(item, customDataPartition)).collect(Collectors.toList());
            boolean insert = dorisDatService.insertBatch(ADS_APP_RULE_IP_TABLE, ruleIpModelList);
            if (!insert) {
                log.error("insert domain ip error, data:{}", JsonUtil.toJsonString(list));
            }
        }
    }

    @Override
    public void deleteByIdAndIP(String id, List<Map<String, Object>> needDeleteIps) {
        appIpRuleMapper.deleteByIdAndIP(id, needDeleteIps);
    }
}
