package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.meiya.whalex.interior.db.search.condition.Rel;
import com.semptian.base.model.WhereParam;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.entity.AppIpRuleEntity;
import com.semptian.entity.ApplicationRuleEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.ApplicationRuleMapper;
import com.semptian.param.AdsBasicAppRuleModel;
import com.semptian.param.ImportExcelErrorMsgModel;
import com.semptian.service.AppIpRuleService;
import com.semptian.service.ApplicationRuleService;
import com.semptian.utils.ExcelUtil;
import com.semptian.utils.IPv6ParseUtil;
import com.semptian.utils.IpUtil;
import com.semptian.vo.PageQueryResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.semptian.constant.CommonConstant.AUTO_UPDATE_RULE_SOURCE_TYPE;
import static com.semptian.constant.CommonConstant.USER_RULE_SOURCE_TYPE;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:07
 **/
@Slf4j
@Service
public class ApplicationRuleServiceImpl extends ServiceImpl<ApplicationRuleMapper, ApplicationRuleEntity> implements ApplicationRuleService {

    private static final String[] MODULE_FIELD = new String[]{"name", "type", "description", "rule"};
    private static final String ADS_APP_RULE_TABLE = "ads_basic_app_rule";
    private static final String ADS_APP_RULE_IP_TABLE = "ads_basic_app_rule_ip";

    @Resource
    private AppIpRuleService appIpRuleService;

    @Resource
    private DomainRuleServiceImpl domainRuleService;

    @Resource
    private DorisDatService dorisDatService;

    @Value("${app.custom.max.num:1000}")
    private Integer customAppMaxNum;

    /**
     * 用户添加的数据,指定特定分区日期
     */
    @Value("${custom.data.partition:2024-01-01}")
    private String customDataPartition;

    /**
     * 域名展示ip的top数
     */
    @Value("${app.domain.ip.top:3}")
    private Integer appIpTop;

    @Value("${doris.dbName.dws:dws}")
    private String dorisDbNameDws;

    @Override
    public Object queryAppRuleList(String keyword, String ruleName, Integer status, Integer size, Integer onPage, String orderField, Integer orderType, List<Integer> queryRange, String lang) {

        Page<ApplicationRuleEntity> page = new Page<>(onPage, size);
        List<ApplicationRuleEntity> list;

        //获取自动更新数据的最新插入日期
        String latestInsertDay = domainRuleService.getLatestInsertDay();

        int range = 0;
        if (CollUtil.isNotEmpty(queryRange)) {
            if (queryRange.size() > 1) {
                range = AUTO_UPDATE_RULE_SOURCE_TYPE;
            } else {
                range = USER_RULE_SOURCE_TYPE;
            }
        }
        if (range == AUTO_UPDATE_RULE_SOURCE_TYPE && StrUtil.isEmpty(latestInsertDay)) {
            return ReturnModel.getInstance().ok(page);
        }

        String[] sqlArr = buildQueryApplicationRuleListWebSql(latestInsertDay, keyword.trim(), size, onPage, orderField, orderType, status, range);
        PageQueryResponseVo<ApplicationRuleEntity> pageQueryResponseVo = dorisDatService.queryForPage(sqlArr[0], sqlArr[1], ApplicationRuleEntity.class);

        if (pageQueryResponseVo.getTotal() == 0) {
            return ReturnModel.getInstance().ok(page);
        }

        list = pageQueryResponseVo.getList();

        Set<String> ids = new HashSet<>();
        for (ApplicationRuleEntity entity : list) {
            ids.add(entity.getId());
            entity.setAppType(entity.getType());
            entity.setExtraName("<span style='color:red'>[" + getSourceTypeName(entity.getSourceType()) + "]</span>");
        }

        //根据id查询对应的TOP3的ip
        if (CollectionUtil.isNotEmpty(ids)) {
            //默认展示Top3
            List<Map<String, Object>> ipList = selectTopAppIpRuleList(ids, latestInsertDay, appIpTop);

            Map<String, String> ruleMap = new HashMap<>();
            if (CollUtil.isNotEmpty(ipList)) {
                for (Map<String, Object> map : ipList) {
                    List<String> ips = new ArrayList<>();

                    for (String ip : map.get("ips").toString().split(",")) {
                        if (!StrUtil.isEmpty(ip)) {
                            String[] arr = ip.split(";");

                            String ipStr = arr[0];
                            String port = arr[1];

                            if (!"-1".equals(port)) {
                                if (IpUtil.isipv6(arr[0])) {
                                    ipStr = "[" + ipStr + "]";
                                }
                                ipStr = ipStr + ":" + port;
                            }
                            ips.add(ipStr);
                        }
                    }

                    ruleMap.put(map.get("ruleId").toString(), String.join(",", ips));
                }
            }

            for (ApplicationRuleEntity entity : list) {
                String ips = ruleMap.get(entity.getId());
                if (StrUtil.isNotEmpty(ips)) {
                    entity.setIp(ips);
                }
            }
        }

        page.setRecords(list);
        page.setTotal(pageQueryResponseVo.getTotal());
        return ReturnModel.getInstance().ok(page);
    }

    /**
     * 获取来源名称
     */
    private String getSourceTypeName(Integer sourceType) {
        switch (sourceType) {
            case AUTO_UPDATE_RULE_SOURCE_TYPE:
                return I18nUtils.getMessage("autoUpdate");
            case USER_RULE_SOURCE_TYPE:
                return I18nUtils.getMessage("customize");
            default:
                return "";
        }
    }

    @Override
    public ReturnModel<?> addAppRule(String name, String description, String ip, String appType, String userId) {
        //判断name是否重复
        if (checkRepeatAppName(name, null)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("nameExists"));
        }

        //校验总数量
        if (checkTotalCount(1L)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_BATCH_INSERT_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("app.custom.max.num") + " " + customAppMaxNum);
        }

        ApplicationRuleEntity applicationRuleEntity = buildAppIpRuleEntity(name, description, appType, userId);

        AdsBasicAppRuleModel adsBasicAppRuleModel = new AdsBasicAppRuleModel(applicationRuleEntity, customDataPartition);
        boolean insert = dorisDatService.insertBatch(ADS_APP_RULE_TABLE, Lists.newArrayList(adsBasicAppRuleModel));
        if (!insert) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }

        //将id与ip端口的对应关系插入到数据库中
        applicationRuleEntity.setIp(ip);
        appIpRuleService.insertIps(Lists.newArrayList(applicationRuleEntity));
        return ReturnModel.getInstance().ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object updateAppRule(String id, String name, String description, String ip, String type, String userId) {
        if (checkRepeatAppName(name, id)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("nameExists"));
        }

        // Doris DUPLICATE KEY模型不支持修改，因此先删除再插入
        delAppRule(Lists.newArrayList(id));

        ApplicationRuleEntity applicationRuleEntity = buildAppIpRuleEntity(name, description, type, userId);
        applicationRuleEntity.setId(id);

        AdsBasicAppRuleModel adsBasicAppRuleModel = new AdsBasicAppRuleModel(applicationRuleEntity, customDataPartition);
        boolean insert = dorisDatService.insertBatch(ADS_APP_RULE_TABLE, Lists.newArrayList(adsBasicAppRuleModel));
        if (!insert) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }

        //判断域名和IP的对应关系需不需要变化
        //查询应用关联的所有IP
        Page<AppIpRuleEntity> ipRuleEntityPage = queryIpList(id, 1000, 1);

        List<String> newIpPorts = new ArrayList<>(Arrays.asList(ip.split(",")));
        List<String> needDeleteIpsList = new ArrayList<>();

        for (AppIpRuleEntity appIpRule : ipRuleEntityPage.getRecords()) {
            String key;
            if (null == appIpRule.getPort()) {
                key = appIpRule.getIp();
            } else {
                if (appIpRule.getIpType() == 1) {
                    key = "[" + appIpRule.getIp() + "]:" + appIpRule.getPort();
                } else {
                    key = appIpRule.getIp() + ":" + appIpRule.getPort();
                }
            }

            if (!newIpPorts.contains(key)) {
                needDeleteIpsList.add(appIpRule.getId());
            } else {
                newIpPorts.remove(appIpRule.getIp());
            }
        }

        //删除已经不存在的ip信息,根据rule_id,ip,port
        if (CollectionUtil.isNotEmpty(needDeleteIpsList)) {
            boolean b = delAppRuleIpList("id", needDeleteIpsList);
            if (!b) {
                log.error("delete app rule ip error, ids:{}", needDeleteIpsList);
            }
        }

        //插入新的ip信息
        if (CollUtil.isNotEmpty(newIpPorts)) {
            ApplicationRuleEntity applicationRule = new ApplicationRuleEntity();
            applicationRule.setId(id);
            applicationRule.setIp(String.join(",", newIpPorts));

            appIpRuleService.insertIps(Lists.newArrayList(applicationRule));
        }
        return ReturnModel.getInstance().ok();
    }

    @Override
    public Object deleteAppRule(String ids) {
        String[] split = ids.split(",");
        if (split.length == 0) {
            return ReturnModel.getInstance().ok();
        }

        List<String> idList = Arrays.stream(split).distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        if (!idList.isEmpty()) {
            //删除应用表数据

            //判断应用规则是自定义还是自动更新,自动更新则不允许删除
            List<ApplicationRuleEntity> applicationRuleList = getAppRuleByIds(idList, domainRuleService.getLatestInsertDay());
            if (CollUtil.isNotEmpty(applicationRuleList)) {
                for (ApplicationRuleEntity applicationRuleEntity : applicationRuleList) {
                    if (applicationRuleEntity.getSourceType() == CommonConstant.AUTO_UPDATE_RULE_SOURCE_TYPE) {
                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.PRESET_DATA_CANNOT_DELETE.getCode()).setMsg(I18nUtils.getMessage("preset.data.cannot.delete"));
                    }
                }
           }

            boolean deleteAppRule = delAppRule(idList);
            if (!deleteAppRule) {
                log.error("delete app rule error, id:{}", idList);
            }

            //删除应用IP表中的数据
            boolean delAppRuleIp = delAppRuleIpList("rule_id", idList);
            if (!delAppRuleIp) {
                log.error("delete app rule ip error, rule ids:{}", idList);
            }

            if (!deleteAppRule || !delAppRuleIp) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
            }
        }

        return ReturnModel.getInstance().ok();
    }

    @Override
    public Object importAppRules(MultipartFile file, String lang, String userId, HttpServletResponse response) {
        List<Map<String, Object>> list = ExcelUtil.readExcel(file, MODULE_FIELD);

        if (CollUtil.isEmpty(list)) {
            return ReturnModel.getInstance().ok().setMsg(I18nUtils.getMessage("import.white.isEmpty"));
        }

        if (list.size() > 100) {
            return ReturnModel.getInstance().ok().setMsg(I18nUtils.getMessage("importOutOfSize"));
        }

        //校验总数量
        if (checkTotalCount((long) list.size())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_BATCH_INSERT_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("app.custom.max.num") + " " + customAppMaxNum);
        }

        List<ImportExcelErrorMsgModel> importExcelErrorMsgModelList = validatorImportDataList(list);
        if (CollUtil.isNotEmpty(importExcelErrorMsgModelList)) {
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getOriginalFilename());
            try (OutputStream fileOutputStream = response.getOutputStream(); InputStream inputStream = file.getInputStream()) {
                XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                XSSFSheet sheet = workbook.getSheetAt(0);

                //创建绘图对象
                XSSFDrawing drawing = sheet.createDrawingPatriarch();

                for (ImportExcelErrorMsgModel importExcelErrorMsgModel : importExcelErrorMsgModelList) {
                    XSSFRow row = sheet.getRow(importExcelErrorMsgModel.getRow());
                    XSSFCell cell = row.getCell(importExcelErrorMsgModel.getColumn());
                    if (ObjectUtil.isNull(cell)) {
                        cell = row.createCell(importExcelErrorMsgModel.getColumn());
                    }

                    XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                    cellComment.setAuthor("CBP");
                    cellComment.setString(new XSSFRichTextString(importExcelErrorMsgModel.getErrorMsg()));
                    cell.setCellComment(cellComment);
                }

                workbook.write(fileOutputStream);
            } catch (Exception e) {
                log.error("response error ", e);
            }

            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }

        List<ApplicationRuleEntity> appRuleEntityList = new ArrayList<>();

        for (Map<String, Object> m : list) {
            String name = String.valueOf(m.get("name"));
            String description = String.valueOf(m.get("description"));
            String type = String.valueOf(m.get("type"));
            String ip = String.valueOf(m.get("rule"));

            ApplicationRuleEntity applicationRuleEntity = buildAppIpRuleEntity(name, description, type, userId);
            applicationRuleEntity.setIp(ip);

            appRuleEntityList.add(applicationRuleEntity);
        }

        //批量写入应用规则数据
        if (CollectionUtil.isNotEmpty(appRuleEntityList)) {
            List<AdsBasicAppRuleModel> appRuleModels = appRuleEntityList.stream().map(appRuleEntity -> new AdsBasicAppRuleModel(appRuleEntity, customDataPartition)).collect(Collectors.toList());

            boolean b = dorisDatService.insertBatch(ADS_APP_RULE_TABLE, appRuleModels);
            if (!b) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
            }

            //写入应用ip规则数据
            appIpRuleService.insertIps(appRuleEntityList);
        }

        return ReturnModel.getInstance().ok();
    }

    @Override
    public List<Map<String, Object>> getAppTypeList() {
        String sql = "select app_type from " + dorisDbNameDws + ".dws_theme_app_info group by app_type order by LOWER(app_type)";
        return dorisDatService.queryForList(sql);
    }

    @Override
    public Page<AppIpRuleEntity> queryIpList(String id, Integer size, Integer onPage) {
        Page<AppIpRuleEntity> page = new Page<>(onPage, size);

        //获取自动更新数据的最新插入日期
        //根据ID查询应用规则类型
        String latestInsertDay = domainRuleService.getLatestInsertDay();

        List<ApplicationRuleEntity> appRuleEntities = getAppRuleByIds(Lists.newArrayList(id), latestInsertDay);
        if (CollUtil.isEmpty(appRuleEntities)) {
            return page;
        }

        Integer sourceType = appRuleEntities.get(0).getSourceType();
        if (sourceType == 2) {
            latestInsertDay = customDataPartition;
        }

        List<AppIpRuleEntity> resList = queryAppIpRulePage(page, id, latestInsertDay);

        page.setRecords(resList);
        return page;
    }

    private List<ApplicationRuleEntity> getAppRuleByIds(List<String> ids, String latestInsertDay) {
        String sql = "SELECT id,name,rule,description,status,`type`,source_type as sourceType,user_id as userId,create_time as createTime,update_time as updateTime FROM ads_basic_app_rule ";

        List<String> idsParamList = new ArrayList<>(ids);
        idsParamList.replaceAll(s -> "'" + s + "'");

        sql = sql + " WHERE insert_day in ('" + latestInsertDay + "', '" + customDataPartition + "') AND id in ( " + String.join(",", idsParamList) + " ) ";
        return dorisDatService.queryForList(sql, ApplicationRuleEntity.class);
    }

    private String[] buildQueryApplicationRuleListWebSql(String latestInsertDay, String keyword, Integer size, Integer onPage, String orderField, Integer orderType, Integer status, Integer range) {
        StringBuilder builder = new StringBuilder("SELECT id,name,rule,description,status,`type`,source_type as sourceType,user_id as userId,create_time as createTime,update_time as updateTime FROM ads_basic_app_rule t1 ");
        builder.append(" WHERE %s ");

        String sql = getInsertDayCondition(latestInsertDay, range, builder);

        if (StrUtil.isNotBlank(keyword)) {

            if (IpUtil.isipv6(keyword)) {
                keyword = IPv6ParseUtil.getShortIPv6(keyword).toLowerCase();
            }

            //name忽略大小写进行模糊匹配,rule和description不忽略进行模糊匹配
            String stringBuilder = " AND (lower(name) like '%" + keyword.toLowerCase() + "%' or rule like '%" + keyword + "%' or description like '%" + keyword + "%' " +
                    " or exists (select 1 from ads_basic_app_rule_ip t2 where t1.id = t2.rule_id and lower(ip) like '%" + keyword.toLowerCase() + "%') )";

            sql = sql + stringBuilder;
        }

        String pageSql = " order by sourceType desc, " + orderField + (orderType == 0 ? " DESC " : " ASC ") + " LIMIT " + size + " OFFSET " + (onPage - 1) * size;

        log.info("buildQueryApplicationRuleListWebSql , sql:{}, pageSql:{}", sql, sql + pageSql);
        return new String[]{sql, sql + pageSql};
    }

    private String getInsertDayCondition(String latestInsertDay, Integer range, StringBuilder builder) {
        String insertDayCondition;

        switch (range) {
            case USER_RULE_SOURCE_TYPE:
                //查询用户自定义
                insertDayCondition = " insert_day = '" + customDataPartition + "'";
                break;
            case AUTO_UPDATE_RULE_SOURCE_TYPE:
                //查询自动更新
                insertDayCondition = " insert_day = '" + latestInsertDay + "'";
                break;
            default:
                //默认查询所有即自动更新+自定义
                insertDayCondition = " insert_day in ('" + latestInsertDay + "', '" + customDataPartition + "') ";
        }

        return String.format(builder.toString(), insertDayCondition);
    }

    public List<Map<String, Object>> selectTopAppIpRuleList(Set<String> ruleIds, String insertDay, int top) {
        String sql = "SELECT rule_id as ruleId, GROUP_CONCAT(ip, ',') AS ips FROM ( SELECT rule_id, CONCAT_WS(';', ip, port) as ip, ROW_NUMBER() OVER (PARTITION BY rule_id ORDER BY behavior_num_sum DESC, ip ASC) AS rn " +
                "FROM ads_basic_app_rule_ip where insert_day in ( %s ) and rule_id in ( %s ) ) ranked_ips WHERE rn <= %s GROUP BY rule_id ";

        List<String> list = new ArrayList<>(ruleIds);
        list.replaceAll(s -> "'" + s + "'");

        List<String> insertDayList = Lists.newArrayList(insertDay, customDataPartition);
        insertDayList.replaceAll(s -> "'" + s + "'");

        sql = String.format(sql, String.join(",", insertDayList), String.join(",", list), top);
        return dorisDatService.queryForList(sql);
    }

    public List<AppIpRuleEntity> queryAppIpRulePage(Page<AppIpRuleEntity> page, String ruleId, String insertDay) {
        String sql = "SELECT id,rule_id as ruleId,ip,ip_type as ipType, port from ads_basic_app_rule_ip where insert_day = %s and rule_id = %s ";
        sql = String.format(sql, "'" + insertDay + "'", "'" + ruleId + "'");

        String pageSql = " ORDER BY behavior_num_sum DESC, ip ASC " + " LIMIT " + page.getSize() + " OFFSET " + (page.getCurrent() - 1) * page.getSize();

        log.info("queryAppIpRulePage , sql:{}, pageSql:{}", sql, sql + pageSql);

        PageQueryResponseVo<AppIpRuleEntity> responseVo = dorisDatService.queryForPage(sql, sql + pageSql, AppIpRuleEntity.class);

        page.setTotal(responseVo.getTotal());
        return responseVo.getList();
    }

    /**
     * 判断name是否重复
     * @param name 域名
     * @return boolean
     */
    public boolean checkRepeatAppName(String name, String id) {
        //判断name是否重复
        String checkRepeatSql = "SELECT id from ads_basic_app_rule where insert_day = %s and name = %s ";
        checkRepeatSql = String.format(checkRepeatSql, "'" + customDataPartition + "'", "'" + name + "'");
        List<ApplicationRuleEntity> existsList = dorisDatService.queryForList(checkRepeatSql, ApplicationRuleEntity.class);

        if (StrUtil.isNotEmpty(id)) {
            existsList = existsList.stream().filter(applicationRuleEntity -> !id.equals(applicationRuleEntity.getId())).collect(Collectors.toList());
        }

        return !CollUtil.isEmpty(existsList);
    }

    public ApplicationRuleEntity buildAppIpRuleEntity(String name, String description, String appType, String userId) {
        ApplicationRuleEntity applicationRuleEntity = new ApplicationRuleEntity();
        applicationRuleEntity.setName(name);
        applicationRuleEntity.setDescription(StrUtil.isNotEmpty(description) ? description : " ");
        applicationRuleEntity.setRule(name);
        applicationRuleEntity.setType(appType);
        applicationRuleEntity.setStatus(1);
        applicationRuleEntity.setSourceType(2);
        applicationRuleEntity.setUserId(Long.parseLong(userId));
        applicationRuleEntity.setUpdateTime(System.currentTimeMillis());
        applicationRuleEntity.setCreateTime(System.currentTimeMillis());
        String id = IdUtil.simpleUUID();
        applicationRuleEntity.setId(id);
        return applicationRuleEntity;
    }

    /**
     * 删除应用信息
     * @param idList id集合
     * @return boolean
     */
    private boolean delAppRule(List<String> idList) {
        WhereParam insertDay = WhereParam.builder().field("insert_day").type(Rel.EQ).param(Lists.newArrayList(customDataPartition)).build();
        WhereParam id = WhereParam.builder().field("id").type(Rel.IN).param(Lists.newArrayList(idList)).build();

        List<WhereParam> paramList = new ArrayList<>();
        paramList.add(insertDay);
        paramList.add(id);

        return dorisDatService.deleteByCondition(ADS_APP_RULE_TABLE, paramList);
    }

    /**
     * 删除应用关联ip信息
     * @param idList 应用ID集合或者应用ipID集合
     */
    public boolean delAppRuleIpList(String idField, List<String> idList) {
        WhereParam ruleIpId = WhereParam.builder().field(idField).type(Rel.IN).param(Lists.newArrayList(idList)).build();
        WhereParam insertDay = WhereParam.builder().field("insert_day").type(Rel.EQ).param(Lists.newArrayList(customDataPartition)).build();
        List<WhereParam> list = new ArrayList<>();
        list.add(insertDay);
        list.add(ruleIpId);

        return dorisDatService.deleteByCondition(ADS_APP_RULE_IP_TABLE, list);
    }

    private boolean checkTotalCount(Long addNum) {
        String checkTotalCountSql = "SELECT count(*) as cnt from ads_basic_app_rule where insert_day = %s ";
        checkTotalCountSql = String.format(checkTotalCountSql, "'" + customDataPartition + "'");
        Long count = dorisDatService.queryForCount(checkTotalCountSql);

        return (count + addNum) > customAppMaxNum;
    }

    private List<ImportExcelErrorMsgModel> validatorImportDataList(List<Map<String, Object>> list) {
        List<ImportExcelErrorMsgModel> errorMsgList = new ArrayList<>();

        //查询所有的自定义应用规则
        String sql = "SELECT id,name,rule,description,status,`type`,source_type as sourceType FROM ads_basic_app_rule t1 where insert_day = '" + customDataPartition + "'";
        List<ApplicationRuleEntity> applicationRuleEntityList = dorisDatService.queryForList(sql, ApplicationRuleEntity.class);
        List<String> nameList = applicationRuleEntityList.stream().map(ApplicationRuleEntity::getName).collect(Collectors.toList());

        //获取所有应用分类
        List<Map<String, Object>> appTypeList = getAppTypeList();
        List<String> appTypeListStr = appTypeList.stream().map(m -> m.get("app_type").toString()).collect(Collectors.toList());

        //添加user_id,type
        for (int i = 0; i <= list.size() - 1; i++) {
            Map<String, Object> m = list.get(i);

            int row = i + 1;

            //判断name是否是空以及长度校验
            if (m.get("name") == null || StringUtils.isBlank(String.valueOf(m.get("name")))) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(0).errorMsg(I18nUtils.getMessage("name.cannot.null")).build());
            }else if (m.get("name") != null && m.get("name").toString().length() > 128) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(0).errorMsg(I18nUtils.getMessage("app.max.length")).build());
            }else if (nameList.contains(m.get("name").toString())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(0).errorMsg(I18nUtils.getMessage("nameExists")).build());
            } else {
                nameList.add(m.get("name").toString());
            }

            //判断type是否是空以及长度校验
            if (m.get("type") == null || StringUtils.isBlank(String.valueOf(m.get("type")))) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(1).errorMsg(I18nUtils.getMessage("appType.cannot.null")).build());
            } else if (m.get("type") != null && m.get("type").toString().length() > 30) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(1).errorMsg(I18nUtils.getMessage("appType.max.length")).build());
            } else if (!appTypeListStr.contains(m.get("type").toString())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(1).errorMsg(I18nUtils.getMessage("appType.not.exist")).build());
            }

            //描述的字符长度不能大于64个字符
            if (m.get("description") != null && m.get("description").toString().length() > 64) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(2).errorMsg(I18nUtils.getMessage("description.max.length")).build());
            }

            //校验IP是否合法
            String ip = null == m.get("rule") ? "" : String.valueOf(m.get("rule"));
            if (StringUtils.isNotBlank(ip)) {
                //判断导入ip的长度是否大于2000个字符
                if (ip.length() > 2000) {
                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(3).errorMsg(I18nUtils.getMessage("ip.max.length")).build());
                } else {
                    String[] split = ip.split(",");
                    if (!IpUtil.checkIPSAndPort(split)) {
                        errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(3).errorMsg(I18nUtils.getMessage("IP_CHECK")).build());
                    }
                }
            } else {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(3).errorMsg(I18nUtils.getMessage("ip.cannot.null")).build());
            }
        }

        return errorMsgList;
    }
}
