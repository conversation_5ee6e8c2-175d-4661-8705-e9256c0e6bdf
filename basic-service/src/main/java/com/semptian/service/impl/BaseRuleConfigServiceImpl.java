package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.BaseRuleConfigEntity;
import com.semptian.mapper.BaseRuleConfigMapper;
import com.semptian.service.BaseRuleConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 知识库配置service实现类
 * <AUTHOR>
 * @date 2022-05-19 19:38
 **/
@Service
public class BaseRuleConfigServiceImpl extends ServiceImpl<BaseRuleConfigMapper, BaseRuleConfigEntity> implements BaseRuleConfigService {

    @Resource
    private BaseRuleConfigMapper baseRuleConfigMapper;

    @Override
    public BaseRuleConfigEntity queryAutoUpdateStatus(Integer ruleType) {
        return baseRuleConfigMapper.queryAutoUpdateStatus(ruleType);
    }

    @Override
    public Object updateAutoUpdateStatus(Integer ruleType, Integer status) {
        return baseRuleConfigMapper.updateAutoUpdateStatus(ruleType, status, System.currentTimeMillis());
    }

    @Override
    public Object updateExecTime(Integer ruleType, Long execTime) {
        return baseRuleConfigMapper.updateExecTime(ruleType, execTime);
    }

}
