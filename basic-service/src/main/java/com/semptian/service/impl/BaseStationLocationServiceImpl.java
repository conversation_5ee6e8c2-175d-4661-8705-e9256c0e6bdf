package com.semptian.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.ExportConstant;
import com.semptian.entity.BaseStationLocationEntity;
import com.semptian.enums.BaseStationQuerySortFieldEnum;
import com.semptian.enums.CommonReturnCode;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.NetworkOperatorEnum;
import com.semptian.feign.message.MsgFeignClient;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.BaseStationLocationMapper;
import com.semptian.model.ExportModel;
import com.semptian.param.*;
import com.semptian.service.BaseStationLocationService;
import com.semptian.utils.DateUtil;
import com.semptian.utils.ExportUtils;
import com.semptian.utils.UserLocationInfoUtil;
import com.semptian.vo.BaseStationLocationVo;
import com.semptian.vo.PageQueryResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 基站位置信息库表 服务实现类
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@Service
@DS("mysql")
public class BaseStationLocationServiceImpl extends ServiceImpl<BaseStationLocationMapper, BaseStationLocationEntity> implements BaseStationLocationService {

    /**
     * 导出选择所有时，ids为-1
     */
    private static final String ALL_DATA_IDS = "-1";

    private static final String FILE_NAME = "base_station_";

    private static final String IMPORT_MESSAGE_TITLE = "Base de données des stations de base - Importation des données des stations de base";
    private static final String IMPORT_MESSAGE_START_CONTENT = "La tâche d'importation a commencé";

    //导入模板基站编号列所在列
    private static final Integer TEMPLATE_STATION_NO_COLUMN_INDEX = 0;

    //导入模板经度列所在列
    private static final Integer TEMPLATE_LNG_COLUMN_INDEX = 2;

    //导入模板纬度列所在列
    private static final Integer TEMPLATE_LAT_COLUMN_INDEX = 1;

    //导入模板CELLNAME列所在列
    private static final Integer TEMPLATE_CELL_NAME_COLUMN_INDEX = 3;

    //导入模板覆盖半径列所在列
    private static final Integer TEMPLATE_COVERAGE_RADIUS_COLUMN_INDEX = 5;

    //导入模板基站地址列所在列
    private static final Integer TEMPLATE_STATION_ADDRESS_COLUMN_INDEX = 4;

    //未知选项
    private static final Integer UNKNOWN = 0;

    //经度正则表达式
    private static final Pattern PATTERN_LNG_REGEX = Pattern.compile("^(-?(?:180(\\.0{1,18})?|1[0-7]\\d(\\.\\d{1,18})?|0?\\d?\\d(\\.\\d{1,18})?))$");

    //纬度正则表达式
    private static final Pattern PATTERN_LAT_REGEX = Pattern.compile("^(-?(?:[0-8]?\\d(\\.\\d{1,18})?|90(\\.0{1,18})?))$");

    private static final Pattern PATTERN_COVERAGE_RADIUS_REGEX = Pattern.compile("^\\d{1,8}(\\.\\d{1,2})?$");

    @Value("${import.batch.rows:5000}")
    private Integer importBatchRows;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    @Lazy
    private MsgFeignClient msgFeignClient;

    /**
     * 文件服务器root目录
     */
    @Value("${export.file.ftp.path:/home/<USER>/upload/}")
    private String ftpFileRootPath;

    @Value("${file.host:http://**************:9000/}")
    private String fileServerAddr;

    @Override
    public List<BaseStationLocationEntity> queryStationByLocation(Float downLeftLat, Float downLeftLon, Float topRightLat, Float topRightLon, Integer size) {
        return this.baseMapper.queryBaseStationInfoByLocation(downLeftLat, downLeftLon, topRightLat, topRightLon, size);
    }

    @Override
    public List<BaseStationLocationEntity> recommendStationByNumber(String keyword, Integer size) {
        return this.baseMapper.recommendStationByNumber(keyword, size);

    }

    /**
     * 新增基站位置信息
     *
     * @param baseStationModel 基站位置信息
     * @return 新增结果
     */
    @Override
    public Object add(BaseStationLocationModel baseStationModel) {
        if (checkRepeated(baseStationModel)) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("baseStation.stationNo.is.exist"));
        }

        if (null != baseStationModel.getCoverageRadius() && !PATTERN_COVERAGE_RADIUS_REGEX.matcher(baseStationModel.getCoverageRadius().toPlainString()).matches()) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("Radius(m) is too long");
        }

        if (baseStationModel.getStationNo().length() > 64) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("baseStation.stationNo.is.too.long"));
        }

        if (baseStationModel.getStationAddress().length() > 512) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("Address is too long");
        }

        if (StrUtil.isNotEmpty(baseStationModel.getCellName()) && baseStationModel.getCellName().length() > 64) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("CELLNAME is too long");
        }

        BaseStationLocationEntity baseStationEntity = buildBaseStationLocationEntity(baseStationModel);

        this.save(baseStationEntity);

        return ReturnModel.getInstance().ok(baseStationModel);
    }

    /**
     * 修改基站位置信息
     *
     * @param baseStationModel 基站位置信息
     * @return 修改结果
     */
    @Override
    public Object update(BaseStationLocationModel baseStationModel) {
        if (checkRepeated(baseStationModel)) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("baseStation.stationNo.is.exist"));
        }

        if (null != baseStationModel.getCoverageRadius() && !PATTERN_COVERAGE_RADIUS_REGEX.matcher(baseStationModel.getCoverageRadius().toPlainString()).matches()) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("Radius(m) is too long");
        }

        if (baseStationModel.getStationNo().length() > 64) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("baseStation.stationNo.is.too.long"));
        }

        if (baseStationModel.getStationAddress().length() > 512) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("Address is too long");
        }

        if (StrUtil.isNotEmpty(baseStationModel.getCellName()) && baseStationModel.getCellName().length() > 64) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("CELLNAME is too long");
        }

        BaseStationLocationEntity baseStationEntity = buildBaseStationLocationEntity(baseStationModel);

        lambdaUpdate().set(BaseStationLocationEntity::getCgi, baseStationEntity.getCgi())
                .set(BaseStationLocationEntity::getStationNo, baseStationEntity.getStationNo())
                .set(BaseStationLocationEntity::getLatitude, baseStationEntity.getLatitude())
                .set(BaseStationLocationEntity::getLongitude, baseStationEntity.getLongitude())
                .set(BaseStationLocationEntity::getCoverageRadius, baseStationEntity.getCoverageRadius())
                .set(BaseStationLocationEntity::getNetworkType, baseStationEntity.getNetworkType())
                .set(BaseStationLocationEntity::getStationAddress, baseStationEntity.getStationAddress())
                .set(BaseStationLocationEntity::getGrade, baseStationEntity.getGrade())
                .set(BaseStationLocationEntity::getNetworkOperator, baseStationEntity.getNetworkOperator())
                .set(BaseStationLocationEntity::getDescription, baseStationEntity.getDescription())
                .set(BaseStationLocationEntity::getModifyTime, System.currentTimeMillis())
                .set(BaseStationLocationEntity::getCellName, baseStationEntity.getCellName())
                .eq(BaseStationLocationEntity::getId, baseStationModel.getId())
                .update();

        this.updateById(baseStationEntity);
        return ReturnModel.getInstance().ok(baseStationModel);
    }

    private BaseStationLocationEntity buildBaseStationLocationEntity(BaseStationLocationModel baseStationModel) {
        BaseStationLocationEntity baseStationEntity = new BaseStationLocationEntity();
        BeanUtil.copyProperties(baseStationModel, baseStationEntity);
        baseStationEntity.setCgi(baseStationModel.getStationNo());
        baseStationEntity.setStationNo(baseStationModel.getStationNo());
        baseStationEntity.setNetworkOperator(baseStationModel.getNetworkOperator());
        baseStationEntity.setNetworkType(0);
        baseStationEntity.setGrade(0);
        return baseStationEntity;
    }

    /**
     * 校验基站编号不能重复
     * @param baseStationModel 基站位置信息
     * @return 是否重复
     */
    private boolean checkRepeated(BaseStationLocationModel baseStationModel) {
        LambdaQueryChainWrapper<BaseStationLocationEntity> queryChainWrapper = lambdaQuery().eq(BaseStationLocationEntity::getStationNo, baseStationModel.getStationNo()).eq(BaseStationLocationEntity::getIsDel, 0);
        if (ObjectUtil.isNotNull(baseStationModel.getId())) {
            queryChainWrapper.ne(BaseStationLocationEntity::getId, baseStationModel.getId());
        }

        Integer count = queryChainWrapper.count();
        return count > 0;
    }

    /**
     * 批量删除基站位置信息
     *
     * @param deleteModel 删除参数
     * @return 删除结果
     */
    @Override
    public Object delete(DeleteModel deleteModel) {
        //批量删除基站位置信息
        this.lambdaUpdate().set(BaseStationLocationEntity::getIsDel, 1).in(BaseStationLocationEntity::getId, deleteModel.getIds()).update();
        return ReturnModel.getInstance().ok();
    }

    /**
     * 查询基站位置信息
     * @param baseStationQueryParamModel 查询参数
     * @return 基站位置分页查询结果
     */
    @Override
    public Object list(BaseStationQueryParamModel baseStationQueryParamModel) {
        QueryWrapper<BaseStationLocationEntity> queryWrapper = getBaseStationQueryWrapper(baseStationQueryParamModel);

        //分页查询
        Page<BaseStationLocationEntity> page = new Page<>();
        page.setCurrent(baseStationQueryParamModel.getOnPage());
        page.setSize(baseStationQueryParamModel.getSize());

        this.page(page, queryWrapper);

        List<BaseStationLocationVo> list = entity2Vo(page.getRecords());

        PageQueryResponseVo<BaseStationLocationVo> pageQueryResponseVo = new PageQueryResponseVo<>();
        pageQueryResponseVo.setTotal(page.getTotal());
        pageQueryResponseVo.setList(list);

        return ReturnModel.getInstance().ok(pageQueryResponseVo);
    }

    /**
     * @param model    导出参数
     * @param response 响应
     */
    @Override
    public Object export(BaseStationExportModel model, HttpServletResponse response) {
        BaseStationQueryParamModel baseStationQueryParamModel = new BaseStationQueryParamModel();
        BeanUtil.copyProperties(model, baseStationQueryParamModel);
        QueryWrapper<BaseStationLocationEntity> queryWrapper = getBaseStationQueryWrapper(baseStationQueryParamModel);

        //导出基站位置信息
        if (!ALL_DATA_IDS.equals(model.getIds())) {
            List<Integer> ids = Arrays.stream(model.getIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            queryWrapper.in("id", ids);
        }

        //查询并转换数据格式
        List<BaseStationLocationVo> list = entity2Vo(this.list(queryWrapper));

        try {
            ExportModel exportModel = new ExportModel();
            exportModel.setExportType(model.getExportType());
            exportModel.setIds(model.getIds());
            ExportUtils.exportWithExcelXlsx(exportModel, list, response, FILE_NAME + System.currentTimeMillis(), ExportConstant.BASE_STATION_EXPORT_FIELDS, ExportConstant.BASE_STATION_EXPORT_TITLES);
            return ReturnModel.getInstance().ok();
        }catch (Exception e){
            log.error("export base station location error ", e);
        }

        return ReturnModel.getInstance().error().setCode(CommonReturnCode.EXPORT_ERROR.getCode()).setMsg(I18nUtils.getMessage("export.failure"));
    }

    /**
     * 组装基站位置信息查询条件
     * @param baseStationQueryParamModel 查询参数
     * @return 查询条件
     */
    private QueryWrapper<BaseStationLocationEntity> getBaseStationQueryWrapper(BaseStationQueryParamModel baseStationQueryParamModel) {
        QueryWrapper<BaseStationLocationEntity> queryWrapper = new QueryWrapper<>();

        //未删除
        queryWrapper.eq("is_del", 0);

        //基站编号、基站地址、CELLNAME、描述字段模糊查询
        if (StrUtil.isNotEmpty(baseStationQueryParamModel.getKeyword())) {
            queryWrapper.and(wrapper ->
                            wrapper.like("station_no", baseStationQueryParamModel.getKeyword())
                            .or().like("station_address", baseStationQueryParamModel.getKeyword())
                            .or().like("cell_name", baseStationQueryParamModel.getKeyword())
                            .or().like("description", baseStationQueryParamModel.getKeyword()));
        }

        //基站等级查询
        if (baseStationQueryParamModel.getGrade() != null) {
            queryWrapper.eq("grade", baseStationQueryParamModel.getGrade());
        }

        //网络类型查询
        if (baseStationQueryParamModel.getNetworkType() != null) {
            queryWrapper.eq("network_type", baseStationQueryParamModel.getNetworkType());
        }

        //更新时间查询
        if (baseStationQueryParamModel.getModifyTimeStart() != null && baseStationQueryParamModel.getModifyTimeEnd() != null) {
            queryWrapper.between("modify_time", baseStationQueryParamModel.getModifyTimeStart(), baseStationQueryParamModel.getModifyTimeEnd());
        }

        if (baseStationQueryParamModel.getNetworkOperator() != null && baseStationQueryParamModel.getNetworkOperator() != -1) {
            queryWrapper.eq("network_operator", baseStationQueryParamModel.getNetworkOperator());
        }

        //排序字段
        if (StrUtil.isEmpty(baseStationQueryParamModel.getSortField())) {
            baseStationQueryParamModel.setSortField("stationNo");
        }
        //排序类型
        if (baseStationQueryParamModel.getSortType() == null) {
            baseStationQueryParamModel.setSortType(0);
        }

        queryWrapper.orderBy(true, baseStationQueryParamModel.getSortType() == 0, BaseStationQuerySortFieldEnum.getFieldByName(baseStationQueryParamModel.getSortField()));

        return queryWrapper;
    }

    /**
     * entity转vo
     * @param entityList entity列表
     * @return vo列表
     */
    private List<BaseStationLocationVo> entity2Vo(List<BaseStationLocationEntity> entityList) {
        //组装返回结果
        String[] networkTypeArr = I18nUtils.getMessage("base.station.network.type").split(",");
        String[] gradeArr = I18nUtils.getMessage("base.station.grade").split(",");
        String unknown = I18nUtils.getMessage("base.station.unknown");

        return entityList.stream().map(baseStationLocationEntity -> {
            BaseStationLocationVo baseStationLocationInfoVo = new BaseStationLocationVo();
            BeanUtil.copyProperties(baseStationLocationEntity, baseStationLocationInfoVo);

            baseStationLocationInfoVo.setStationNo(baseStationLocationEntity.getStationNo());
            baseStationLocationInfoVo.setLatitude(baseStationLocationEntity.getLatitude().stripTrailingZeros());
            baseStationLocationInfoVo.setLongitude(baseStationLocationEntity.getLongitude().stripTrailingZeros());

            if (UNKNOWN.equals(baseStationLocationEntity.getNetworkType())) {
                baseStationLocationInfoVo.setNetworkTypeName(unknown);
            }else {
                baseStationLocationInfoVo.setNetworkTypeName(networkTypeArr[baseStationLocationEntity.getNetworkType() - 1]);
            }

            if (UNKNOWN.equals(baseStationLocationEntity.getGrade())) {
                baseStationLocationInfoVo.setGradeName(unknown);
            }else {
                baseStationLocationInfoVo.setGradeName(gradeArr[baseStationLocationEntity.getGrade() - 1]);
            }

            if (null != baseStationLocationEntity.getNetworkOperator()) {
                if (UNKNOWN.equals(baseStationLocationEntity.getNetworkOperator())) {
                    baseStationLocationInfoVo.setNetworkOperatorName(unknown);
                }else {
                    baseStationLocationInfoVo.setNetworkOperatorName(NetworkOperatorEnum.getNameByCode(baseStationLocationEntity.getNetworkOperator()));
                }
            }

            baseStationLocationInfoVo.setModifyTimeName(DateUtil.format(baseStationLocationEntity.getModifyTime(), DateUtil.YYYY_MM_DD_HH_MM_SS_FR_DZ));
            return baseStationLocationInfoVo;
        }).collect(Collectors.toList());
    }

    @Override
    public Object importBatch(MultipartFile file, Integer userId, Integer type) {
        log.info("start import base station location info");
        try {
            long t1 = System.currentTimeMillis();

            List<BaseStationExcelModel> dataList =  new ArrayList<>();

            EasyExcel.read(file.getInputStream(), BaseStationExcelModel.class, new AnalysisEventListener<BaseStationExcelModel>() {
                @Override
                public void invoke(BaseStationExcelModel data, AnalysisContext context) {
                    dataList.add(data);
                }

                // 全部读取完成就调用该方法
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("import Batch base station, parse excel Read total cost {}ms", (System.currentTimeMillis() - t1));
                }
            }).sheet().doRead();

            //成功插入或更新条数
            int successCount = 0;
            //校验失败条数
            int failCount = 0;

            long t3 = System.currentTimeMillis();

            String filePath = "";
            if (CollUtil.isNotEmpty(dataList)) {
                //数据校验，拆分新增和修改以及错误数据
                Map<String, BaseStationLocationEntity> addMap = new HashMap<>();
                Map<Integer, BaseStationLocationEntity> updateMap = new HashMap<>();
                List<BaseStationExcelModel> checkFailList = new ArrayList<>();
                List<ImportExcelErrorMsgModel> errorMsgList = new ArrayList<>();
                validatorDataList(dataList, addMap, updateMap, checkFailList, errorMsgList);

                long t4 = System.currentTimeMillis();
                log.info("import Batch base station, Validate data total cost {}ms", (t4 - t3));

                if (MapUtil.isNotEmpty(addMap)) {
                    successCount += addMap.size();

                    //新增数据
                    insertNewBaseStationData(addMap);

                    long t5 = System.currentTimeMillis();
                    log.info("import Batch base station, save batch total cost {}ms", (t5 - t4));
                }

                long t6 = System.currentTimeMillis();
                if (MapUtil.isNotEmpty(updateMap)) {
                    successCount += updateMap.size();

                    //覆盖已有数据
                    for (Map.Entry<Integer, BaseStationLocationEntity> entry : updateMap.entrySet()) {
                        Integer id = entry.getKey();
                        BaseStationLocationEntity baseStationLocationEntity = entry.getValue();
                        baseStationLocationEntity.setId(id);
                        this.updateById(baseStationLocationEntity);
                    }

                    long t7 = System.currentTimeMillis();
                    log.info("import Batch base station, update record total cost {}ms", (t7 - t6));
                }

                if (CollUtil.isNotEmpty(checkFailList)) {
                    failCount += checkFailList.size();

                    //校验失败的数据写入原有模板文件中
                    ClassPathResource resource = new ClassPathResource("file/CGI_template.xlsx");

                    try (InputStream templateInputStream = resource.getInputStream();
                         XSSFWorkbook workbook = new XSSFWorkbook(templateInputStream)){
                        // 获取第一个sheet
                        XSSFSheet sheet = workbook.getSheetAt(0);
                        // 从第二行开始写入数据
                        int rowNum = 1;

                        //创建绘图对象
                        XSSFDrawing drawing = sheet.createDrawingPatriarch();

                        for (int i = 0; i < checkFailList.size(); i++) {
                            XSSFRow row = sheet.createRow(rowNum++);

                            BaseStationExcelModel baseStationExcelModel = checkFailList.get(i);
                            row.createCell(0).setCellValue(baseStationExcelModel.getCgi());
                            row.createCell(1).setCellValue(baseStationExcelModel.getLatitude());
                            row.createCell(2).setCellValue(baseStationExcelModel.getLongitude());
                            row.createCell(3).setCellValue(baseStationExcelModel.getCellName());
                            row.createCell(4).setCellValue(baseStationExcelModel.getStationAddress());
                            row.createCell(5).setCellValue(baseStationExcelModel.getCoverageRadius());
                            row.createCell(6).setCellValue(baseStationExcelModel.getDescription());

                            //获取对应行的批注
                            int errorRow = i + 1;
                            List<ImportExcelErrorMsgModel> errorList = errorMsgList.stream().filter(errorMsg -> errorMsg.getRow() == errorRow).collect(Collectors.toList());

                            if (CollUtil.isNotEmpty(errorList)) {
                                for (ImportExcelErrorMsgModel errorMsgModel : errorList) {
                                    XSSFCell cell = row.getCell(errorMsgModel.getColumn());
                                    XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                    cellComment.setAuthor("CBP");
                                    cellComment.setString(new XSSFRichTextString(errorMsgModel.getErrorMsg()));
                                    cell.setCellComment(cellComment);
                                }
                            }
                        }

                        // 定义新文件路径
                        filePath = "base_station/" + "CGI_template_import_failed_" + DateUtil.format(new Date(), DateUtil.YYYYMMDDHHMMSS) + ".xlsx";

                        // 写入到新文件
                        try (FileOutputStream fos = new FileOutputStream(ftpFileRootPath + filePath)) {
                            workbook.write(fos);
                        }
                    }catch (Exception e) {
                        log.error("create check fail Excel error ", e);
                    }
                }
            }

            //导入完成后,发送消息通知
            sendEndMessage(userId, successCount, failCount, filePath);

            log.info("import base station location info end. total cost:{}ms", (System.currentTimeMillis() - t1));
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.info("import error: ", e);
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.fail"));
        }
    }

    /**
     * 校验数据并导入数据分为新增数据、修改数据，以及不合规数据;合规数据如果和库中现有数据编号相同则覆盖,导入数据中存在编号相同的数据则覆盖
     * @param dataList 导入数据
     * @param addMap 新增数据
     * @param updateMap 修改数据
     * @param checkFailList 不合规数据
     */
    private void validatorDataList(List<BaseStationExcelModel> dataList, Map<String, BaseStationLocationEntity> addMap, Map<Integer, BaseStationLocationEntity> updateMap, List<BaseStationExcelModel> checkFailList, List<ImportExcelErrorMsgModel> errorMsgList) {
        //获取所有未删除的基站信息
        List<BaseStationLocationEntity> existBaseStationList = this.baseMapper.selectList(new QueryWrapper<BaseStationLocationEntity>().select("id,station_no").eq("is_del", 0));
        Map<String, Integer> existBaseStationMap = existBaseStationList.stream().collect(Collectors.toMap(BaseStationLocationEntity::getStationNo, BaseStationLocationEntity::getId));

        int row = 1;
        for (BaseStationExcelModel baseStationExcelModel : dataList) {
            //不合规标记
            boolean checkSuccess = true;
            //数据更新标记
            boolean updateFlag = false;

            //基站编号校验要点1、基站编号不能为空2、基站编号长度不能超过64;3、基站编号重复则覆盖
            String stationNo = baseStationExcelModel.getCgi();
            if (StrUtil.isBlank(stationNo)) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_STATION_NO_COLUMN_INDEX).errorMsg("required field").build());
            } else if (stationNo.trim().length() > 64) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_STATION_NO_COLUMN_INDEX).errorMsg("too long").build());
            } else if (existBaseStationMap.containsKey(baseStationExcelModel.getCgi().trim())) {
                updateFlag = true;
            } else {
                stationNo = stationNo.trim();
            }

            String latitude = baseStationExcelModel.getLatitude();
            if (StrUtil.isBlank(latitude)) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_LAT_COLUMN_INDEX).errorMsg("required field").build());
            } else if (!PATTERN_LAT_REGEX.matcher(latitude.trim()).matches()) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_LAT_COLUMN_INDEX).errorMsg("format error").build());
            } else {
                latitude = BigDecimal.valueOf(Double.parseDouble(latitude.trim())).setScale(8, RoundingMode.HALF_UP).toString();
            }

            String longitude = baseStationExcelModel.getLongitude();
            if (StrUtil.isBlank(longitude)) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_LNG_COLUMN_INDEX).errorMsg("required field").build());
            } else if (!PATTERN_LNG_REGEX.matcher(baseStationExcelModel.getLongitude()).matches()) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_LNG_COLUMN_INDEX).errorMsg("format error").build());
            } else {
                longitude = BigDecimal.valueOf(Double.parseDouble(longitude.trim())).setScale(8, RoundingMode.HALF_UP).toString();
            }

            String stationAddress = baseStationExcelModel.getStationAddress();
            if (StrUtil.isBlank(stationAddress)) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_STATION_ADDRESS_COLUMN_INDEX).errorMsg("required field").build());
            } else if (stationAddress.trim().length() > 512) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_STATION_ADDRESS_COLUMN_INDEX).errorMsg("too long").build());
            } else {
                stationAddress = stationAddress.trim();
            }

            String coverageRadius = baseStationExcelModel.getCoverageRadius();
            if (StrUtil.isNotBlank(coverageRadius) && !PATTERN_COVERAGE_RADIUS_REGEX.matcher(coverageRadius.trim()).matches()) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_COVERAGE_RADIUS_COLUMN_INDEX).errorMsg("format error").build());
            }

            String cellName = StrUtil.isEmpty(baseStationExcelModel.getCellName()) ? "" : baseStationExcelModel.getCellName().trim();
            if (StrUtil.isNotBlank(cellName) && cellName.length() > 64) {
                checkSuccess = false;
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_CELL_NAME_COLUMN_INDEX).errorMsg("too long").build());
            }

            String description = StrUtil.isEmpty(baseStationExcelModel.getDescription()) ? "" : baseStationExcelModel.getDescription().trim();

            if (checkSuccess) {
                BaseStationLocationEntity baseStationLocation = new BaseStationLocationEntity();
                baseStationLocation.setStationNo(stationNo);
                baseStationLocation.setCgi(stationNo);
                baseStationLocation.setLatitude(BigDecimal.valueOf(Double.parseDouble(latitude)));
                baseStationLocation.setLongitude(BigDecimal.valueOf(Double.parseDouble(longitude)));
                if (StrUtil.isNotBlank(coverageRadius)) {
                    baseStationLocation.setCoverageRadius(BigDecimal.valueOf(Double.parseDouble(coverageRadius.trim())));
                }
                baseStationLocation.setNetworkOperator(UserLocationInfoUtil.getNetworkOperator(stationNo));
                baseStationLocation.setStationAddress(stationAddress);
                baseStationLocation.setCellName(cellName);
                baseStationLocation.setDescription(description);
                baseStationLocation.setIsDel(0);
                baseStationLocation.setModifyTime(System.currentTimeMillis());

                if (updateFlag) {
                    updateMap.put(existBaseStationMap.get(stationNo), baseStationLocation);
                } else {
                    baseStationLocation.setCreateTime(System.currentTimeMillis());
                    baseStationLocation.setGrade(0);
                    baseStationLocation.setNetworkType(0);

                    addMap.put(stationNo, baseStationLocation);
                }
            } else {
                row++;
                checkFailList.add(baseStationExcelModel);
            }
        }
    }

    private void insertNewBaseStationData(Map<String, BaseStationLocationEntity> addMap) {
        List<BaseStationLocationEntity> baseStationLocationEntities = new ArrayList<>(addMap.values());

        //批量插入基站位置
        //5000行作为一个批次，超过5000行再分批次插入
        int batchSize = baseStationLocationEntities.size() / importBatchRows;
        int remainder = baseStationLocationEntities.size() % importBatchRows;

        SqlSession sqlSession = sqlSessionFactory.openSession(false);
        BaseStationLocationMapper mapper = sqlSession.getMapper(BaseStationLocationMapper.class);
        try {
            for (int i = 0; i < batchSize; i++) {
                try {
                    List<BaseStationLocationEntity> batchList = baseStationLocationEntities.subList(i * importBatchRows, (i + 1) * importBatchRows);
                    // 手动提交事务
                    sqlSession.commit();
                    mapper.insertBatch(batchList);
                }catch (Exception e) {
                    sqlSession.rollback();
                }
            }

            if (remainder > 0) {
                try {
                    List<BaseStationLocationEntity> batchList = baseStationLocationEntities.subList(batchSize * importBatchRows, batchSize * importBatchRows + remainder);

                    // 手动提交事务
                    sqlSession.commit();
                    mapper.insertBatch(batchList);
                }catch (Exception e) {
                    sqlSession.rollback();
                }
            }
        }catch (Exception e) {
            log.error("save batch error: ", e);
        }finally {
            sqlSession.close();
        }
    }

    private void sendEndMessage(Integer userId, int successCount, int failCount, String filePath) {
        String msg = IMPORT_MESSAGE_TITLE + ":" + "Tâche d'importation terminée." +
                " " + successCount + " enregistrements importés avec succès";

        if (StrUtil.isNotEmpty(filePath)) {
            filePath = fileServerAddr + "download/" + filePath;
            msg += ", " + "<a href=\"" + filePath + "\" target=\"_blank\" title=\"Veuillez télécharger, apporter des corrections, puis réimporter\" >" +
                    failCount + "</a>" + " enregistrements échoués";
        } else {
            msg += ", " + failCount + " enregistrements échoués";
        }
        sendMsg(userId.toString(), msg);
    }

    @Override
    public void sendStartMessage(Integer userId) {
        String msg = IMPORT_MESSAGE_TITLE + ":" + IMPORT_MESSAGE_START_CONTENT;
        sendMsg(userId.toString(), msg);
    }

    private void sendMsg(String userId, String msg) {
        log.info("send userId:{}, message: {}", userId, msg);
        AddMessageModel addMessageModel = new AddMessageModel();
        addMessageModel.setMessageType(1);
        addMessageModel.setType(2);
        addMessageModel.setHandleUrl(null);
        List<MessageModel> mesList = Lists.newArrayList();
        MessageModel messageModel = new MessageModel();
        messageModel.setUserId(Integer.parseInt(userId));
        List<String> collect = Lists.newArrayList();
        collect.add(msg);
        messageModel.setContent(collect);
        mesList.add(messageModel);
        addMessageModel.setMessage(mesList);

        try {
            msgFeignClient.sendMessageBatchUser(addMessageModel);
        }catch (Exception e) {
            log.error("send message error: ", e);
        }
        log.info("send message success");
    }
}
