package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.CallClueEntity;
import com.semptian.mapper.CallClueMapper;
import com.semptian.service.CallClueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * call线索服务提供者
 *
 * @Author: sk
 * @Date: 2020/12/15 15:43
 */

@Slf4j
@Service
public class CallClueServiceImpl extends ServiceImpl<CallClueMapper, CallClueEntity> implements CallClueService {
    @Override
    public int modifyCallNumber(CallClueEntity callClueEntity) {
        return baseMapper.modifyCallNumber(callClueEntity);
    }
}
