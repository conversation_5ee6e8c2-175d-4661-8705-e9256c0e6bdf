package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.*;
import com.semptian.enums.CommonReturnCode;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.NumberStatusEnum;
import com.semptian.feign.portal.PortalInterfaceService;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.ClueIpFilterTableMapper;
import com.semptian.mapper.ClueTableModelMapper;
import com.semptian.param.IpExcelModel;
import com.semptian.param.UpdateIpModel;
import com.semptian.service.ClueIpFilterTableService;
import com.semptian.service.ClueService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @author: ZC
 * @date: 2020/12/17 14:58
 */
@Slf4j
@Service
public class ClueIpFilterTableServiceImpl extends ServiceImpl<ClueIpFilterTableMapper, ClueIpFilterTableEntity> implements ClueIpFilterTableService {

    @Autowired
    private ClueTableModelMapper clueTableModelMapper;

    @Autowired
    private ClueService clueService;

    @Autowired
    private PortalInterfaceService portalInterfaceService;

    @Resource
    private ClueIpFilterTableService clueIpFilterTableService;

    private static String CLUE_LEVEL = "ordinaryrule";
    private static Integer STATE_DELETE = 32;
    private static String ALL_EXPORT = "-1";
    private static int CSV_EXPORT = 0;
    private static int EXCEL_EXPORT = 1;
    private static int TXT_EXPORT = 2;
    private static String FILE_NAME_CSV = "IPWhiteList.csv";
    private static String FILE_NAME_EXCEL = "IPWhiteList";
    private static String FILE_NAME_TXT = "IPWhiteList.txt";
    private static int CLUE_TYPE = 249;
    private static int CLUE_FROM = 6;

    /**
     * 排序字段
     */
    private static final String IP_ADDR = "ipAddr";
    private static final String IP_MASK = "ipMask";
    private static final String CLUE_STATE = "clueState";
    private static final String UPDATE_TIME = "updateTime";
    private static final String EXPIRE_START_TIME = "expireStartTime";
    private static final String EXPIRE_END_TIME = "expireEndTime";


    /**
     * 增加ip白名单
     *
     * @param clueIpFilterTableEntity
     * @param userId
     * @return
     */
    @Override
    @SuppressWarnings(value = "all")
    public Object insertIpWhite(ClueIpFilterTableEntity clueIpFilterTableEntity, Integer userId) {

        String account = "";
        Map<String, Object> data = portalInterfaceService.getUserInfoByUserId(userId);
        if (null != data && !data.isEmpty()) {
            account = data.getOrDefault("account", "").toString();
        }

        Long clueId = clueService.nextVal();
        long timeMillis = System.currentTimeMillis();
        long insertTime = timeMillis / 1000;
        ClueTableModel clueTableModel = new ClueTableModel();
        clueTableModel.setClueId(Integer.valueOf(String.valueOf(clueId)));
        clueTableModel.setClueName(clueIpFilterTableEntity.getIpAddr());
        clueTableModel.setClueFrom(CLUE_FROM);
        clueTableModel.setClueLevel(CLUE_LEVEL);
        clueTableModel.setClueType(CLUE_TYPE);
        clueTableModel.setDataCount(0);
        clueTableModel.setObjectId(-10);
        clueTableModel.setProcMethod(0);
        clueTableModel.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
        clueTableModel.setClueAddName(account);
        clueTableModel.setCreateTime(insertTime);
        clueTableModel.setUpdateTime(insertTime);
        //添加ClueTable表数据
        int i = clueTableModelMapper.insertMsgIpClueTable(clueTableModel);
        int j = 0;
        //IP重复判断
        QueryWrapper<ClueIpFilterTableEntity> wrapper = new QueryWrapper();
        wrapper.eq("IPADDR", clueIpFilterTableEntity.getIpAddr());
        Integer integer = this.baseMapper.selectCount(wrapper);
        if (integer > 0) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("ip.address.repeat"));
        }
        if (i > 0) {
            ClueIpFilterTableEntity cip = new ClueIpFilterTableEntity();
            cip.setClueId(clueTableModel.getClueId());
            if (clueIpFilterTableEntity.getExpireStartTime() != null) {
                cip.setExpireStartTime(clueIpFilterTableEntity.getExpireStartTime() / 1000);
            } else {

                cip.setExpireStartTime(insertTime);
            }
            cip.setIpAddr(clueIpFilterTableEntity.getIpAddr());
            cip.setIpMask(clueIpFilterTableEntity.getIpMask());
            if (clueIpFilterTableEntity.getIpState() == 2) {
                cip.setExpireEndTime(null);
                //默认1 短期,2长期
                cip.setIpState(clueIpFilterTableEntity.getIpState());
            } else {
                long endTime = clueIpFilterTableEntity.getExpireEndTime() / 1000;
                cip.setExpireEndTime(endTime);
                //默认1 短期,2长期
                cip.setIpState(clueIpFilterTableEntity.getIpState());
            }

            cip.setUserId(userId);
            //添加ip白名单表数据
            j = this.baseMapper.insert(cip);
            if (j > 0) {
                return ReturnModel.getInstance().ok();
            }
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("ip.add.white.failure"));
    }

    /**
     * 删除-支持批量
     *
     * @param ids
     * @return
     */
    @Override
    public Object deleteIpWhite(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }
        //转换String的ids为集合
        List<String> clueIds = StringChangeListModel.splitStrToLost(ids);
        List<Integer> idList = new ArrayList<>();
        for (String clueId : clueIds) {
            Integer integer = Integer.valueOf(clueId);
            idList.add(integer);
        }
        List<ClueTableModel> models = clueTableModelMapper.selectList(new QueryWrapper<ClueTableModel>().in("CLUEID", idList));
        for (ClueTableModel model : models) {
            if (model.getClueState() == NumberStatusEnum.ON.getCode()) {
                return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("status.notDelete"));
            }
            if (model.getClueState() == NumberStatusEnum.WAITFORCONTROL.getCode() || model.getClueState() == NumberStatusEnum.WAITFORCANCELCONTROL.getCode()) {
                return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("inProcess.data.cannot.delete"));
            }
        }
        //删除ip白名单表的此id数据
        Integer integer = this.baseMapper.deleteBatchIds(idList);
        long timeMillis = System.currentTimeMillis();
        long insertTime = timeMillis / 1000;
        if (integer > 0) {
            //改变ClueTable表的状态为32待删除
            clueTableModelMapper.updateStateByIds(idList, STATE_DELETE, insertTime);
            return ReturnModel.getInstance().ok();
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("ip.delete.white.failure"));
    }

    /**
     * 修改状态
     *
     * @param updateIpModel
     * @return
     */
    @Override
    public Object updateIpWhiteState(UpdateIpModel updateIpModel) {

        if (StringUtils.isEmpty(updateIpModel)) {
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }

        long timeMillis = System.currentTimeMillis();
        long insertTime = timeMillis / 1000;
        //转换String的ids为集合
        List<String> clueIds = StringChangeListModel.splitStrToLost(updateIpModel.getIds());
        List<Integer> idList = new ArrayList<>();
        for (String clueId : clueIds) {
            idList.add(Integer.valueOf(clueId));
        }
        //查询是否有过期IP
        QueryWrapper<ClueIpFilterTableEntity> ipr = new QueryWrapper<>();
        ipr.in("CLUEID", idList);
        List<ClueIpFilterTableEntity> clueIpFilterTableEntities = this.baseMapper.selectList(ipr);
        for (ClueIpFilterTableEntity clueIpFilterTableEntity : clueIpFilterTableEntities) {
            if (clueIpFilterTableEntity.getExpireEndTime() != null && clueIpFilterTableEntity.getExpireEndTime() < insertTime) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("time.out.ip.notInControl"));
            }
        }

        //防止重复设置状态
        QueryWrapper<ClueTableModel> wr = new QueryWrapper<>();
        if (idList.size() > EXCEL_EXPORT) {
            wr.in("CLUEID", idList).ne("CLUESTATE", updateIpModel.getToStatus());
            Integer integer = clueTableModelMapper.selectCount(wr);
            if (integer != idList.size()) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("repeat.state.notDo"));
            }
        }


        if (idList.size() == EXCEL_EXPORT) {
            wr.eq("CLUEID", idList.get(0)).eq("CLUESTATE", updateIpModel.getToStatus());
            Integer integer = clueTableModelMapper.selectCount(wr);
            if (integer > CSV_EXPORT) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("repeat.state.notDo"));
            }
        }

        boolean i;
//        List<ClueTableModel> tableModels = clueTableModelMapper.select(new QueryWrapper<ClueTableModel>().in("CLUEID", idList));
        //更新线索状态

        if (updateIpModel.getToStatus() == NumberStatusEnum.ON.getCode()) {
            i = clueTableModelMapper.updateStateByIds(idList, NumberStatusEnum.WAITFORCONTROL.getCode(), insertTime);

        } else {
            i = clueTableModelMapper.updateStateByIds(idList, NumberStatusEnum.WAITFORCANCELCONTROL.getCode(), insertTime);
        }
        if (i) {
            return ReturnModel.getInstance().ok();
        }


        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("failed.to.modify.state"));

    }

    /**
     * 查询
     *
     * @param ipAddr
     * @param startTime
     * @param endTime
     * @param onPage
     * @param size
     * @param orderField
     * @return
     */
    @Override
    @SuppressWarnings(value = "all")
    public Object selectIpWhiteMsg(String ipAddr, Long startTime, Long endTime, Integer onPage, Integer size, String orderField, Integer orderType) {
        HashMap<String, Object> map = new HashMap<>(20);
        if (startTime != null && endTime != null) {
            startTime = startTime / 1000;
            endTime = endTime / 1000;
        }
        String sortType = "";
        //排序字段
        String orderFieldName = orderFieldName(orderField);

        Integer startIndex = (onPage - 1) * size;
        Integer endIndex = onPage * size;

        if (orderType == 1) {
            sortType = "asc";
        } else {
            sortType = "desc";
        }

        List<IpWhiteMsgModel> ipWhiteMsgModelList = this.baseMapper.selectAllIpMsg(ipAddr, startTime, endTime, startIndex, endIndex, sortType, orderFieldName);
        if (CollectionUtils.isEmpty(ipWhiteMsgModelList)) {
            map.put("total", 0);
            map.put("records", ipWhiteMsgModelList);
            return ReturnModel.getInstance().ok().setData(map);
        }
        for (IpWhiteMsgModel ipWhiteMsgModel : ipWhiteMsgModelList) {
            if (ipWhiteMsgModel.getClueState() == 8) {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
            } else {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
            }
        }
        List<IpWhiteMsgModel> countList = this.baseMapper.selectAllIp(ipAddr, startTime, endTime);
        map.put("total", countList.size());
        map.put("records", ipWhiteMsgModelList);
        return ReturnModel.getInstance().ok().setData(map);


    }

    /**
     * 编辑
     *
     * @param clueIpFilterTableEntity
     * @return
     */
    @Override
    public Object updateIpWhiteMsg(ClueIpFilterTableEntity clueIpFilterTableEntity) {

        if (StringUtils.isEmpty(clueIpFilterTableEntity)) {
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }

        long timeMillis = System.currentTimeMillis();
        long insertTime = timeMillis / 1000;
        if (clueIpFilterTableEntity.getExpireEndTime() != null && clueIpFilterTableEntity.getIpState() == 1) {
            long endTime = clueIpFilterTableEntity.getExpireEndTime() / 1000;
//            long startTime = clueIpFilterTableEntity.getExpireStartTime() / 1000;
//            clueIpFilterTableEntity.setExpireStartTime(startTime);
            clueIpFilterTableEntity.setExpireEndTime(endTime);
        }

        ClueIpFilterTableEntity cf = this.baseMapper.selectById(clueIpFilterTableEntity.getClueId());
        Long st = cf.getExpireStartTime();
        if (!st.equals(clueIpFilterTableEntity.getExpireStartTime()) && clueIpFilterTableEntity.getExpireStartTime() != null) {
            long startTime = clueIpFilterTableEntity.getExpireStartTime() / 1000;
            clueIpFilterTableEntity.setExpireStartTime(startTime);
        } else if (clueIpFilterTableEntity.getIpState() == 2 && !st.equals(clueIpFilterTableEntity.getExpireStartTime())) {
            clueIpFilterTableEntity.setExpireEndTime(null);
            clueIpFilterTableEntity.setExpireStartTime(insertTime);
        }

//        Wrapper wrapper = new QueryWrapper<ClueIpFilterTableEntity>();
//        wrapper.eq("CLUEID", clueIpFilterTableEntity.getClueId());
//        Integer update = this.baseMapper.update(clueIpFilterTableEntity, wrapper);
        Integer update = this.baseMapper.updateById(clueIpFilterTableEntity);

        if (update > 0) {
            int i = clueTableModelMapper.updateTimeIpWhiteName(insertTime, clueIpFilterTableEntity.getClueId(), clueIpFilterTableEntity.getIpAddr());
            if (i > 0) {
                return ReturnModel.getInstance().ok();
            }
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("ip.edit.failure"));

    }

    /**
     * 查看详情
     *
     * @param clueId
     * @return
     */
    @Override
    public Object selectIpById(Integer clueId) {

        if (StringUtils.isEmpty(clueId)) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }
        IpDetailModel ipDetailModel = this.baseMapper.selectDetailMsg(clueId);
        if (ipDetailModel.getClueState() == 8) {
            ipDetailModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
        } else {
            ipDetailModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
        }
        return ReturnModel.getInstance().ok().setData(ipDetailModel);

    }

    /**
     * 校验IP合法以及是否存在
     *
     * @param ipAddr
     * @return
     */
    @Override
    @SuppressWarnings(value = "all")
    public Object ipWhiteCheck(String ipAddr, String clueId) {

        if (StringUtils.isEmpty(ipAddr)) {
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }
        //clueId为空就是 新增校验
        if (StringUtils.isEmpty(clueId)) {
            String ipRegex = "^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])\\.){3}(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])$";
            Pattern p1 = Pattern.compile(ipRegex);
            if (!p1.matcher(ipAddr).find()) {
                return ReturnModel.getInstance().ok().setData(true).setMsg(I18nUtils.getMessage("ip.illegal.or.null"));
            }
            QueryWrapper<ClueIpFilterTableEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("IPADDR", ipAddr);
            Integer integer = this.baseMapper.selectCount(wrapper);
            if (integer > 0) {
                return ReturnModel.getInstance().ok().setData(true).setMsg(I18nUtils.getMessage("ip.address.repeat"));
            }
        } else {
            String ipRegex = "^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])\\.){3}(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])$";
            Pattern p1 = Pattern.compile(ipRegex);
            if (!p1.matcher(ipAddr).find()) {
                return ReturnModel.getInstance().ok().setData(true).setMsg(I18nUtils.getMessage("ip.illegal.or.null"));
            }
            QueryWrapper<ClueIpFilterTableEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("IPADDR", ipAddr).ne("CLUEID", clueId);
            Integer integer = this.baseMapper.selectCount(wrapper);
            if (integer > 0) {
                return ReturnModel.getInstance().ok().setData(true).setMsg(I18nUtils.getMessage("ip.address.repeat"));
            }
        }
        return ReturnModel.getInstance().ok().setData(false);

    }

    /**
     * 校验 Mask
     *
     * @param ipMask
     * @return
     */
    @Override
    public Object maskCheck(String ipMask) {
        if (StringUtils.isEmpty(ipMask)) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }
        String maskRegex = "^((128|192)|2(24|4[08]|5[245]))(\\.(0|(128|192)|2((24)|(4[08])|(5[245])))){3}$";
        Pattern p2 = Pattern.compile(maskRegex);
        if (!p2.matcher(ipMask).find()) {
            return ReturnModel.getInstance().ok().setData(true).setMsg(I18nUtils.getMessage("mask.illegal.or.null"));
        }
        return ReturnModel.getInstance().ok().setData(false);
    }


    @Override
    @SuppressWarnings(value = "all")
    public Object ipMsgExport(String ids, Integer type, HttpServletResponse response) {

        if (StringUtils.isEmpty(ids)) {
            return ReturnModel.getInstance().error().setCode(CommonReturnCode.PARAM_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }
        try {
            //全部导出
            if (ids.equals(ALL_EXPORT)) {
                List<IpWhiteMsgModel> ipWhiteMsgModelList = this.baseMapper.selectAllExport();
                if (type == CSV_EXPORT) {
                    String ipExportName = ipExportName(FILE_NAME_CSV);
                    File file = csvFileIp(ipWhiteMsgModelList, null, ipExportName);
                    exportToCsv(response, file, file.getName());
                    //删除保存在项目的文件
                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                } else if (type == EXCEL_EXPORT) {
                    String ipExportName = ipExportName(FILE_NAME_EXCEL);
                    File file = excelFileIp(ipWhiteMsgModelList, null, ipExportName);
                    exportToCsv(response, file, file.getName());
                    //删除保存在项目的文件
                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                } else if (type == TXT_EXPORT) {
                    String ipExportName = ipExportName(FILE_NAME_TXT);
                    File file = txtIpFile(ipWhiteMsgModelList, ipExportName);
                    exportToCsv(response, file, file.getName());
                    //删除保存在项目的文件
                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                }

            }
            //非全部导出
            List<String> clueIds = StringChangeListModel.splitStrToLost(ids);
            List<IpWhiteMsgModel> ipWhiteMsgModelList = this.baseMapper.selectExport(clueIds);
            if (type == CSV_EXPORT) {
                //CSV导出
                String ipExportName = ipExportName(FILE_NAME_CSV);
                File file = csvFileIp(ipWhiteMsgModelList, null, ipExportName);
                exportToCsv(response, file, file.getName());
                //删除保存在项目的文件
                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();
            } else if (type == EXCEL_EXPORT) {
                //Excel导出
                String ipExportName = ipExportName(FILE_NAME_EXCEL);
                File file = excelFileIp(ipWhiteMsgModelList, null, ipExportName);
                exportToCsv(response, file, file.getName());
                //删除保存在项目的文件
                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();
            } else if (type == TXT_EXPORT) {
                //TXT导出
                String ipExportName = ipExportName(FILE_NAME_TXT);
                File file = txtIpFile(ipWhiteMsgModelList, ipExportName);
                exportToCsv(response, file, file.getName());
                //删除保存在项目的文件
                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();
            }
            return ReturnModel.getInstance().error().setCode(CommonReturnCode.EXPORT_ERROR.getCode()).setMsg(I18nUtils.getMessage("export.failure"));
        } catch (Exception e) {
            return ReturnModel.getInstance().error().setCode(CommonReturnCode.EXPORT_ERROR.getCode()).setMsg(I18nUtils.getMessage("export.failure"));
        }
    }


    /**
     * 导入
     *
     * @param file
     * @param userId
     * @param type
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings(value = "all")
    @Override
    public Object importBatch(MultipartFile file, Integer userId, Integer type) {

        List<ClueIpFilterTableEntity> ipList = Lists.newArrayList();
        List<ClueEntity> clList = Lists.newArrayList();
        long time = System.currentTimeMillis() / 1000;
        String account = "";
        Map<String, Object> data = portalInterfaceService.getUserInfoByUserId(userId);
        if (null != data && !data.isEmpty()) {
            account = data.getOrDefault("account", "").toString();
        }
        try {
            // 添加一下国际化的导入语言
            ExcelUtil<IpExcelModel> excelUtil = new ExcelUtil<>(IpExcelModel.class);
            List<IpExcelModel> excelModelList = excelUtil.importExcel(file.getInputStream(),type);
            if (CollectionUtils.isEmpty(excelModelList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.isEmpty"));
            }
            //文档IP去重
            Set<IpExcelModel> set = new TreeSet<>(Comparator.comparing(IpExcelModel::getIpAddr));
            set.addAll(excelModelList);

            for (IpExcelModel ipExcelModel : set) {
                //IP，mask必传项
                if (StringUtils.isEmpty(ipExcelModel.getIpAddr()) || StringUtils.isEmpty(ipExcelModel.getIpMask())) {
                    continue;
                }
                //组装IP表
                ClueIpFilterTableEntity cif = new ClueIpFilterTableEntity();
                cif.setIpAddr(ipExcelModel.getIpAddr());
                cif.setIpMask(ipExcelModel.getIpMask());
                //IP重复判断
                QueryWrapper<ClueIpFilterTableEntity> wrapper = new QueryWrapper();
                wrapper.eq("IPADDR", ipExcelModel.getIpAddr());
                Integer integer = this.baseMapper.selectCount(wrapper);
                if (integer > 0) {
                    continue;
                }
                //校验IP合法性
                boolean ip = findIp(ipExcelModel.getIpAddr());
                if (ip == true) {
                    continue;
                }
                //校验Mask掩码合法性
                boolean mask = findMask(ipExcelModel.getIpMask());
                if (mask == true) {
                    continue;
                }

                Long clueId = clueService.nextVal();
                Integer clueIdOne = Integer.valueOf(String.valueOf(clueId));
                cif.setClueId(clueIdOne);
                cif.setIpState(2);
                cif.setExpireStartTime(time);
                cif.setExpireEndTime(null);
                cif.setUserId(userId);
                ipList.add(cif);
                //组装线索表
                ClueEntity ce = new ClueEntity();
                ce.setClueId(clueId);
                ce.setClueName(ipExcelModel.getIpAddr());
                ce.setClueType(CLUE_TYPE);
                ce.setDataCount(0L);
                ce.setObjectId(-10L);
                ce.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
                ce.setCreateTime(time);
                ce.setUpdateTime(time);
                ce.setCreateUserId(Long.valueOf(userId));
                clList.add(ce);
            }
            //插入线索表
            if (CollectionUtils.isEmpty(clList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.content.isFalse"));
            }
            int i = 0;
            for (ClueEntity clueEntity : clList) {
                boolean insert = clueService.save(clueEntity);
                if (!insert) {
                    i++;
                }
            }
            if (i == 0) {
                //插入IP白名单表
                for (ClueIpFilterTableEntity clueIpFilterTableEntity : ipList) {
                    boolean b = clueIpFilterTableService.save(clueIpFilterTableEntity);
                    if (!b) {
                        i++;
                    }
                }
                if (i == 0) {
                    return ReturnModel.getInstance().ok();
                }
            }
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.white.isException"));
        } catch (Exception e) {
            log.info("import error , case {}",e);
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.white.isException"));
        }

    }

    /**
     * 检测过期IP并自动设置成停控状态
     */
    @Override
    public void ipExpireMonitor() {
        List<IpExpireModel> ipExpireModels = this.baseMapper.ipExpireMonitor();
        List<Integer> idList = Lists.newArrayList();
        long timeMillis = System.currentTimeMillis() / 1000;
        if (CollectionUtils.isEmpty(ipExpireModels)) {
            return;
        }
        ipExpireModels.forEach(s -> {
            if (s.getExpireEndTime() < timeMillis) {
                idList.add(s.getClueId());
            }
        });
        if (!CollectionUtils.isEmpty(idList)) {
            clueTableModelMapper.updateStateByIds(idList, NumberStatusEnum.WAITFORCANCELCONTROL.getCode(), timeMillis);
        }
        return;

    }


    public ExcelUtil<IpExcelModel> importWhite(Integer type) {
        ExcelUtil<IpExcelModel> excelUtil = new ExcelUtil<>(IpExcelModel.class);
        if (type == 0 || type == 1) {
            return excelUtil;
        } else if (type == 2) {
            return null;
        } else if (type == 3) {
            return null;
        }
        return excelUtil;
    }

    /**
     * 校验IP合法性
     *
     * @param ipAddr
     * @return
     */
    public boolean findIp(String ipAddr) {
        String ipRegex = "^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])\\.){3}(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])$";
        Pattern p1 = Pattern.compile(ipRegex);
        if (!p1.matcher(ipAddr).find()) {
            return true;
        }
        return false;
    }

    /**
     * 校验IPMask掩码合法性
     *
     * @param ipMask
     * @return
     */
    public boolean findMask(String ipMask) {
        String maskRegex = "^((128|192)|2(24|4[08]|5[245]))(\\.(0|(128|192)|2((24)|(4[08])|(5[245])))){3}$";
        Pattern p2 = Pattern.compile(maskRegex);
        if (!p2.matcher(ipMask).find()) {
            return true;
        }
        return false;
    }


    /**
     * CSV导出设置
     *
     * @param list
     * @param filePath
     * @param fileName
     * @return
     */
    @SuppressWarnings(value = "all")
    private File csvFileIp(List<IpWhiteMsgModel> list, String filePath, String fileName) {
        /**
         *  国际化表头
         */
        String TIME = I18nUtils.getMessage("updateTime");
        String STATE = I18nUtils.getMessage("clueStateStr");
        String MASK = I18nUtils.getMessage("IP.Mask");
        String IP = I18nUtils.getMessage("IP.Addr");
        LinkedHashMap map = new LinkedHashMap();
        map.put("ipAddr", IP);
        map.put("ipMask", MASK);
        map.put("clueStateStr", STATE);
        map.put("updateTime", TIME);
        List<IpWhiteMsgExport> whiteMsgExportList = new ArrayList<>();
        for (IpWhiteMsgModel ipWhiteMsgModel : list) {
            IpWhiteMsgExport ipWhiteMsgExport = new IpWhiteMsgExport();
            if (ipWhiteMsgModel.getClueState() == NumberStatusEnum.OFF.getCode()) {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
                ipWhiteMsgExport.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));

            } else if (ipWhiteMsgModel.getClueState() == NumberStatusEnum.ON.getCode()) {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
                ipWhiteMsgExport.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
            } else {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
                ipWhiteMsgExport.setClueStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
            }
            String s = "\t" + DateUtil.formatTimeSecond(ipWhiteMsgModel.getUpdateTime()) + "\t";
            ipWhiteMsgExport.setUpdateTime(s);
            ipWhiteMsgExport.setIpAddr(ipWhiteMsgModel.getIpAddr());
            ipWhiteMsgExport.setIpMask(ipWhiteMsgModel.getIpMask());
//            ipWhiteMsgExport.setClueState(ipWhiteMsgModel.getClueState());
            //将新的model添加
            whiteMsgExportList.add(ipWhiteMsgExport);
        }
        File csvFile = CsvUtils.createCsvFile(whiteMsgExportList, map, filePath, fileName);
        return csvFile;

    }

    /**
     * Excel导出设置
     *
     * @param list
     * @param filePath
     * @param fileName
     * @return
     * @throws Exception
     */
    @SuppressWarnings(value = "all")
    private File excelFileIp(List<IpWhiteMsgModel> list, String filePath, String fileName) throws Exception {
        /**
         *  国际化表头
         */
        String TIME = I18nUtils.getMessage("updateTime");
        String STATE = I18nUtils.getMessage("clueStateStr");
        String MASK = I18nUtils.getMessage("IP.Mask");
        String IP = I18nUtils.getMessage("IP.Addr");
        String[] propertis = {"ipAddr", "ipMask", "clueStateStr", "updateTime"};
        String[] colum = {IP, MASK, STATE, TIME};
        List<IpWhiteMsgExport> whiteMsgExportList = new ArrayList<>();
        for (IpWhiteMsgModel ipWhiteMsgModel : list) {
            IpWhiteMsgExport ipWhiteMsgExport = new IpWhiteMsgExport();
            if (ipWhiteMsgModel.getClueState() == NumberStatusEnum.OFF.getCode()) {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
                ipWhiteMsgExport.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));

            } else if (ipWhiteMsgModel.getClueState() == NumberStatusEnum.ON.getCode()) {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
                ipWhiteMsgExport.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
            } else {
                ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
                ipWhiteMsgExport.setClueStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
            }
            String s = DateUtil.formatTimeSecond(ipWhiteMsgModel.getUpdateTime());
            ipWhiteMsgExport.setUpdateTime(s);
            ipWhiteMsgExport.setIpAddr(ipWhiteMsgModel.getIpAddr());
            ipWhiteMsgExport.setIpMask(ipWhiteMsgModel.getIpMask());
//            ipWhiteMsgExport.setClueState(ipWhiteMsgModel.getClueState());
            //将新的model添加
            whiteMsgExportList.add(ipWhiteMsgExport);
        }
        File excel = ExcelCreateUtils.createExcel(whiteMsgExportList, propertis,colum, fileName + ".xls", filePath);
        return excel;
    }


    /**
     * 生成文件名
     *
     * @param name
     * @return
     */
    public static String ipExportName(String name) {
        Date date = new Date();
        SimpleDateFormat yyyyMMddHHmmss = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = yyyyMMddHHmmss.format(date);
        String fileName = format + name;
        return fileName;
    }

    /**
     * TXT文本导出设置
     *
     * @param list
     * @param fileName
     * @return
     */
    private File txtIpFile(List<IpWhiteMsgModel> list, String fileName) {
        /**
         *  国际化表头
         */
        String TIME = I18nUtils.getMessage("updateTime");
        String STATE = I18nUtils.getMessage("clueStateStr");
        String MASK = I18nUtils.getMessage("IP.Mask");
        String IP = I18nUtils.getMessage("IP.Addr");

        File outFile = new File(fileName);
        Writer writer=null;
        try {
            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile, true), StandardCharsets.UTF_8), 10240);
            writer.write(IP + "\t" + "\t" +
                    MASK + "\t" + "\t" +
                    STATE + "\t" +
                    TIME + "\r\n");
            IpWhiteMsgExport ipWhiteMsgExport = new IpWhiteMsgExport();
            for (IpWhiteMsgModel ipWhiteMsgModel : list) {
                if (ipWhiteMsgModel.getClueState() == NumberStatusEnum.OFF.getCode()) {
                    ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));

                } else if (ipWhiteMsgModel.getClueState() == NumberStatusEnum.ON.getCode()) {
                    ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
                } else {
                    ipWhiteMsgModel.setClueStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
                }
                String s = DateUtil.formatTimeSecond(ipWhiteMsgModel.getUpdateTime());
                ipWhiteMsgExport.setUpdateTime(s);
                writer.write(ipWhiteMsgModel.getIpAddr() + "\t" +
                        ipWhiteMsgModel.getIpMask() + "\t" +
                        ipWhiteMsgModel.getClueStateStr() + "\t" + "\t" +
                        ipWhiteMsgExport.getUpdateTime() + "\r\n");
            }
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }finally {
            if (writer!=null){
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("writer close error");
                }
            }

        }
        return outFile;
    }


    /**
     * 导出目标文件
     *
     * @param response 通过response对象输出流
     * @param file     目标文件
     */
    @SuppressWarnings(value = "all")
    private void exportToCsv(HttpServletResponse response, File file, String fName) throws IOException {
        ZipOutputStream zipos;
        DataOutputStream os = null;
        FileInputStream in = null;
        zipos = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()));
        //设置压缩方法
        zipos.setMethod(ZipOutputStream.DEFLATED);
        try {
            File file1;
            file1 = new File(file.getCanonicalPath());
            zipos.putNextEntry(new ZipEntry(fName));
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fName, "UTF-8"));
            response.setContentType("application/octet-stream");
            os = new DataOutputStream(zipos);
            in = new FileInputStream(file1);
            IOUtils.copy(in, os);
        } catch (IOException e) {
            log.warn("file is not exists");
            log.error("exportToCsv error: {}. error msg {}.", e, e.getMessage());
        } finally {
            if (os != null) {
                os.close();
            }
            zipos.close();
            if (in != null) {
                in.close();
            }
        }
    }


    public String orderFieldName(String orderField) {

        if (null != orderField) {
            if (orderField.equals(IP_ADDR)) {
                return "C1.IPADDR";
            }
            if (orderField.equals(IP_MASK)) {
                return "C1.IPMASK";
            }
            if (orderField.equals(CLUE_STATE)) {
                return "C2.CLUESTATE";
            }
            if (orderField.equals(UPDATE_TIME)) {
                return "C2.UPDATETIME";
            }
            if (orderField.equals(EXPIRE_START_TIME)) {
                return "C1.EXPIRESTARTTIME";
            }
            if (orderField.equals(EXPIRE_END_TIME)) {
                return "C1.EXPIREENDTIME";
            }
        }
        return "C2.UPDATETIME";

    }

}
