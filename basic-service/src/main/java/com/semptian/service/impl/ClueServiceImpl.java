package com.semptian.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.CallClueEntity;
import com.semptian.entity.ClueEntity;
import com.semptian.enums.NumberStatusEnum;
import com.semptian.mapper.ClueMapper;

import com.semptian.param.PhoneNumberModel;
import com.semptian.service.CallClueService;
import com.semptian.service.ClueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 线索数据库服务提供者
 *
 * @Author: sk
 * @Date: 2020/12/15 15:46
 */
@Service
@Slf4j
public class ClueServiceImpl extends ServiceImpl<ClueMapper, ClueEntity> implements ClueService {

    @Resource
    private ClueService clueService;
    @Resource
    private CallClueService callClueService;
    @Override
    public int queryWhiteNumberCount(String keyword,Long startTime,Long endTime) {
        return baseMapper.queryWhiteNumberCount(keyword,startTime,endTime);
    }

    @Override
    public List<ClueDto> queryWhiteNumberList(String keyword, int startIndex, int endIndex, String orderField, int orderType,Long startTime ,Long endTime) {
        return baseMapper.queryWhiteNumberList(keyword,startIndex, endIndex,orderField,orderType,startTime,endTime);
    }

    @Override
    public List<ClueDto> queryOrdinaryNumberList(String keyword, int startIndex, int endIndex, String orderField, int orderType, Long startTime,Long endTime) {
        return baseMapper.queryOrdinaryNumberList(keyword, startIndex, endIndex, orderField, orderType, startTime,endTime);
    }

    @Override
    public int queryOrdinaryNumberCount(String keyword, Long startTime,Long endTime) {
        return baseMapper.queryOrdinaryNumberCount(keyword, startTime,endTime);
    }

    @Override
    public boolean checkOrdinaryNumber(String telephoneNum, String countryCode, Long id) {
        return baseMapper.checkOrdinaryNumber(telephoneNum,countryCode,id) == 0 ? Boolean.FALSE : Boolean.TRUE;
    }

    @Override
    public Long nextVal() {
        return baseMapper.nextVal();
    }

    @Override
    public ClueDto queryOrdinaryNumberDetail(Long id) {
        return baseMapper.queryOrdinaryNumberDetail(id);
    }

    @Override
    public void modifyNumber(PhoneNumberModel phoneNumberModel) {



        ClueEntity clueEntity = new ClueEntity();

        BeanUtil.copyProperties(phoneNumberModel ,clueEntity);
        clueEntity.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
        clueEntity.setClueName(phoneNumberModel.getCountryCode()+phoneNumberModel.getTelephoneNum());
        clueEntity.setClueInfo(phoneNumberModel.getRemark());
        QueryWrapper<ClueEntity> qw = new QueryWrapper<>();
        qw.eq("CLUEID",phoneNumberModel.getId());
        clueService.update(clueEntity, qw);


        CallClueEntity callClueEntity = new CallClueEntity();
        callClueEntity.setClueId(phoneNumberModel.getId());
        callClueEntity.setId(phoneNumberModel.getCountryCode()+ phoneNumberModel.getTelephoneNum());
        callClueService.modifyCallNumber(callClueEntity);

    }

    @Override
    public boolean updateBatchByStatus(List<Long> ids , Integer toStatus) {
        return baseMapper.updateBatchByStatus(ids,toStatus) > 0;
    }

    @Override
    public List<ClueDto> selectListByClueIds(List<Long> ids) {
        return baseMapper.selectListByClueIds(ids);
    }

    @Override
    public List<ClueDto> selectAllExport() {
        return baseMapper.selectAllExport();
    }

    @Override
    public List<ClueDto> selectOrdinaryExport() {
        return baseMapper.selectOrdinaryExport();
    }

    @Override
    public List<ClueDto> selectOrdinaryByIdsExport(List<Long> numberIds) {
        return baseMapper.selectOrdinaryByIdsExport(numberIds);
    }

    @Override
    public List<ClueDto> getByIdsExport(List<Long> numberIds,List<Long> specialIds) {
        return baseMapper.getByIdsExport(numberIds,specialIds);
    }


}
