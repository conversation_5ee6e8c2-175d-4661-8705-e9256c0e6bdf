package com.semptian.service.impl;

import com.semptian.dto.common.HomePageInfoDto;
import com.semptian.dto.common.StatisticsDto;
import com.semptian.mapper.CommonMapper;
import com.semptian.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private CommonMapper commonMapper;

    @Override
    public List<StatisticsDto> statistics() {
        return commonMapper.statistics();
    }

    @Override
    public List<HomePageInfoDto> queryInfo() {
        return commonMapper.queryInfo();
    }
}
