package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.CountryCodeEntity;
import com.semptian.mapper.CountryCodeMapper;
import com.semptian.service.CountryCodeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CountryCodeServiceImpl extends ServiceImpl<CountryCodeMapper, CountryCodeEntity> implements CountryCodeService {
    @Override
    public Long getNextVal() {
        return this.baseMapper.getNextVal();
    }

    @Override
    public boolean checkCountryCodeExist(Long id, String countryCode) {
        return this.baseMapper.checkCountryCodeExist(id,countryCode) > 0;
    }

    @Override
    public void batchInsert(List list) {
        this.baseMapper.batchInsert(list);
    }
}
