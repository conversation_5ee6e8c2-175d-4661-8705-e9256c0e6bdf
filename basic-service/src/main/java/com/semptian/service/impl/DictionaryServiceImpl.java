package com.semptian.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.*;
import com.semptian.mapper.DictionaryMapper;
import com.semptian.mapper.NationalConfMapper;
import com.semptian.service.DictionaryService;
import com.semptian.utils.DictionaryUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: SunQi
 * @create: 2021/01/04
 * desc:
 **/
@Service
@DS("mysql")
@Slf4j
public class DictionaryServiceImpl implements DictionaryService {

    @Value("${i18n.default.lang}")
    private String defaultLang;
    @Autowired
    DictionaryMapper dictionaryMapper;

    @Override
    public ReturnModel queryI18nValue(PageQueryModel pageQueryModel) {
        ReturnModel returnModel = checkRequestParam(pageQueryModel);
        if (returnModel != null) {
            return returnModel;
        }
        String code = pageQueryModel.getCode();
        if (StringUtils.isBlank(code)) {
            return new ReturnModel().error().setMsg("code参数不能为空");
        }
        List<String> codes = Arrays.asList(pageQueryModel.getCode().split(","));
        QueryWrapper<DictionaryEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("type", pageQueryModel.getType());
        wrapper.eq("lang", pageQueryModel.getLang());
        wrapper.nested(w -> w.in("code", codes));
        Page<DictionaryEntity> page = new Page<>();
        Integer onPage = pageQueryModel.getOnPage();
        if (onPage == null) {
            onPage = 1;
        }
        Integer size = pageQueryModel.getSize();
        if (size == null) {
            size = 10;
        }
        page.setCurrent(onPage);
        page.setSize(size);

        Page<DictionaryEntity> dictionaryEntities = dictionaryMapper.selectPage(page, wrapper);
        Map<String, Object> map = new HashMap(16);
        map.put("dictionaryList", dictionaryEntities.getRecords());
        map.put("total", page.getTotal());
        map.put("totalPage", page.getPages());
        map.put("currentPage", page.getCurrent());
        map.put("size", page.getSize());
        return new ReturnModel().ok().setData(map);
    }


    @Override
    public ReturnModel queryI18nValueByKeyword(PageQueryModel pageQueryModel) {
        ReturnModel returnModel = checkRequestParam(pageQueryModel);
        if (returnModel != null) {
            return returnModel;
        }
        QueryWrapper<DictionaryEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("type", pageQueryModel.getType());
        wrapper.eq("lang", pageQueryModel.getLang());

        String keyword = pageQueryModel.getKeyword();
        if (StringUtils.isNotBlank(keyword)) {
            wrapper.like("code", keyword);
        }
        Page<DictionaryEntity> page = new Page<>();
        Integer onPage = pageQueryModel.getOnPage();
        if (onPage == null) {
            onPage = 1;
        }
        Integer size = pageQueryModel.getSize();
        if (size == null) {
            size = 10;
        }
        page.setCurrent(onPage);
        page.setSize(size);

        List<DictionaryEntity> dictionaryEntities = dictionaryMapper.selectPage(page, wrapper).getRecords();
        Map<String, Object> map = new HashMap(16);
        map.put("dictionaryList", dictionaryEntities);
        map.put("total", page.getTotal());
        map.put("totalPage", page.getPages());
        map.put("currentPage", page.getCurrent());
        map.put("size", page.getSize());
        return new ReturnModel().ok().setData(map);
    }

    @Override
    public ReturnModel addOrUpdateDictionary(DictionaryEntity dictionaryEntity) {
        ReturnModel returnModel = checkRequestParam(dictionaryEntity);
        if (returnModel != null) {
            return returnModel;
        }
        if (dictionaryEntity.getId() == null) {
            //新增
            dictionaryEntity.setCreateTime(System.currentTimeMillis());
            dictionaryEntity.setModifyTime(System.currentTimeMillis());
            dictionaryMapper.insert(dictionaryEntity);
        } else {
            //修改
            dictionaryEntity.setModifyTime(System.currentTimeMillis());
            dictionaryMapper.updateById(dictionaryEntity);
        }

        return new ReturnModel().ok();
    }

    @Override
    public Map<String, String> getValueByLang(String type, String defaultLang, String lang, List<String> values) {

        if (StringUtils.isBlank(type)) {
            log.info("type参数为空");
            return null;
        }

        if (StringUtils.isBlank(defaultLang)) {
            log.info("defaultLang参数为空");
            return null;
        }

        if (StringUtils.isBlank(lang)) {
            log.info("lang参数为空");
            return null;
        }
        if (values == null || values.size() == 0) {
            log.info("values参数为空");
            return null;
        }

        List<I18nModel> in18nValues = dictionaryMapper.getIn18nValues(type, defaultLang, lang, values);

        if (in18nValues == null || in18nValues.size() == 0) {
            log.info("获取国际化后值的list为空");
            return null;
        }
        Map<String, String> i18nValueMap = new HashMap<>(16);
        a:
        for (String value : values) {
            b:
            for (I18nModel model : in18nValues) {
                if (value.equals(model.getOriValue())) {
                    i18nValueMap.put(value, model.getI18nValue());
                    continue a;
                }
            }
            if (!i18nValueMap.containsKey(value)) {
                i18nValueMap.put(value, value);
            }
        }

        return i18nValueMap;
    }

    @Override
    public Object getI18nData(I18nDataModel model) {

        if (model == null) {
            log.info("国际化失败，参数为空");
            return null;
        }
        String url = model.getUrl();
        String lang = model.getLang();
        Object data = model.getData();
        if (StringUtils.isBlank(url)) {
            log.info("国际化失败，参数url为空");
            return null;
        }
        if (StringUtils.isBlank(lang)) {
            log.info("国际化失败，参数lang为空");
            return null;
        }
        if (data == null) {
            log.info("国际化失败，参数data为空");
            return null;
        }
        //解析url获取type和modelType
        String type = DictionaryUtils.parseUrl(url);

        //查询数据库根据url获取解析格式
        String parseFormat = dictionaryMapper.getParseFormat(url);
        if (StringUtils.isBlank(parseFormat)) {
            log.info("国际化失败，获取JSON的解析格式为空");
            return new ReturnModel().ok().setData(data);
        }
        parseFormat = StringEscapeUtils.unescapeJava(parseFormat);
        //解析JSON字符串获取需要国际化的值,保留原始返回值中的null值
        String jsonData = JSON.toJSONString(data, SerializerFeature.WriteMapNullValue);
        if (StringUtils.isBlank(jsonData)){
            log.info("国际化失败，需要国际化的data为{},jsonData为{}",data,jsonData);
            return null;
        }
        List<String> values = DictionaryUtils.parseJOSN(jsonData, parseFormat, new ArrayList<>());
        log.debug("需要国际化的value值为{}", values);
        if (values == null || values.size() == 0) {
            log.info("国际化失败，需要国际化的values为空");
            return null;
        }

        //获取国际化后的值
        Map<String, String> I18nValeMap = getValueByLang(type, defaultLang, lang, values);
        if (I18nValeMap == null || I18nValeMap.isEmpty()) {
            log.info("国际化失败，获取国际化后的值错误");
            return null;
        }
        log.debug("国际化前和国际化后的key的对应关系为：{}" + I18nValeMap);

        //解析JSON字符串替换为国际化后的值
        Object json = JSON.parse(jsonData);
        Object parse = JSON.parse(parseFormat);
        Object i18nData = DictionaryUtils.replaceI18nValue(json, parse, I18nValeMap);
        if (i18nData == null) {
            log.info("国际化失败,替换国际化值时出错");
            return null;
        }
        log.debug("返回值国际化成功,国际化后的值为：{}", i18nData);
        return i18nData;
    }

    @Override
    public ReturnModel getI18nValueByKeyword(I18nKeywordModel keywordModel) {
        if (keywordModel == null) {
            log.info("参数为空");
            return new ReturnModel().error();
        }
        String url = keywordModel.getUrl();
        if (StringUtils.isBlank(url)) {
            log.info("url参数为空");
            return new ReturnModel().error();
        }
        Map<String, String> keywords = keywordModel.getKeywordValue();
        if (keywords == null) {
            log.info("keyword参数为空");
            return new ReturnModel().error();
        }
        String lang = keywordModel.getLang();
        if (StringUtils.isBlank(lang)) {
            log.info("lang参数为空");
            return new ReturnModel().error();
        }
        //1、根据 url、lang、查询dict获取该值得所关键字的值有语言值的list集合
        String type = DictionaryUtils.parseUrl(url);
        Map<String, List> i18nKeywordValueMap = new HashMap<>();
        for (String key : keywords.keySet()) {
            String keywordvalue = keywords.get(key);
            List<String> i18nKeywordValue = getI18nValueByKeywordValue(type, lang, keywordvalue);
            i18nKeywordValueMap.put(key, i18nKeywordValue);
        }
        //将Map返回给代理层
        return new ReturnModel().ok().setData(i18nKeywordValueMap);
    }

    @Override
    public Object getAppTypeList() {
        return dictionaryMapper.getAppTypeList();
    }

    private ReturnModel checkRequestParam(DictionaryEntity dictionaryEntity) {
        if (dictionaryEntity == null) {
            return new ReturnModel().error().setMsg("参数异常");
        }

        if (StringUtils.isBlank(dictionaryEntity.getType())) {
            return new ReturnModel().error().setMsg("type参数不能为空");
        }
        String lang = dictionaryEntity.getLang();
        if (StringUtils.isBlank(lang)) {
            return new ReturnModel().error().setMsg("lang参数不能为空");
        }
        String code = dictionaryEntity.getCode();
        if (StringUtils.isBlank(code)) {
            return new ReturnModel().error().setMsg("code参数不能为空");
        }
        String value = dictionaryEntity.getValue();
        if (StringUtils.isBlank(value)) {
            return new ReturnModel().error().setMsg("value参数不能为空");
        }

        return null;
    }

    private ReturnModel checkRequestParam(PageQueryModel pageQueryModel) {
        if (pageQueryModel == null) {
            return new ReturnModel().error().setMsg("参数异常");
        }
        String type = pageQueryModel.getType();
        if (StringUtils.isBlank(type)) {
            return new ReturnModel().error().setMsg("type参数不能为空");
        }
        String lang = pageQueryModel.getLang();
        if (StringUtils.isBlank(lang)) {
            return new ReturnModel().error().setMsg("lang参数不能为空");
        }

        return null;
    }

    private List<String> getI18nValueByKeywordValue(String type, String lang, String keywordValue) {
        QueryWrapper<DictionaryEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("type", type);
        wrapper.eq("lang", lang);
        wrapper.like("value", keywordValue);
        List<DictionaryEntity> dictionaryEntities = dictionaryMapper.selectList(wrapper);
        if (dictionaryEntities != null && dictionaryEntities.size() > 0) {
            Set<String> code = dictionaryEntities.stream().map(DictionaryEntity::getCode).collect(Collectors.toSet());
            QueryWrapper<DictionaryEntity> wrapper2 = new QueryWrapper<>();
            wrapper2.eq("lang", defaultLang);
            wrapper2.in("code", code);
            List<DictionaryEntity> dictionaryEntities2 = dictionaryMapper.selectList(wrapper2);
            if (dictionaryEntities2 != null && dictionaryEntities2.size() > 0) {
                List<String> list2 = new ArrayList<>(dictionaryEntities2.stream().map(DictionaryEntity::getValue).collect(Collectors.toSet()));
                return list2;
            }
        }
        return new ArrayList<>();
    }

}
