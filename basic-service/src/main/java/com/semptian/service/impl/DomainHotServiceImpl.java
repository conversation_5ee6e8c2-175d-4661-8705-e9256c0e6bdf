package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.entity.DomainHotEntity;
import com.semptian.mapper.DomainHotMapper;
import com.semptian.mapper.DomainIpRuleMapper;
import com.semptian.service.DomainHotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@Slf4j
@Service
public class DomainHotServiceImpl extends ServiceImpl<DomainHotMapper, DomainHotEntity> implements DomainHotService {

    @Resource
    private DomainHotMapper domainHotMapper;

    @Override
    public Object isHot(String domain, List<Integer> matchRule) {
        try {
            List<Map<String, Object>> res = domainHotMapper.isHot(domain.toLowerCase(), matchRule);
            if (CollUtil.isNotEmpty(res)) {
                return ReturnModel.getInstance().ok().setData(CommonConstant.TRUE);
            } else {
                return ReturnModel.getInstance().ok().setData(CommonConstant.FALSE);
            }
        } catch (Exception e) {
            log.error("sql query error, case:{}", e);
            return ReturnModel.getInstance().ok().setData(CommonConstant.UNKNOWN);
        }
    }
}
