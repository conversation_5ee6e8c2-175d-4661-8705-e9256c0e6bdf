package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.util.JsonUtil;
import com.semptian.entity.DomainIpRuleEntity;
import com.semptian.entity.DomainRuleEntity;
import com.semptian.mapper.DomainIpRuleMapper;
import com.semptian.param.AdsBasicDomainRuleIpModel;
import com.semptian.service.DomainIpRuleService;
import com.semptian.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@Slf4j
@Service
public class DomainIpRuleServiceImpl extends ServiceImpl<DomainIpRuleMapper, DomainIpRuleEntity> implements DomainIpRuleService {

    @Resource
    private DomainIpRuleMapper domainIpRuleMapper;


    @Resource
    private DorisDatService dorisDatService;

    /**
     * 用户添加的数据,指定特定分区日期
     */
    @Value("${custom.data.partition:2024-01-01}")
    private String customDataPartition;

    private final int BATCH_QUERY_SIZE = 1000;

    private static final String ADS_DOMAIN_RULE_IP_TABLE = "ads_basic_domain_rule_ip";



    @Override
    public List<String> isHot(String ip, String abbrIpv6, List<Integer> queryRange, Integer topSize) {
        List<String> res = Lists.newArrayList();
        try {
            res = domainIpRuleMapper.isHot(ip.toLowerCase(), abbrIpv6, queryRange);
        } catch (Exception e) {
            log.error("query isHotDomain error, ex:{}, ip:{}", e.getMessage(), ip);
        }

        return res.size() > topSize ? res.subList(0, topSize) : res;
    }

    @Override
    public List<String> hotFilter(List<String> domainList, Integer topSize) {

        List<String> res = Lists.newArrayList();
        List<String> subDomainList;
        int handleNum = 0;
        int onPage = 1;
        while (handleNum < domainList.size()) {
            try {
                subDomainList = domainList.subList(handleNum, Math.min(handleNum + BATCH_QUERY_SIZE, domainList.size()));
                res.addAll(domainIpRuleMapper.hotFilter(subDomainList));
                if (res.size() >= topSize) {
                    return res.subList(0, topSize);
                }
            } catch (Exception e) {
                log.warn("[PAGE:{}] query isHotDomain error, ex:{}", onPage, e.getMessage());
            }
            handleNum += BATCH_QUERY_SIZE;
            onPage++;
        }

        return res;
    }

    /**
     * 根据对应的域名或者应用数据ID，插入对应的ip信息到表中
     *
     * @param domainRuleList 域名规则list
     */
    @Override
    public void insertIps(List<DomainRuleEntity> domainRuleList) {
        if (CollUtil.isEmpty(domainRuleList)) {
            return;
        }

        List<DomainIpRuleEntity> list = new ArrayList<>();

        for (DomainRuleEntity domainRuleEntity : domainRuleList) {
            String ips = domainRuleEntity.getIp();
            String ruleId = domainRuleEntity.getId();

            if (StringUtils.isBlank(ips)) {
                continue;
            }

            //将IP插入到域名IP表中
            String[] split = ips.split(",");

            if (split.length > 0) {
                //去重
                Set<String> ipSet = new HashSet<>(Arrays.asList(split));
                for (String s : ipSet) {
                    DomainIpRuleEntity domainIpRuleEntity = new DomainIpRuleEntity();
                    domainIpRuleEntity.setRuleId(ruleId);
                    domainIpRuleEntity.setIp(s);
                    domainIpRuleEntity.setUpdateTime(System.currentTimeMillis());
                    domainIpRuleEntity.setPort(443);
                    boolean b = IpUtil.isipv4(s);
                    if (b) {
                        domainIpRuleEntity.setIpType(0);
                    } else {
                        domainIpRuleEntity.setIpType(1);
                    }
                    domainIpRuleEntity.setId(IdUtil.simpleUUID());
                    list.add(domainIpRuleEntity);
                }
            }
        }


        if (CollectionUtil.isNotEmpty(list)) {
            List<AdsBasicDomainRuleIpModel> ruleIpModelList = list.stream().map(item -> new AdsBasicDomainRuleIpModel(item, customDataPartition)).collect(Collectors.toList());
            boolean insert = dorisDatService.insertBatch(ADS_DOMAIN_RULE_IP_TABLE, ruleIpModelList);
            if (!insert) {
                log.error("insert domain ip error, data:{}", JsonUtil.toJsonString(list));
            }
        }
    }
}
