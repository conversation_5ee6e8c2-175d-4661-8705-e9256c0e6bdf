package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiya.whalex.interior.db.search.condition.Rel;
import com.semptian.base.model.WhereParam;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.entity.DomainIpRuleEntity;
import com.semptian.entity.DomainRuleEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.DomainRuleMapper;
import com.semptian.param.AdsBasicDomainRuleModel;
import com.semptian.param.ImportExcelErrorMsgModel;
import com.semptian.service.DomainIpRuleService;
import com.semptian.service.DomainRuleService;
import com.semptian.service.DwsBehaviorDetailService;
import com.semptian.utils.ExcelUtil;
import com.semptian.utils.IPv6ParseUtil;
import com.semptian.utils.IpUtil;
import com.semptian.vo.PageQueryResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.semptian.constant.CommonConstant.AUTO_UPDATE_RULE_SOURCE_TYPE;
import static com.semptian.constant.CommonConstant.USER_RULE_SOURCE_TYPE;

/**
 * <AUTHOR>
 * @date 2021-09-09 19:38
 **/
@Slf4j
@Service
public class DomainRuleServiceImpl extends ServiceImpl<DomainRuleMapper, DomainRuleEntity> implements DomainRuleService {

    private static final String TYPE = "visit web site";
    private static final String[] MODULE_FIELD = new String[]{"name", "level", "ip"};

    private static final String ADS_DOMAIN_RULE_TABLE = "ads_basic_domain_rule";
    private static final String ADS_DOMAIN_RULE_IP_TABLE = "ads_basic_domain_rule_ip";

    @Resource
    private DomainRuleMapper domainRuleMapper;

    @Resource
    private DomainIpRuleService domainIpRuleService;

    @Resource
    DwsBehaviorDetailService dwsBehaviorDetailService;

    @Resource
    private DorisDatService dorisDatService;

    @Value("${domain.custom.max.num:1000000}")
    private Integer customDomainMaxNum;

    /**
     * 用户添加的数据,指定特定分区日期
     */
    @Value("${custom.data.partition:2024-01-01}")
    private String customDataPartition;

    /**
     * 域名展示ip的top数
     */
    @Value("${app.domain.ip.top:3}")
    private Integer domainIpTop;

    @Override
    public Object newQueryDomainList(String keyword, String queryIp, Integer size, Integer onPage, String orderField, Integer orderType, Integer queryRange) {
        Page<DomainRuleEntity> page = new Page<>(onPage, size);
        List<DomainRuleEntity> domainRuleEntities;

        //获取自动更新数据的最新插入日期
        String latestInsertDay = getLatestInsertDay();

        if (queryRange != null && queryRange == AUTO_UPDATE_RULE_SOURCE_TYPE && StrUtil.isEmpty(latestInsertDay)) {
            return ReturnModel.getInstance().ok(page);
        }

        //构建查询SQL
        String[] sqlArr = buildQueryDomainRuleListWebSql(latestInsertDay, keyword.trim(), size, onPage, orderField, orderType, queryRange, queryIp);
        if (sqlArr.length == 0) {
            return ReturnModel.getInstance().ok(page);
        }

        PageQueryResponseVo<DomainRuleEntity> pageQueryResponseVo = dorisDatService.queryForPage(sqlArr[0], sqlArr[1], DomainRuleEntity.class);

        if (pageQueryResponseVo.getTotal() == 0) {
            return ReturnModel.getInstance().ok(page);
        }

        domainRuleEntities = pageQueryResponseVo.getList();

        //根据规则查询Top的IP并返回展示
        getRuleTopIps(domainRuleEntities, latestInsertDay);

        page.setTotal(pageQueryResponseVo.getTotal());
        page.setRecords(domainRuleEntities);
        return ReturnModel.getInstance().ok(page);
    }

    private void getRuleTopIps(List<DomainRuleEntity> domainRuleEntities, String latestInsertDay) {
        Set<String> autoUpdateRuleIds = new HashSet<>();
        Set<String> customRuleIds = new HashSet<>();
        for (DomainRuleEntity entity : domainRuleEntities) {
            if (entity.getSourceType() == USER_RULE_SOURCE_TYPE) {
                customRuleIds.add(entity.getId());
            } else {
                autoUpdateRuleIds.add(entity.getId());
            }

            //前端返回需要显示的域名级别
            entity.setType(entity.getDomainLevel().toString());

            entity.setExtraName("<span style='color:red'>[" + getSourceTypeName(entity.getSourceType()) + "]</span>");
        }

        //自动更新的规则需要根据id查询对应的TOP的ip
        Map<String, String> autoUpdateRuleMap = new HashMap<>();
        if (CollUtil.isNotEmpty(autoUpdateRuleIds)) {
            autoUpdateRuleMap = getRuleTopIps(autoUpdateRuleIds, latestInsertDay, domainIpTop);
        }
        //手动新增的自定义规则需要根据id查询对应全部的ip
        Map<String, String> customMap = new HashMap<>();
        if (CollUtil.isNotEmpty(customRuleIds)) {
            customMap = getRuleTopIps(customRuleIds, customDataPartition, domainIpTop);
        }

        List<String> ids = domainRuleEntities.stream().map(DomainRuleEntity::getId).collect(Collectors.toList());

        //查询域名对应的ip数量
        List<Map <String, Object>> ipCountList = getRuleIpCount(ids, latestInsertDay);
        Map<String, Long> ipCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ipCountList)) {
            ipCountMap = ipCountList.stream().collect(Collectors.toMap(map -> map.get("ruleId").toString(), map -> Long.parseLong(map.get("ipCount").toString())));
        }

        for (DomainRuleEntity entity : domainRuleEntities) {
            if (entity.getSourceType() == USER_RULE_SOURCE_TYPE) {
                entity.setIp(customMap.get(entity.getId()) == null ? "" : customMap.get(entity.getId()));
            } else {
                entity.setIp(autoUpdateRuleMap.get(entity.getId()) == null ? "" : autoUpdateRuleMap.get(entity.getId()));
            }

            entity.setIpCount(ipCountMap.getOrDefault(entity.getId(), 0L));
        }
    }

    private Map<String, String> getRuleTopIps(Set<String> ruleIds, String insertDay, Integer top) {
        Map<String, String> autoUpdateRuleMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(ruleIds)) {
            List<Map<String, Object>> ipList = selectTopDomainIpRuleList(ruleIds, insertDay, top);

            if (CollUtil.isNotEmpty(ipList)) {
                for (Map<String, Object> map : ipList) {
                    autoUpdateRuleMap.put(map.get("ruleId").toString(), map.get("ips").toString());
                }
            }
        }
        return autoUpdateRuleMap;
    }

    private List<Map<String, Object>> selectTopDomainIpRuleList(Set<String> ruleIds, String insertDay, int top) {
        String sql = "SELECT rule_id as ruleId, GROUP_CONCAT(ip, ',') AS ips FROM ( SELECT rule_id, ip, ROW_NUMBER() OVER (PARTITION BY rule_id ORDER BY behavior_num_sum DESC,ip ASC) AS rn " +
                "FROM ads_basic_domain_rule_ip where insert_day = %s and rule_id in ( %s ) ) ranked_ips WHERE rn <= %s GROUP BY rule_id ";

        List<String> list = new ArrayList<>(ruleIds);
        list.replaceAll(s -> "'" + s + "'");

        sql = String.format(sql, "'" + insertDay + "'", String.join(",", list), top);
        return dorisDatService.queryForList(sql);
    }

    public List<String> getDomainIpRuleListByIp(String ip, List<String> insertDayList) {
        insertDayList.replaceAll(s -> "'" + s + "'");
        String sql = "SELECT id,rule_id as ruleId,ip,ip_type as ipType,port,up_dateTime as updateTime from ads_basic_domain_rule_ip\n WHERE insert_day in (" + String.join(",", insertDayList) + ") AND ip like '%" + ip + "%' ";

        List<DomainIpRuleEntity> domainIpRuleEntities = dorisDatService.queryForList(sql, DomainIpRuleEntity.class);
        return domainIpRuleEntities.stream().map(DomainIpRuleEntity::getRuleId).distinct().collect(Collectors.toList());
    }

    private String[] buildQueryDomainRuleListWebSql(String latestInsertDay, String keyword, Integer size, Integer onPage, String orderField, Integer orderType, Integer queryRange, String queryIp) {
        StringBuilder builder = new StringBuilder("SELECT id,name,rule,description,status,`type`,source_type as sourceType,domain_level as domainLevel,user_id as userId,create_time as createTime,update_time as updateTime FROM ads_basic_domain_rule ");
        builder.append(" WHERE %s ");

        //根据IP查询出所有的规则ID
        List<String> domainIpRuleList = new ArrayList<>();
        if (StrUtil.isNotEmpty(queryIp)) {
            //如果查询IP是ipv6则将ipv6转简写后查询
            if (IpUtil.isipv6(queryIp)) {
                queryIp = IPv6ParseUtil.getShortIPv6(queryIp).toLowerCase();
            }

            List<String> insertDayList = Lists.newArrayList(customDataPartition, latestInsertDay);
            if (queryRange != null && queryRange == USER_RULE_SOURCE_TYPE) {
                insertDayList.remove(latestInsertDay);
            } else if (queryRange != null &&  queryRange == AUTO_UPDATE_RULE_SOURCE_TYPE) {
                insertDayList.remove(customDataPartition);
            }

            //根据IP查询对应规则ID，如果查询不到说明没有,返回结果
            domainIpRuleList = getDomainIpRuleListByIp(queryIp, insertDayList);
            if (CollUtil.isEmpty(domainIpRuleList)) {
                return new String[]{};
            }
        }

        String sql = getSqlString(latestInsertDay, queryRange, builder, domainIpRuleList);

        if (StrUtil.isNotBlank(keyword)) {
            //name忽略大小写进行模糊匹配,rule和description不忽略进行模糊匹配
            sql = sql + " AND (lower(name) like '%" + keyword.toLowerCase() + "%' or rule like '%" + keyword + "%' or description like '%" + keyword + "%') ";
        }

        String pageSql = " order by sourceType desc, " + orderField + " " + (orderType == 0 ? "DESC" : "ASC") + " LIMIT " + size + " OFFSET " + (onPage - 1) * size;

        log.info("buildQueryDomainRuleListWebSql , sql:{}, pageSql:{}", sql, sql + pageSql);
        return new String[]{sql, sql + pageSql};
    }

    private String getSqlString(String latestInsertDay, Integer queryRange, StringBuilder builder, List<String> domainIpRuleList) {
        String sql;

        if (queryRange == null) {
            //默认查询所有即自动更新+自定义
            sql = String.format(builder.toString(), " insert_day in ('" + latestInsertDay + "', '" + customDataPartition + "') ");
        } else if (queryRange == USER_RULE_SOURCE_TYPE) {
            //查询用户自定义
            sql = String.format(builder.toString(), " insert_day = '" + customDataPartition + "'");
        } else {
            //查询自动更新
            sql = String.format(builder.toString(), " insert_day = '" + latestInsertDay + "'");
        }

        if (CollUtil.isNotEmpty(domainIpRuleList)) {
            domainIpRuleList.replaceAll(s -> "'" + s + "'");
            sql = sql + " AND id in (" + String.join(",", domainIpRuleList) + ") ";
        }
        return sql;
    }


    /**
     * 获取来源名称
     */
    private static String getSourceTypeName(Integer sourceType) {
        switch (sourceType) {
            case AUTO_UPDATE_RULE_SOURCE_TYPE:
                return I18nUtils.getMessage("autoUpdate");
            case USER_RULE_SOURCE_TYPE:
                return I18nUtils.getMessage("customize");
            default:
                return "";
        }
    }

    @Override
    public ReturnModel<?> addDomainRule(String name, String ip, Integer domainType, String userId) {
        if (checkRepeatName(name, null)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("nameExists"));
        }

        if (checkTotalDomainCount(1L)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_BATCH_INSERT_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("domain.custom.max.num") + " " + customDomainMaxNum);
        }

        DomainRuleEntity domainRuleEntity = buildDomainIpRuleEntity(name, userId, domainType);

        AdsBasicDomainRuleModel adsBasicDomainRuleModel = new AdsBasicDomainRuleModel(domainRuleEntity, customDataPartition);
        boolean insert = dorisDatService.insertBatch(ADS_DOMAIN_RULE_TABLE, Lists.newArrayList(adsBasicDomainRuleModel));
        if (!insert) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }

        domainRuleEntity.setIp(ip);
        domainIpRuleService.insertIps(Lists.newArrayList(domainRuleEntity));
        return ReturnModel.getInstance().ok();
    }

    private boolean checkTotalDomainCount(Long addNum) {
        String checkTotalCountSql = "SELECT count(*) as cnt from ads_basic_domain_rule where insert_day = %s ";
        checkTotalCountSql = String.format(checkTotalCountSql, "'" + customDataPartition + "'");
        Long count = dorisDatService.queryForCount(checkTotalCountSql);

        return (count + addNum) > customDomainMaxNum;
    }

    @Override
    public Object updateDomainRule(String id, String name, String ip, Integer domainType, String userId) {
        if (checkRepeatName(name, id)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("nameExists"));
        }

        // Doris DUPLICATE KEY模型不支持修改，因此先删除再插入
        delDomainRule(Lists.newArrayList(id));

        DomainRuleEntity domainRuleEntity = buildDomainIpRuleEntity(name, userId, domainType);
        domainRuleEntity.setId(id);

        AdsBasicDomainRuleModel adsBasicDomainRuleModel = new AdsBasicDomainRuleModel(domainRuleEntity, customDataPartition);
        boolean insert = dorisDatService.insertBatch(ADS_DOMAIN_RULE_TABLE, Lists.newArrayList(adsBasicDomainRuleModel));
        if (!insert) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }

        //判断域名和IP的对应关系需不需要变化
        //查询出域名关联的所有IP
        Page<DomainIpRuleEntity> ipRuleEntityPage = queryIpList(id, 1000, 1);

        List<String> newIps = new ArrayList<>(Arrays.asList(ip.split(",")));
        List<String> needDeleteIpsList = new ArrayList<>();

        for (DomainIpRuleEntity domainIpRuleEntity : ipRuleEntityPage.getRecords()) {
            if (!newIps.contains(domainIpRuleEntity.getIp())) {
                needDeleteIpsList.add(domainIpRuleEntity.getId());
            } else {
                newIps.remove(domainIpRuleEntity.getIp());
            }
        }

        //删除已经不存在的ip信息
        if (CollUtil.isNotEmpty(needDeleteIpsList)) {
            boolean b = delDomainRuleIpList("id", needDeleteIpsList);
            if (!b) {
                log.error("delete domain rule ip error, ids:{}", needDeleteIpsList);
            }
        }

        //插入新的ip信息
        if (CollUtil.isNotEmpty(newIps)) {
            DomainRuleEntity domainRule = new DomainRuleEntity();
            domainRule.setId(id);
            domainRule.setIp(String.join(",", newIps));
            domainIpRuleService.insertIps(Lists.newArrayList(domainRule));
        }
        return ReturnModel.getInstance().ok();
    }

    @Override
    public Object importDomainRules(MultipartFile file, String lang, String userId, HttpServletResponse response) {
        List<Map<String, Object>> list = ExcelUtil.readExcel(file, MODULE_FIELD);
        if (CollectionUtil.isEmpty(list)) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("import.white.isEmpty"));
        }

        if (list.size() > 100) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("importOutOfSize"));
        }

        if (checkTotalDomainCount((long) list.size())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_BATCH_INSERT_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("domain.custom.max.num") + " " + customDomainMaxNum);
        }

        List<ImportExcelErrorMsgModel> importExcelErrorMsgModelList = validatorImportDataList(list);

        if (CollUtil.isNotEmpty(importExcelErrorMsgModelList)) {
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getOriginalFilename());
            try (OutputStream fileOutputStream = response.getOutputStream(); InputStream inputStream = file.getInputStream()) {
                XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                XSSFSheet sheet = workbook.getSheetAt(0);

                //创建绘图对象
                XSSFDrawing drawing = sheet.createDrawingPatriarch();

                for (ImportExcelErrorMsgModel importExcelErrorMsgModel : importExcelErrorMsgModelList) {
                    XSSFRow row = sheet.getRow(importExcelErrorMsgModel.getRow());
                    XSSFCell cell = row.getCell(importExcelErrorMsgModel.getColumn());
                    if (ObjectUtil.isNull(cell)) {
                        cell = row.createCell(importExcelErrorMsgModel.getColumn());
                    }

                    XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                    cellComment.setAuthor("CBP");
                    cellComment.setString(new XSSFRichTextString(importExcelErrorMsgModel.getErrorMsg()));
                    cell.setCellComment(cellComment);
                }

                workbook.write(fileOutputStream);
            } catch (Exception e) {
                log.error("response error ", e);
            }

            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }

        List<DomainRuleEntity> domainRuleEntityList = new ArrayList<>();

        for (Map<String, Object> m : list) {
            String name = String.valueOf(m.get("name"));
            Integer level = Integer.valueOf(String.valueOf(m.get("level")));
            String ip = String.valueOf(m.get("ip"));

            DomainRuleEntity domainRuleEntity = buildDomainIpRuleEntity(name, userId, level);
            domainRuleEntity.setIp(ip);

            domainRuleEntityList.add(domainRuleEntity);
        }

        //批量写入域名规则数据
        if (CollUtil.isNotEmpty(domainRuleEntityList)) {
            List<AdsBasicDomainRuleModel> domainRuleModelList = domainRuleEntityList.stream().map(domainRuleEntity -> new AdsBasicDomainRuleModel(domainRuleEntity, customDataPartition)).collect(Collectors.toList());

            boolean b = dorisDatService.insertBatch(ADS_DOMAIN_RULE_TABLE, domainRuleModelList);
            if (!b) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
            }

            //写入域名ip规则数据
            domainIpRuleService.insertIps(domainRuleEntityList);
        }

        return ReturnModel.getInstance().ok();
    }

    @Override
    public Object deleteDomainRule(String ids) {
        //查询所有的预置数据id
        String[] split = ids.split(",");
        if (split.length == 0) {
            return ReturnModel.getInstance().ok();
        }

        List<String> idList = Arrays.stream(split).distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        if (!idList.isEmpty()) {
            //删除域名表数据

            //判断域名规则是自定义还是自动更新,自动更新则不允许删除
            List<DomainRuleEntity> domainRuleList = getDomainRuleListByIds(idList, getLatestInsertDay());
            if (CollUtil.isNotEmpty(domainRuleList)) {
                for (DomainRuleEntity domainRuleEntity : domainRuleList) {
                    if (domainRuleEntity.getSourceType() == CommonConstant.AUTO_UPDATE_RULE_SOURCE_TYPE) {
                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.PRESET_DATA_CANNOT_DELETE.getCode()).setMsg(I18nUtils.getMessage("preset.data.cannot.delete"));
                    }
                }
           }

            boolean deleteDomainRule = delDomainRule(idList);
            if (!deleteDomainRule) {
                log.error("delete domain rule error, id:{}", idList);
            }

            //删除域名IP表中的数据
            boolean deleteDomainRuleIp = delDomainRuleIpList("rule_id", idList);
            if (!deleteDomainRuleIp) {
                log.error("delete domain rule ip error, rule ids:{}", idList);
            }

            if (!deleteDomainRule || !deleteDomainRuleIp) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
            }
        }

        return ReturnModel.getInstance().ok();
    }

    @Override
    public Object changeStatus(String id, Integer status) {
        DomainRuleEntity domainRuleEntity = new DomainRuleEntity();
        domainRuleEntity.setId(id);
        domainRuleEntity.setStatus(status);
        return domainRuleMapper.updateById(domainRuleEntity);
    }

    @Override
    public Page<DomainIpRuleEntity> queryIpList(String id, Integer size, Integer onPage) {
        Page<DomainIpRuleEntity> page = new Page<>(onPage, size);

        //获取自动更新数据的最新插入日期
        //根据ID查询域名规则类型
        String latestInsertDay = getLatestInsertDay();

        List<DomainRuleEntity> domainRuleEntities = getDomainRuleListByIds(Lists.newArrayList(id), latestInsertDay);
        if (CollUtil.isEmpty(domainRuleEntities)) {
            return page;
        }

        Integer sourceType = domainRuleEntities.get(0).getSourceType();
        if (sourceType == 2) {
            latestInsertDay = customDataPartition;
        }

        List<DomainIpRuleEntity> resList = queryDomainIpRulePage(page, id, latestInsertDay);
        page.setRecords(resList);
        return page;
    }

    @Override
    public Object queryIpByNameList(List<String> nameList) {
        return dwsBehaviorDetailService.queryIpByNameList(nameList);
    }

    public String getLatestInsertDay() {
        String sql = "SELECT insert_day from ads_basic_domain_rule order by insert_day desc limit 1";
        List<Map<String, Object>> maps = dorisDatService.queryForList(sql);
        if (CollUtil.isNotEmpty(maps)) {
            Map<String, Object> objectMap = maps.get(0);
            return null != objectMap.get("insert_day") ? objectMap.get("insert_day").toString() : null;
        }
        return null;
    }

    public DomainRuleEntity buildDomainIpRuleEntity(String name, String userId, Integer domainLevel) {
        DomainRuleEntity domainRuleEntity = new DomainRuleEntity();
        String id = IdUtil.simpleUUID();
        domainRuleEntity.setId(id);
        domainRuleEntity.setName(name);
        domainRuleEntity.setRule(name);
        domainRuleEntity.setDescription("customize");
        domainRuleEntity.setStatus(1);
        domainRuleEntity.setType(TYPE);
        domainRuleEntity.setSourceType(2);
        domainRuleEntity.setUserId(Long.parseLong(userId));
        domainRuleEntity.setCreateTime(System.currentTimeMillis());
        domainRuleEntity.setUpdateTime(System.currentTimeMillis());
        domainRuleEntity.setDomainLevel(domainLevel);

        return domainRuleEntity;
    }

    public List<DomainIpRuleEntity> queryDomainIpRulePage(Page<DomainIpRuleEntity> page, String ruleId, String insertDay) {
        String sql = "SELECT id,rule_id as ruleId,ip,ip_type as ipType, port from ads_basic_domain_rule_ip where insert_day = %s and rule_id = %s ";
        sql = String.format(sql, "'" + insertDay + "'", "'" + ruleId + "'");

        String pageSql = " ORDER BY behavior_num_sum DESC, ip ASC " + " LIMIT " + page.getSize() + " OFFSET " + (page.getCurrent() - 1) * page.getSize();

        log.info("queryDomainIpRulePage , sql:{}, pageSql:{}", sql, sql + pageSql);

        PageQueryResponseVo<DomainIpRuleEntity> responseVo = dorisDatService.queryForPage(sql, sql + pageSql, DomainIpRuleEntity.class);

        page.setTotal(responseVo.getTotal());
        return responseVo.getList();
    }

    /**
     * 判断name是否重复
     * @param name 域名
     * @return boolean
     */
    public boolean checkRepeatName(String name, String id) {
        //判断name是否重复
        String checkRepeatSql = "SELECT id from ads_basic_domain_rule where insert_day = %s and name = %s ";
        checkRepeatSql = String.format(checkRepeatSql, "'" + customDataPartition + "'", "'" + name + "'");
        List<DomainRuleEntity> existsList = dorisDatService.queryForList(checkRepeatSql, DomainRuleEntity.class);

        if (StrUtil.isNotEmpty(id)) {
            existsList = existsList.stream().filter(domainRuleEntity -> !id.equals(domainRuleEntity.getId())).collect(Collectors.toList());
        }

        return !CollUtil.isEmpty(existsList);
    }

    /**
     * 删除域名规则
     * @param idList 域名规则ID集合
     * @return 删除结果
     */
    private boolean delDomainRule(List<String> idList) {
        WhereParam insertDay = WhereParam.builder().field("insert_day").type(Rel.EQ).param(Lists.newArrayList(customDataPartition)).build();
        WhereParam id = WhereParam.builder().field("id").type(Rel.IN).param(Lists.newArrayList(idList)).build();

        List<WhereParam> paramList = new ArrayList<>();
        paramList.add(insertDay);
        paramList.add(id);

        return dorisDatService.deleteByCondition(ADS_DOMAIN_RULE_TABLE, paramList);
    }

    /**
     * 删除域名关联ip信息
     * @param idList 域名ID集合或者域名ipID集合
     */
    public boolean delDomainRuleIpList(String idField, List<String> idList) {
        WhereParam ruleIpId = WhereParam.builder().field(idField).type(Rel.IN).param(Lists.newArrayList(idList)).build();
        WhereParam insertDay = WhereParam.builder().field("insert_day").type(Rel.EQ).param(Lists.newArrayList(customDataPartition)).build();
        List<WhereParam> list = new ArrayList<>();
        list.add(insertDay);
        list.add(ruleIpId);

        return dorisDatService.deleteByCondition(ADS_DOMAIN_RULE_IP_TABLE, list);
    }

    private List<ImportExcelErrorMsgModel> validatorImportDataList(List<Map<String, Object>> list) {
        List<ImportExcelErrorMsgModel> errorMsgList = new ArrayList<>();

        //查询所有的自定义域名规则
        String sql = "SELECT id,name,rule,description,status,`type`,source_type as sourceType FROM ads_basic_domain_rule t1 where insert_day = '" + customDataPartition + "'";
        List<DomainRuleEntity> domainRuleEntityList = dorisDatService.queryForList(sql, DomainRuleEntity.class);
        List<String> nameList = domainRuleEntityList.stream().map(DomainRuleEntity::getName).collect(Collectors.toList());

        //添加user_id,type
        for (int i = 0; i <= list.size() - 1; i++) {
            Map<String, Object> m = list.get(i);

            int row = i + 1;

            //判断name是否是空
            if (m.get("name") == null || StringUtils.isBlank(String.valueOf(m.get("name")))) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(0).errorMsg(I18nUtils.getMessage("domain.cannot.null")).build());
            } else if (m.get("name") != null && m.get("name").toString().length() > 255) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(0).errorMsg(I18nUtils.getMessage("domain.max.length")).build());
            } else if (nameList.contains(m.get("name").toString())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(0).errorMsg(I18nUtils.getMessage("nameExists")).build());
            } else {
                nameList.add(m.get("name").toString());
            }

            //判断type是否是空
            if (m.get("level") == null || StringUtils.isBlank(String.valueOf(m.get("level")))) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(1).errorMsg(I18nUtils.getMessage("domainType.cannot.null")).build());
            } else if (m.get("level") != null) {
                //判断type是否是2或者3
                String level = m.get("level").toString();
                if (!("2".equals(level) || "3".equals(level))) {
                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(1).errorMsg(I18nUtils.getMessage("domainType.illegal")).build());
                }
            }

            String ip = null == m.get("ip") ? "" : String.valueOf(m.get("ip"));
            if (StringUtils.isNotBlank(ip)) {
                //判断导入ip的长度是否大于2000个字符
                if (ip.length() > 2000) {
                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(2).errorMsg(I18nUtils.getMessage("ip.max.length")).build());
                } else {
                    String[] split = ip.split(",");
                    boolean b = IpUtil.checkIP(split);
                    if (!b) {
                        errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(2).errorMsg(I18nUtils.getMessage("IP_CHECK")).build());
                    }
                }
            } else {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(2).errorMsg(I18nUtils.getMessage("ip.cannot.null")).build());
            }
        }

        return errorMsgList;
    }

    private List<DomainRuleEntity> getDomainRuleListByIds(List<String> ids, String latestInsertDay) {
        String sql = "SELECT id,name,rule,description,status,`type`,source_type as sourceType,domain_level as domainLevel,user_id as userId,create_time as createTime,update_time as updateTime FROM ads_basic_domain_rule ";

        List<String> idsParamList = new ArrayList<>(ids);
        idsParamList.replaceAll(s -> "'" + s + "'");

        sql = sql + " WHERE insert_day in ('" + latestInsertDay + "', '" + customDataPartition + "') AND id in ( " + String.join(",", idsParamList) + " ) ";
        return dorisDatService.queryForList(sql, DomainRuleEntity.class);
    }

    List<Map<String, Object>> getRuleIpCount(List<String> ids, String latestInsertDay) {
        String sql = "SELECT rule_id as ruleId,count( DISTINCT ip) as ipCount from ads_basic_domain_rule_ip ";

        List<String> idsParamList = new ArrayList<>(ids);
        idsParamList.replaceAll(s -> "'" + s + "'");

        sql = sql + " WHERE insert_day in ('" + latestInsertDay + "', '" + customDataPartition + "') AND rule_id in ( " + String.join(",", idsParamList) + " ) GROUP BY rule_id ";

        return dorisDatService.queryForList(sql);
    }
}
