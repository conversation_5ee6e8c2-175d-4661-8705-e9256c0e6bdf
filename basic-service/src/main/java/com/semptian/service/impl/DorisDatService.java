package com.semptian.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.semptian.base.model.WhereParam;
import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.vo.PageQueryResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * doris dat 服务实现
 *
 * <AUTHOR> Hu
 * @since 2024/10/22
 */

@Service
public class DorisDatService {

    @Autowired
    private TianHeDorisTemplate dorisTemplate;

    public <T> PageQueryResponseVo<T> queryForPage(String sql, String pageSql, Class<T> clazz) {
        PageQueryResponseVo<T> pageResultModel = new PageQueryResponseVo<>();
        String countSql = getCountSql(sql);

        Long total = dorisTemplate.queryForCount(countSql);

        if (ObjectUtil.isNull(total) || total == 0) {
            pageResultModel.setTotal(0L);
            pageResultModel.setList(Lists.newArrayList());
            return pageResultModel;
        }

        List<Map<String, Object>> list = dorisTemplate.queryForList(pageSql);

        List<T> result = list.stream().map(record -> BeanUtil.toBean(record, clazz)).collect(Collectors.toList());

        pageResultModel.setTotal(total);
        pageResultModel.setList(result);
        return pageResultModel;
    }

    public List<Map<String, Object>> queryForList(String sql) {
        return dorisTemplate.queryForList(sql);
    }

    public Long queryForCount(String sql) {
        return dorisTemplate.queryForCount(sql);
    }

    public <T> List<T> queryForList(String sql, Class<T> clazz) {
        List<Map<String, Object>> list = dorisTemplate.queryForList(sql);

        return list.stream().map(record -> BeanUtil.toBean(record, clazz)).collect(Collectors.toList());
    }

    public <T> boolean insertBatch(String tableName, List<T> recordBatch) {
        return dorisTemplate.insertBatch(tableName, recordBatch);
    }

    public boolean deleteByCondition(String tableName, List<WhereParam> paramList) {
        return dorisTemplate.deleteByCondition(tableName, paramList);
    }

    public String getCountSql(String originalSql) {
        return " select count(1) cnt from (" + originalSql + ") as num";
    }
}
