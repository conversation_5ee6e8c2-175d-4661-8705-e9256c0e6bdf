package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.semptian.bo.DomainBo;
import com.semptian.bo.IpRuleBo;
import com.semptian.bo.RuleBo;
import com.semptian.entity.DomainIpRuleEntity;
import com.semptian.mapper.DwsBehaviorDetailMapper;
import com.semptian.service.DwsBehaviorDetailService;
import com.semptian.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Clickhouse DwsBehaviorDetail Service实现类
 *
 * <AUTHOR>
 * @date 2022-05-25 16:27
 **/
@Slf4j
@Service
@DS("click_house")
public class DwsBehaviorDetailServiceImpl implements DwsBehaviorDetailService {

    @Value("${rule.data.autoUpdate.days}")
    private Integer days;

    @Value("${rule.data.onlyHttps}")
    private Boolean onlyHttps;

    @Value("${rule.data.hot.appNum}")
    private Integer hotAppNum;

    @Value("${rule.data.hot.tertiaryDomainNum}")
    private Integer hotTertiaryDomainNum;

    @Value("${rule.data.hot.secondaryDomainNum}")
    private Integer hotSecondaryDomainNum;

    @Value("${rule.data.hot.appIpNum}")
    private Integer hotAppIpNum;

    @Value("${rule.data.hot.domainIpNum}")
    private Integer hotDomainIpNum;

    @Value("${rule.data.domain.tertiary.extract}")
    private String tertiaryDomainExtractRule;

    @Value("${rule.data.domain.secondary.extract}")
    private String secondaryDomainExtractRule;

    @Value("${rule.data.domain.extract.source}")
    private String domainExtractSource;

    @Value("${rule.data.domain.source}")
    private String ruleDataDomainSource;

    @Value("${rule.data.app.source}")
    private String ruleDataAppSource;

    @Resource
    private DwsBehaviorDetailMapper dwsBehaviorDetailMapper;

    @Override
    public Integer queryAppNum() {
        try {
            return dwsBehaviorDetailMapper.queryAppNum(days, onlyHttps, getDataTypeRange(ruleDataAppSource));
        } catch (Exception e) {
            log.warn("queryAppNum error, ex:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<RuleBo> queryApp(Integer startNum, Integer pageNum) {
        try {
            return dwsBehaviorDetailMapper.queryApp(days, onlyHttps, getDataTypeRange(ruleDataAppSource), startNum, pageNum);
        } catch (Exception e) {
            log.warn("queryApp error, ex:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<String> queryHotTertiaryDomain() {
        try {
            return dwsBehaviorDetailMapper.queryHotTertiaryDomain(days, onlyHttps, tertiaryDomainExtractRule, secondaryDomainExtractRule, hotTertiaryDomainNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource));
        } catch (Exception e) {
            log.warn("queryHotTertiaryDomain error, ex:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<String> queryHotSecondaryDomain() {
        try {
            return dwsBehaviorDetailMapper.queryHotSecondaryDomain(days, onlyHttps, secondaryDomainExtractRule, hotSecondaryDomainNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource));
        } catch (Exception e) {
            log.warn("queryHotSecondaryDomain error, ex:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<IpRuleBo> queryHotIpForApp(List<String> appNameList) {
        // 参数校验
        if (CollUtil.isEmpty(appNameList)) {
            return Lists.newArrayList();
        }
        try {
            List<IpRuleBo> ipRuleBos = dwsBehaviorDetailMapper.queryHotIpForApp(days, appNameList, onlyHttps, hotAppIpNum, getDataTypeRange(ruleDataAppSource));
            return ipRuleBos.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("queryHotIpForApp error, ex:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<IpRuleBo> queryHotIpForTertiaryDomain(List<String> domainList) {
        // 参数校验
        if (CollUtil.isEmpty(domainList)) {
            return Lists.newArrayList();
        }
        try {
            return dwsBehaviorDetailMapper.queryHotIpForDomain(days, domainList, onlyHttps, tertiaryDomainExtractRule, hotDomainIpNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource));
        } catch (Exception e) {
            log.warn("queryHotIpForDomain error, ex:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<IpRuleBo> queryHotIpForSecondaryDomain(List<String> domainList) {
        // 参数校验
        if (CollUtil.isEmpty(domainList)) {
            return Lists.newArrayList();
        }
        try {
            return dwsBehaviorDetailMapper.queryHotIpForDomain(days, domainList, onlyHttps, secondaryDomainExtractRule, hotDomainIpNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource));
        } catch (Exception e) {
            log.warn("queryHotIpForSecondaryDomain error, ex:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public void insertHotTertiaryDomain(int domainLevel) {
        try {
             dwsBehaviorDetailMapper.insertHotTertiaryDomain(days, onlyHttps, tertiaryDomainExtractRule, secondaryDomainExtractRule, hotTertiaryDomainNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource), domainLevel);
        } catch (Exception e) {
            log.warn("insertHotTertiaryDomain error, ex:{}", e.getMessage(), e);
        }
    }

    @Override
    public void insertHotSecondaryDomain(int domainLevel) {
        try {
             dwsBehaviorDetailMapper.insertHotSecondaryDomain(days, onlyHttps, secondaryDomainExtractRule, hotSecondaryDomainNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource), domainLevel);
        } catch (Exception e) {
            log.warn("insertHotSecondaryDomain error, ex:{}", e.getMessage(), e);
        }
    }

    @Override
    public void insertHotIpForTertiaryDomain(int domainLevel) {
        try {
            dwsBehaviorDetailMapper.insertHotIpForDomain(days, domainLevel, onlyHttps, tertiaryDomainExtractRule, hotDomainIpNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource),System.currentTimeMillis());
        } catch (Exception e) {
            log.warn("insertHotIpForDomain error, domainLevel:{}", domainLevel, e);
        }
    }

    @Override
    public void insertHotIpForSecondaryDomain(int domainLevel) {
        try {
            dwsBehaviorDetailMapper.insertHotIpForDomain(days, domainLevel, onlyHttps, secondaryDomainExtractRule, hotDomainIpNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource),System.currentTimeMillis());
        } catch (Exception e) {
            log.warn("insertHotIpForDomain error, domainLevel:{}", domainLevel, e);
        }
    }

    @Override
    public long selectSaveDomainCount(int domainLevel) {
        try {
            return dwsBehaviorDetailMapper.selectSaveDomainCount(domainLevel);
        } catch (Exception e) {
            log.warn("selectSaveDomainCount error", e);
        }
        return 0;
    }

    @Override
    public List<DomainBo> selectSaveDomainList(int domainLevel, int onPage, int size) {
        try {
            return dwsBehaviorDetailMapper.selectSaveDomainList(domainLevel, onPage, size);
        } catch (Exception e) {
            log.warn("selectSaveDomainList error", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public void insertHotIpForSecondaryDomainByDomainList(List<String> presetDomainList) {
        // 参数校验
        if (CollUtil.isEmpty(presetDomainList)) {
            return ;
        }
        try {
             dwsBehaviorDetailMapper.insertHotIpForDomainByDomainList(days, presetDomainList, onlyHttps, secondaryDomainExtractRule, hotDomainIpNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource),System.currentTimeMillis());
        } catch (Exception e) {
            log.warn("insertHotIpForDomain error, ex:{}", e.getMessage());
        }
    }

    @Override
    public void deleteCkDomainDate() {
        try {
            List<String> ports=dwsBehaviorDetailMapper.selectAllPartition("dws","dws_base_domain_rule");
            ports = ports.stream().distinct().collect(Collectors.toList());
            for (String port : ports) {
                if (StringUtils.isEmpty(port)){
                    continue;
                }
                Integer deletePort=Integer.parseInt(port);
                dwsBehaviorDetailMapper.deleteCkPartitionByCaptureDay(deletePort,"dws.dws_base_domain_rule");
            }
        } catch (Exception e) {
            log.warn("deleteCkDomainDate error, ex:{}", e.getMessage(),e);
        }
    }


    @Override
    public void deleteCkDomainIpData() {
        try {
            List<String> ports=dwsBehaviorDetailMapper.selectAllPartition("dws","dws_base_domain_rule_ip");
            List<Integer> deletePorts = ports.stream().filter(StringUtils::isNotEmpty).map(Integer::parseInt).filter(s -> !s.equals(DateUtil.formatYYYYMMDDToString(System.currentTimeMillis()))).distinct().collect(Collectors.toList());
            for (Integer deletePort : deletePorts) {
                dwsBehaviorDetailMapper.deleteCkPartitionByCaptureDay(deletePort,"dws.dws_base_domain_rule_ip");
            }
        } catch (Exception e) {
            log.warn("deleteCkDomainIpData error, ex:{}", e.getMessage(),e);
        }
    }

    @Override
    public long selectTodayDomainIpCount() {
        try {
          return dwsBehaviorDetailMapper.selectTodayDomainIpCount(DateUtil.formatYYYYMMDDToString(System.currentTimeMillis()));
        } catch (Exception e) {
            log.warn("deleteCkDomainIpData error, ex:{}", e.getMessage(),e);
        }
        return 0;
    }

    @Override
    public List<String> selectDoMainListByKeyword(String keyword,String abbrIPv6,Integer onPage,Integer pageSize) {
        return dwsBehaviorDetailMapper.selectDoMainListByKeyword(keyword,abbrIPv6,onPage,pageSize);
    }

    @Override
    public Integer selectDoMainListByKeywordCount(String keyword,String abbrIPv6) {
        return dwsBehaviorDetailMapper.selectDoMainListByKeywordCount(keyword,abbrIPv6);
    }

    @Override
    public List<String> selectDomainListByIp(String ip, String abbrIPv6) {
        return dwsBehaviorDetailMapper.selectDomainListByIp(ip, abbrIPv6);
    }

    @Override
    public List<String> selectDomainsByDomainList(List<String> presetDomainList) {
        if (CollectionUtils.isEmpty(presetDomainList)){
            return Lists.newArrayList();
        }
        try {
            return dwsBehaviorDetailMapper.selectDomainsByDomainList(presetDomainList);
        } catch (Exception e) {
            log.warn("selectDomainsByDomainList error, ex:{}", e.getMessage(),e);
        }
        return Lists.newArrayList();
    }

    @Override
    public void insertHistoryDomainIp(List<String> newList) {
        if (CollectionUtils.isEmpty(newList)){
            return;
        }
        try {
            dwsBehaviorDetailMapper.insertHistoryDomainIp(newList,System.currentTimeMillis());
        } catch (Exception e) {
            log.warn("insertHistoryDomainIp error, ex:{}", e.getMessage(),e);
        }
    }

    @Override
    public long selectDomainCount() {
        try {
            return dwsBehaviorDetailMapper.selectDomainCount();
        } catch (Exception e) {
            log.warn("selectDomainCount error, ex:{}", e.getMessage(),e);
        }
        return 0L;
    }


    @Override
    public List<Map<String, String>> selectTop(Set<String> names, int top) {
        return dwsBehaviorDetailMapper.selectTop(names,top);
    }

    @Override
    public List<DomainIpRuleEntity> queryIpList(String name) {
        return dwsBehaviorDetailMapper.queryIpList(name,hotDomainIpNum);
    }

    @Override
    public void insertHotIpForTertiaryDomainByDomainList(List<String> presetDomainList) {
        // 参数校验
        if (CollUtil.isEmpty(presetDomainList)) {
            return ;
        }
        try {
            dwsBehaviorDetailMapper.insertHotIpForDomainByDomainList(days, presetDomainList, onlyHttps, tertiaryDomainExtractRule, hotDomainIpNum, domainExtractSource, getDataTypeRange(ruleDataDomainSource),System.currentTimeMillis());
        } catch (Exception e) {
            log.warn("insertHotIpForDomain error, ex:{}", e.getMessage());
        }
    }

    /**
     * 获取数据协议类型范围
     */
    private List<Integer> getDataTypeRange(String ruleDataSource) {
        return Arrays.stream(ruleDataSource.split(",")).map(Integer::valueOf).collect(Collectors.toList());
    }


    @Override
    public List<DomainIpRuleEntity> queryIpByNameList(List<String> nameList) {
        return dwsBehaviorDetailMapper.queryIpByNameList(nameList);
    }


}
