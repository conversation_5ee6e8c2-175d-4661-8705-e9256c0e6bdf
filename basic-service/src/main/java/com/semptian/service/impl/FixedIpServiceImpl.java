package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.constants.ArchiveIndexConstants;
import com.semptian.base.enums.ReturnCode;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.service.elasticsearch.DataOperation;
import com.semptian.common.RedisHelper;
import com.semptian.entity.FixedIpEntity;
import com.semptian.enums.*;
import com.semptian.feign.archive.ArchiveFeignClient;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.FixedIpMapper;
import com.semptian.param.FixIpArchiveModel;
import com.semptian.param.RelationUserIpModel;
import com.semptian.service.FixedIpService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.semptian.constant.CommonConstant.FIX_IP_AUTH_ACCOUNT_TYPE;

/**
 * @author: lmz
 * @description: 固定IP知识库实现类
 * @date: 2022/9/9 11:47
 */
@Service
@Slf4j
@DS("mysql")
public class FixedIpServiceImpl extends ServiceImpl<FixedIpMapper, FixedIpEntity> implements FixedIpService {

    @Resource
    private FixedIpMapper fixedIpMapper;

    @Autowired
    FixedIpServiceImpl fixedIpService;

    @Autowired
    private ArchiveFeignClient archiveFeignClient;

    @Resource
    private DataOperation dataOperation;

    public static final String INSERT_TEMPLATE_PERSON = "insert into fixed_ip_archive (id, IP_NAME, IP_ADDRESS, ENTITY_TYPE, REMARK, CREATE_TIME,MODIFY_TIME,INDIVIDUAL_NAME,PERSON_SEX,BIRTH,CARD_ENTERPRISE_TYPE,CARD_NUM_CODE,ADDRESS,WORK_ADDRESS,POSITION,PERSON_DEGREE,PHONE_NUM,PERSON_POLITICAL_STATUS,NATIONALITY_INDUSTRY,JURIDICAL_PERSON,ENTERPRISE_SCALE,ENTERPRISE_EMPLOYEE_NUM,ENTERPRISE_REGISTER_CAPITAL,ORIGINAL_IP_ADDRESS)\n" + "       SELECT\n" + "       ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\n" + "       FROM DUAL\n" + "       WHERE not exists (\n" + "       SELECT id FROM fixed_ip_archive WHERE IP_ADDRESS = ?\n" + "\t   AND DELETE_STATUS = 0)";

    @Autowired
    JdbcTemplate jdbcTemplate;

    private static final String INSERT_DISTRIBUTED_LOCK = "_basic_library_fixed_ip_insert_lock_";

    @Autowired
    private RedisHelper redisHelper;

    private static final Integer FIXED_IP_STRING_CONSTANT = 64;

    private static final Integer FIXED_IP_LONG_STRING_CONSTANT = 255;

    private static final Integer BATCH_INSERT_LIMIT = 2000;



    private static final Integer FIX_IP_ENTITY_GOVERNMENT_ENTERPRISE = 0;

    private static final Integer FIX_IP_ACCOUNT_TYPE_GOVERNMENT_ENTERPRISE = 2;

    private static final String USER_TYPE_FIXED_IP = "3";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnModel saveFixedIp(FixedIpEntity fixedIpEntity, String lang) {
        //判断该固定Ip数据是否存在
        Integer entityType = fixedIpEntity.getEntityType();
        validatorFixedIpParams(fixedIpEntity, entityType, 0, lang);
        EscapeUtil.escapeReflect(fixedIpEntity);
        if (StringUtils.isNotBlank(fixedIpEntity.getWarnMsg())) {
            return ReturnModel.getInstance().error().setMsg(fixedIpEntity.getWarnMsg());
        }
        int updateColumn = 0;
        /*ReturnModel ipObj = archiveFeignClient.queryFixedIpRepair();
        Object data = ipObj.getData();*/
        Long modifyTime = System.currentTimeMillis();
        Long createTime = modifyTime;
        /*if (ObjectUtil.isNotNull(ipObj)) {
            List<String> ipList = (List<String>) data;
            //如果包含，进行数据修复
            if (ipList.size() != 0 && ipList.contains(fixedIpEntity.getIpAddress())) {
                String ipAddress = fixedIpEntity.getIpAddress();
                createTime = fixedIpMapper.selectFixedIpRepair(ipAddress);
                //如果oracle数据库中不存在历史数据，则为插入新数据
                if (createTime == null || createTime == 0) {
                    createTime = System.currentTimeMillis();
                }
            }
        }*/
        //获取针对该IP的分布式锁
        String confirmLock = INSERT_DISTRIBUTED_LOCK + "_" + fixedIpEntity.getIpAddress();
        boolean lock = redisHelper.lock(confirmLock);
        //使用分布式锁处理
        if (lock) {
            QueryWrapper<FixedIpEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("DELETE_STATUS", 0);
            int count = fixedIpService.count(wrapper);
            if (count >= 10000) {
                redisHelper.releaseLock(confirmLock);
                return ReturnModel.getInstance().setMsg(I18nUtils.getMessage("fixed.ip.insertBlock"));
            }
            if (entityType.equals(FixedIpTypeEnum.PERSONAL.getKey())) {
                fixedIpEntity.setCreateTime(createTime);
                fixedIpEntity.setModifyTime(modifyTime);
                updateColumn = fixedIpMapper.insertFixedIpPersonal(fixedIpEntity);
            } else if (entityType.equals(FixedIpTypeEnum.ENTERPRISE.getKey())) {
                fixedIpEntity.setCreateTime(createTime);
                fixedIpEntity.setModifyTime(modifyTime);
                updateColumn = fixedIpMapper.insertFixedIpEnterprise(fixedIpEntity);
            } else {
                redisHelper.releaseLock(confirmLock);
                return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.entityError")).setCode(0);
            }
            int endCount = fixedIpService.count(wrapper);
            if (endCount > 10000) {
                //插入行数大于10000，触发事务回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                redisHelper.releaseLock(confirmLock);
                return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.insertBlock"));
            }
            redisHelper.releaseLock(confirmLock);
        } else {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.insertFail")).setCode(0);
        }
        //否则进行插入
        if (updateColumn == 0) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.insertFail")).setCode(0);
        }

        //创建索引
        addFixIpArchiveIndex(fixedIpEntity);

        return ReturnModel.getInstance().ok();
    }

    private void addFixIpArchiveIndex(FixedIpEntity fixedIpEntity) {
        try {
            //进行固定IP档案的创建
            String id = ArcIdUtil.getAuthAccountId(FIX_IP_AUTH_ACCOUNT_TYPE, fixedIpEntity.getIpAddress());

            //固定ip档案实体类型
            Integer accountType = fixedIpEntity.getEntityType();
            if (FIX_IP_ENTITY_GOVERNMENT_ENTERPRISE.equals(fixedIpEntity.getEntityType())) {
                accountType = FIX_IP_ACCOUNT_TYPE_GOVERNMENT_ENTERPRISE;
            }

            long currentTime = System.currentTimeMillis();
            FixIpArchiveModel fixIpArchiveModel = FixIpArchiveModel.builder()._id(id).id(id).archive_type(ArchiveIndexConstants.ARCHIVE_FIXED_IP_TYPE).create_time(currentTime).archive_name(fixedIpEntity.getIpAddress())
                    .auth_account_type(FIX_IP_AUTH_ACCOUNT_TYPE).sort_score(0L).behavior_num(0L).account_type(accountType).latest_relation_time(currentTime).file_flag(0).build();
            log.info("fixed ip archive create index:{}, fixIpArchiveModel:{}", ArchiveIndexConstants.ARCHIVE_FIXED_IP_INDEX, JsonUtil.toJson(fixIpArchiveModel));

            boolean result = dataOperation.add(ArchiveIndexConstants.ARCHIVE_FIXED_IP_INDEX, fixIpArchiveModel);

            log.info("fixed ip archive create result is {}", result);
        }catch (Exception e) {
            log.error("fixed ip archive create index error: ", e);
        }
    }


    /**
     * 校验字段长度是否符合要求
     *
     * @param fixedIpEntity
     * @param entityType
     * @param type
     * @param lang
     */
    private void validatorFixedIpParams(FixedIpEntity fixedIpEntity, int entityType, int type, String lang) {
        if (type == 0 && StringUtils.isNotBlank(fixedIpEntity.getIpAddress()) && fixedIpEntity.getIpAddress().length() > FIXED_IP_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.IP_ADDRESS.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.IP_ADDRESS.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            }
        }
        if (type == 0 && StringUtils.isNotBlank(fixedIpEntity.getIpName()) && fixedIpEntity.getIpName().length() > FIXED_IP_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.IP_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.IP_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            }
        }
        if (type == 0 && StringUtils.isNotBlank(fixedIpEntity.getIndividualName()) && fixedIpEntity.getIndividualName().length() > FIXED_IP_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.INDIVIDUAL_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.INDIVIDUAL_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            }
        }
        if (StringUtils.isBlank(fixedIpEntity.getRemark())) {
            fixedIpEntity.setRemark("");
        } else if (type == 0 && fixedIpEntity.getRemark().length() > FIXED_IP_LONG_STRING_CONSTANT) {
            fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.REMARK.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, lang));
        }
        if (StringUtils.isBlank(fixedIpEntity.getPhoneNum())) {
            fixedIpEntity.setPhoneNum("");
        } else if (type == 0 && fixedIpEntity.getPhoneNum().length() > FIXED_IP_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.PHONE_NUM.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.REMARK.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            }
        }
        if (StringUtils.isBlank(fixedIpEntity.getCardNumCode())) {
            fixedIpEntity.setCardNumCode("");
        } else if (type == 0 && fixedIpEntity.getCardNumCode().length() > FIXED_IP_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.CARD_NUM_CODE.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.CARD_NUM_CODE.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
            }
        }
        if (StringUtils.isBlank(fixedIpEntity.getAddress())) {
            fixedIpEntity.setAddress("");
        } else if (type == 0 && fixedIpEntity.getAddress().length() > FIXED_IP_LONG_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, lang));
            }
        }
        if (StringUtils.isBlank(fixedIpEntity.getWorkAddress())) {
            fixedIpEntity.setWorkAddress("");
        } else if (type == 0 && fixedIpEntity.getWorkAddress().length() > FIXED_IP_LONG_STRING_CONSTANT) {
            if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.WORK_ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, lang));
            } else {
                fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.WORK_ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, lang));
            }
        }
        if (ObjectUtil.isNull(fixedIpEntity.getCardEnterpriseType())) {
            fixedIpEntity.setCardEnterpriseType(2);
        }
        if (StringUtils.isBlank(fixedIpEntity.getNationalityIndustry())) {
            fixedIpEntity.setNationalityIndustry("");
        } else if (type == 0 && fixedIpEntity.getNationalityIndustry().length() > FIXED_IP_STRING_CONSTANT) {
            fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.NATIONALITY_INDUSTRY.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
        }
        if (StringUtils.isBlank(fixedIpEntity.getPosition())) {
            fixedIpEntity.setPosition("");
        } else if (type == 0 && fixedIpEntity.getPosition().length() > FIXED_IP_STRING_CONSTANT) {
            fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.POSITION.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
        }
        if (StringUtils.isBlank(fixedIpEntity.getPersonPoliticalStatus())) {
            fixedIpEntity.setPersonPoliticalStatus("");
        } else if (type == 0 && fixedIpEntity.getPersonPoliticalStatus().length() > FIXED_IP_STRING_CONSTANT) {
            fixedIpEntity.setWarnMsg(validateLength(FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_POLITICAL_STATUS.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
        }
        if (ObjectUtil.isNull(fixedIpEntity.getPersonSex())) {
            fixedIpEntity.setPersonSex(2);
        }
        if (ObjectUtil.isNull(fixedIpEntity.getPersonDegree())) {
            fixedIpEntity.setPersonDegree(3);
        }
        if (StringUtils.isBlank(fixedIpEntity.getJuridicalPerson())) {
            fixedIpEntity.setJuridicalPerson("");
        } else if (type == 0 && fixedIpEntity.getJuridicalPerson().length() > FIXED_IP_STRING_CONSTANT) {
            fixedIpEntity.setWarnMsg(validateLength(FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.JURIDICAL_PERSON.getKey(), lang), FIXED_IP_STRING_CONSTANT, lang));
        }
        if (ObjectUtil.isNull(fixedIpEntity.getEnterpriseScale())) {
            fixedIpEntity.setEnterpriseScale(5);
        }
    }

    /**
     * 固定Ip存储字段长度校验
     *
     * @param lang
     * @param field
     * @param len
     */
    private static String validateLength(String field, int len, String lang) {
        if ("zh_CN".equals(lang)) {
            return field + " 字符长度超过" + len + " ,请修改";
        } else if ("fr_DZ".equals(lang)) {
            return field + " longueur de caractères supérieure à " + len + " ,s’il vous plaît modifier";
        } else {
            return field + " string length is over " + len + " ,please modify";
        }
    }

    @Override
    public ReturnModel fixedIpList(String userId, Integer onPage, Integer size, Integer deleteStatus, String sortField, Integer sortType, Integer entityType, String keyword, Integer archiveStatus) {
        Map<String, Object> resultMap = new HashMap<>(8);
        String sortSql;
        sortField = getAlias(sortField).toUpperCase();
        if (sortType == 1) {
            sortSql = " ORDER BY " + sortField + " DESC";
        } else {
            sortSql = " ORDER BY " + sortField + " ASC";
        }

        if (StringUtils.isNotBlank(keyword)) {
            keyword = EscapeUtil.escapeSingleQuote(keyword);
            keyword = EscapeUtil.escapeCKMatch(keyword);
        }

        //分页查询
        Page<FixedIpEntity> page = new Page<>();
        page.setCurrent(onPage);
        page.setSize(size);
        List<FixedIpEntity> fixedIpEntities = fixedIpMapper.getFixedIpList(page, deleteStatus, sortSql, entityType, keyword, archiveStatus);

        if (!fixedIpEntities.isEmpty()) {
            StringJoiner stringJoiner = new StringJoiner(",");
            for (FixedIpEntity fixedIpEntity : fixedIpEntities) {
                //根据ip地址查询档案使用处理转换后值
                stringJoiner.add(fixedIpEntity.getIpAddress());
            }
            String ipList = stringJoiner.toString();
            ReturnModel returnModel = archiveFeignClient.queryFixedIpStatus(ipList);
            Object data = returnModel.getData();
            Map<String, List<String>> map = JSONObject.parseObject(JSONObject.toJSONString(data), Map.class);
            List<String> arcIpList = map.get("arcIp");
            //List<String> careIpList = map.get("careIp");
            if (arcIpList != null && !arcIpList.isEmpty()) {
                /*if (careIpList != null && !careIpList.isEmpty()) {
                    for (FixedIpEntity fixedIpEntity : fixedIpEntities) {
                        if (arcIpList.contains(fixedIpEntity.getIpAddress())) {
                            fixedIpEntity.setArchiveStatus(0);
                            if (careIpList.contains(fixedIpEntity.getIpAddress())) {
                                //设置档案状态为关注状态
                                fixedIpEntity.setIsCare(1);
                            } else {
                                fixedIpEntity.setIsCare(0);
                            }
                        } else {
                            fixedIpEntity.setArchiveStatus(1);
                        }
                    }
                } else {*/
                for (FixedIpEntity fixedIpEntity : fixedIpEntities) {
                    if (arcIpList.contains(fixedIpEntity.getIpAddress())) {
                        fixedIpEntity.setArchiveStatus(0);
                        fixedIpEntity.setIsCare(0);
                    } else {
                        fixedIpEntity.setArchiveStatus(1);
                    }
                }
            }

            for (FixedIpEntity fixedIpEntity : fixedIpEntities) {
                //页面显示IP地址时返回原始用户输入ip地址
                fixedIpEntity.setIpAddress(fixedIpEntity.getOriginalIpAddress());
            }
        }

        //Long total = fixedIpMapper.countFixedIpList(deleteStatus, entityType, keyword, archiveStatus);
        resultMap.put("total", page.getTotal());
        resultMap.put("list", fixedIpEntities);
        return ReturnModel.getInstance().setData(resultMap).ok();
    }

    private static String getAlias(String name) {
        if (name.matches("[A-Z]+.*")) {
            return name;
        }
        if (name.matches(".*[A-Z]+.*")) {
            int m = 0;
            for (; m < name.length() - 1; m++) {
                if (name.substring(m, m + 1).matches("[A-Z]")) {
                    break;
                }
            }
            String substring = name.substring(m, m + 1);
            name = name.replaceFirst(substring, "_" + substring.toLowerCase());
            name = getAlias(name);
        }
        return name;
    }

    @Override
    public ReturnModel<?> deleteBatchIp(String ipAddress) {
        List<String> addressList = new ArrayList<>();
        for (String address : ipAddress.split(",")) {
            if (IpUtil.isipv6(address)) {
                address = IPv6ParseUtil.getShortIPv6(address).toLowerCase();
            }

            addressList.add(address);
        }

        if (CollUtil.isEmpty(addressList)) {
            return ReturnModel.getInstance().error().setMsg("ip address is empty");
        }

        Long modifyTime = System.currentTimeMillis();
        int deleteColumn = fixedIpMapper.deleteBatchIp(addressList, modifyTime);

        for (String address : addressList) {
            try {
                //进行固定IP档案ES索引的删除
                boolean deletedResult = dataOperation.deleteById(ArchiveIndexConstants.ARCHIVE_FIXED_IP_INDEX, "archive_name", Lists.newArrayList(address));
                log.info("fixed ip address :{} ES delete result is :{}", address, deletedResult);

                //调用全息档案删除档案统计表数据
                ReturnModel returnModel = archiveFeignClient.fixIpAccountDelete(address);
                if (null == returnModel || returnModel.getCode() != ReturnCode.SUCCESS.getCode()) {
                    log.error("fixed ip doris delete error, address:{}", address);
                }

            }catch (Exception e) {
                log.error("fixed ip delete error, address:{}", address, e);
            }
        }

        return ReturnModel.getInstance().ok().setMsg("fixed ip already delete " + deleteColumn);
    }

    @Override
    public ReturnModel updateArcIp(FixedIpEntity fixedIpEntity, String lang) {
        Integer entityType = fixedIpEntity.getEntityType();
        validatorFixedIpParams(fixedIpEntity, entityType, 0, lang);
        if (StringUtils.isNotBlank(fixedIpEntity.getWarnMsg())) {
            return ReturnModel.getInstance().error().setMsg(fixedIpEntity.getWarnMsg());
        }
        EscapeUtil.escapeReflect(fixedIpEntity);
        int updateColumn = 0;
        Long currentTime = System.currentTimeMillis();
        fixedIpEntity.setModifyTime(currentTime);
        StringJoiner updateSql = new StringJoiner(",");
        if (StringUtils.isNotBlank(fixedIpEntity.getIpName())) {
            updateSql.add("IP_NAME = '" + fixedIpEntity.getIpName() + "'");
        }
        if (fixedIpEntity.getRemark() != null) {
            updateSql.add("REMARK = '" + fixedIpEntity.getRemark() + "'");
        }
        if (StringUtils.isNotBlank(fixedIpEntity.getIndividualName())) {
            updateSql.add("INDIVIDUAL_NAME = '" + fixedIpEntity.getIndividualName() + "'");
        }
        if (fixedIpEntity.getBirth() != null) {
            //0001-01-01日期
            if (fixedIpEntity.getBirth().getTime() == -62135798400000L) {
                updateSql.add("BIRTH = NULL ");
            } else {
                updateSql.add("BIRTH = #{birth}");

            }
        }
        if (fixedIpEntity.getCardEnterpriseType() != null) {
            updateSql.add("CARD_ENTERPRISE_TYPE = " + fixedIpEntity.getCardEnterpriseType());
        }
        if (fixedIpEntity.getCardNumCode() != null) {
            updateSql.add("CARD_NUM_CODE = '" + fixedIpEntity.getCardNumCode() + "'");
        }
        if (fixedIpEntity.getAddress() != null) {
            updateSql.add("ADDRESS = '" + fixedIpEntity.getAddress() + "'");
        }
        if (fixedIpEntity.getWorkAddress() != null) {
            updateSql.add("WORK_ADDRESS = '" + fixedIpEntity.getWorkAddress() + "'");
        }
        if (fixedIpEntity.getPhoneNum() != null) {
            updateSql.add("PHONE_NUM = '" + fixedIpEntity.getPhoneNum() + "'");
        }

        updateSql.add("MODIFY_TIME = " + currentTime);
        String ipAddress = fixedIpEntity.getIpAddress();
        Date birth = fixedIpEntity.getBirth();

        if (fixedIpEntity.getNationalityIndustry() != null) {
            updateSql.add("NATIONALITY_INDUSTRY = '" + fixedIpEntity.getNationalityIndustry() + "'");
        }

        if (entityType.equals(FixedIpTypeEnum.ENTERPRISE.getKey())) {
            if (fixedIpEntity.getEnterpriseScale() != null) {
                updateSql.add("ENTERPRISE_SCALE = " + fixedIpEntity.getEnterpriseScale());
            }
            if (fixedIpEntity.getJuridicalPerson() != null) {
                updateSql.add("JURIDICAL_PERSON = '" + fixedIpEntity.getJuridicalPerson() + "'");
            }
            if (fixedIpEntity.getEnterpriseEmployeeNum() != null) {
                if (fixedIpEntity.getEnterpriseEmployeeNum() < 0) {
                    updateSql.add("ENTERPRISE_EMPLOYEE_NUM = NULL ");
                } else {
                    updateSql.add("ENTERPRISE_EMPLOYEE_NUM = " + fixedIpEntity.getEnterpriseEmployeeNum());
                }
            }
            if (fixedIpEntity.getEnterpriseRegisterCapital() != null) {
                if (fixedIpEntity.getEnterpriseRegisterCapital() < 0) {
                    updateSql.add("ENTERPRISE_REGISTER_CAPITAL = NULL ");
                } else {
                    updateSql.add("ENTERPRISE_REGISTER_CAPITAL = " + fixedIpEntity.getEnterpriseRegisterCapital());
                }
            }

            String sql = updateSql.toString();
            updateColumn = fixedIpMapper.updateFixedIpPersonal(ipAddress, entityType, sql, birth);
        } else if (entityType.equals(FixedIpTypeEnum.PERSONAL.getKey())) {
            if (fixedIpEntity.getPersonSex() != null) {
                updateSql.add("PERSON_SEX = " + fixedIpEntity.getPersonSex());
            }
            if (fixedIpEntity.getPosition() != null) {
                updateSql.add("POSITION = '" + fixedIpEntity.getPosition() + "'");
            }
            if (fixedIpEntity.getPersonDegree() != null) {
                updateSql.add("PERSON_DEGREE = " + fixedIpEntity.getPersonDegree());
            }
            if (fixedIpEntity.getPersonPoliticalStatus() != null) {
                updateSql.add("PERSON_POLITICAL_STATUS = '" + fixedIpEntity.getPersonPoliticalStatus() + "'");
            }
            String sql = updateSql.toString();
            updateColumn = fixedIpMapper.updateFixedIpEnterprise(ipAddress, entityType, sql, birth);
        } else {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.entityError")).setCode(0);
        }
        if (updateColumn == 0) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.updateError")).setCode(0);
        }

        return ReturnModel.getInstance().ok().setMsg("update fixed ip is success");
    }

    @Override
    public ReturnModel queryIpAddressList(Integer size) {
        long startTime = DateUtil.beginOfDay(new Date()).getTime();
        List<String> ipAddressList = fixedIpMapper.selectIpAddressList(size, startTime);
        return ReturnModel.getInstance().ok().setData(ipAddressList);
    }

    @Override
    public void fixedIpModel(Integer entityType, String lang, HttpServletResponse response) {
        if (entityType.equals(FixedIpTypeEnum.PERSONAL.getKey())) {
            importPersonalModel(lang, response);
        } else if (entityType.equals(FixedIpTypeEnum.ENTERPRISE.getKey())) {
            importEnterpriseModel(lang, response);
        }
    }

    private void importEnterpriseModel(String lang, HttpServletResponse response) {
        ServletOutputStream fileOutputStream = null;
        XSSFWorkbook workbook = null;
        try {
            String fullPath = "企业模板.xlsx";
            if (lang.equals("fr_DZ")) {
                fullPath = "Modèle de société.xlsx";
            }
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=" + fullPath);
            fileOutputStream = response.getOutputStream();
            workbook = new XSSFWorkbook();
            XSSFSheet sheet1 = workbook.createSheet("Sheet1");
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            /**
             * 创建行首
             */
            sheet1 = productRuleEnums(sheet1, EntityTypeEnum.getByValue(lang, 0), 2);
            sheet1 = productRuleEnums(sheet1, CardEnterpriseTypeEnum.getByValue(lang, 0), 6);
            sheet1 = productRuleEnums(sheet1, EnterpriseScaleEnum.getByValue(lang), 12);
            //设置日期格式
//            sheet1 = productDateRule(sheet1,5);
            XSSFRow rowXS = sheet1.createRow(0);
            String[] enterpriseEnums = FixedIpEnterpriseEnum.getByValue(lang);
            for (int i = 0; i < enterpriseEnums.length; i++) {
                XSSFCell cell = rowXS.createCell(i, CellType.STRING);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(enterpriseEnums[i]);
            }
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            workbook.close();
        } catch (Exception e) {
            log.error("Read xlsx is error {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(workbook);
            IOUtils.closeQuietly(fileOutputStream);
        }
    }

    private void importPersonalModel(String lang, HttpServletResponse response) {
        ServletOutputStream fileOutputStream = null;
        XSSFWorkbook workbook = null;
        try {
            String fullPath = "个人模板.xlsx";
            if (lang.equals("fr_DZ")) {
                fullPath = "Modèle de personne.xlsx";
            }
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=" + fullPath);
            fileOutputStream = response.getOutputStream();
            workbook = new XSSFWorkbook();
            XSSFSheet sheet1 = workbook.createSheet("Sheet1");
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            /**
             * 创建行首
             */
            sheet1 = productRuleEnums(sheet1, EntityTypeEnum.getByValue(lang, 1), 2);
            sheet1 = productRuleEnums(sheet1, PersonSexEnum.getByValue(lang), 5);
            sheet1 = productRuleEnums(sheet1, CardEnterpriseTypeEnum.getByValue(lang, 1), 7);
            sheet1 = productRuleEnums(sheet1, PersonDegreeEnum.getByValue(lang), 13);
            //设置日期格式
//            sheet1 = productDateRule(sheet1,6);
            String[] personEnums = FixedIpPersonEnum.getByValue(lang);
            XSSFRow row = sheet1.createRow(0);
            for (int i = 0; i < personEnums.length; i++) {
                XSSFCell cell = row.createCell(i, CellType.STRING);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(personEnums[i]);
            }
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            workbook.close();
        } catch (Exception e) {
            log.error("Read xlsx is error {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(workbook);
            IOUtils.closeQuietly(fileOutputStream);
        }
    }

    private XSSFSheet productRuleEnums(XSSFSheet sheet1, String[] data, Integer startColumn) {
        XSSFDataValidationHelper validationHelper = (XSSFDataValidationHelper) sheet1.getDataValidationHelper();
        XSSFDataValidationConstraint dataValidationConstraint = (XSSFDataValidationConstraint) validationHelper.createExplicitListConstraint(data);
        CellRangeAddressList addressList = null;
        XSSFDataValidation validation = null;
        addressList = new CellRangeAddressList(1, 10001, startColumn, startColumn);
        validation = (XSSFDataValidation) validationHelper.createValidation(dataValidationConstraint, addressList);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(true);
        sheet1.addValidationData(validation);
        return sheet1;
    }

    private XSSFSheet productDateRule(XSSFSheet sheet1, Integer startColumn) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy");
        XSSFDataValidationHelper validationHelper = (XSSFDataValidationHelper) sheet1.getDataValidationHelper();
        XSSFDataValidationConstraint dataValidationConstraint = (XSSFDataValidationConstraint) validationHelper.createTimeConstraint(DataValidationConstraint.OperatorType.LESS_OR_EQUAL, simpleDateFormat.format(new Date()), simpleDateFormat.format(new Date()));
        CellRangeAddressList addressList = null;
        XSSFDataValidation validation = null;
        addressList = new CellRangeAddressList(1, 10001, startColumn, startColumn);
        validation = (XSSFDataValidation) validationHelper.createValidation(dataValidationConstraint, addressList);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(true);
        sheet1.addValidationData(validation);
        return sheet1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnModel importFixedIps(Integer entityType, String lang, MultipartFile file, HttpServletResponse response) {
        List<FixedIpEntity> fixedIpEntityList = new ArrayList<>();
        QueryWrapper<FixedIpEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("DELETE_STATUS", 0);
        int count = 10000 - fixedIpService.count(wrapper);
        List<String> ipList = fixedIpMapper.selectFixedIpList();
        /*ReturnModel ipObj = archiveFeignClient.queryFixedIpRepair();
        Object data = ipObj.getData();
        List<String> deleteIpList = (List<String>) data;
        if (deleteIpList == null) {
            deleteIpList = new ArrayList<>();
        }*/
        List<String> deleteIpList = new ArrayList<>();
        Map<String, Boolean> acquireMap = new HashMap<>();
        queryPersonalModel(lang, file, fixedIpEntityList, response, entityType, count, ipList, deleteIpList, acquireMap);
        if (!acquireMap.isEmpty()) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("fixed.ip.batchTwo"));
        }
        int size = 0;
        if (!fixedIpEntityList.isEmpty()) {
            boolean resultFlag = batchInsert(fixedIpEntityList);

            //进行固定IP档案的创建
            if (resultFlag) {
                fixedIpEntityList.forEach(this::addFixIpArchiveIndex);
            }
        }
        return ReturnModel.getInstance().ok().setMsg("insert size:" + size);
    }

    private void queryPersonalModel(String lang, MultipartFile file, List<FixedIpEntity> fixedIpEntityList, HttpServletResponse response, Integer entityType, int count, List<String> ipList, List<String> deleteIpList, Map<String, Boolean> acquireMap) {
        String originalFilename = file.getOriginalFilename();
        InputStream inputStream = null;
        XSSFWorkbook sheets = null;
        ServletOutputStream fileOutputStream = null;
        boolean writeValidate = false;
        try {
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=" + originalFilename);
            fileOutputStream = response.getOutputStream();
            inputStream = file.getInputStream();
            sheets = new XSSFWorkbook(inputStream);
            XSSFSheet sheet = sheets.getSheetAt(0);
            //创建绘图对象
            XSSFDrawing drawing = sheet.createDrawingPatriarch();
            Iterator<Row> iterator = sheet.iterator();
            int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();
            if (physicalNumberOfRows > BATCH_INSERT_LIMIT) {
                acquireMap.put("msg", true);
                return;
            }
            //设置批注
            //超出指定行数
            if (physicalNumberOfRows > count + 1) {
                log.error("row nums are more than length limit " + count);
                XSSFCell cell = sheet.getRow(0).getCell(0);
                XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                cellComment.setAuthor("DEYE");
                if (count > 0) {
                    cellComment.setString(new XSSFRichTextString("more than 10000 rows of data,please reduce to less than " + count));
                } else {
                    cellComment.setString(new XSSFRichTextString("more than 10000 rows of data,batch insert can't  used"));
                }
                writeValidate = true;
                cell.setCellComment(cellComment);
            }
            XSSFRow row = (XSSFRow) iterator.next();
            Iterator<Cell> iterator1 = row.iterator();
            int index = 0;
            while (iterator1.hasNext()) {
                Cell cell = iterator1.next();
                //校验行首是否符合标准
                String stringCellValue = cell.getStringCellValue();
                String personLine = "";
                if (entityType.equals(FixedIpTypeEnum.PERSONAL.getKey())) {
                    personLine = FixedIpPersonEnum.getByKey(stringCellValue, lang, index);
                } else if (entityType.equals(FixedIpTypeEnum.ENTERPRISE.getKey())) {
                    personLine = FixedIpEnterpriseEnum.getByKey(stringCellValue, lang, index);
                }

                if (StringUtils.isBlank(personLine)) {
                    XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                    cellComment.setAuthor("DEYE");
                    cellComment.setString(new XSSFRichTextString("The beginning of the line does not match the template"));
                    cell.setCellComment(cellComment);
                    writeValidate = true;
                }
                index++;
            }
            while (iterator.hasNext()) {
                //提取数据入库
                XSSFRow row1 = (XSSFRow) iterator.next();
                FixedIpEntity fixedIpEntity = new FixedIpEntity();
                XSSFCell indexCell = null;
                XSSFComment cellComment = null;
                try {
                    for (int i = 0; i < row1.getLastCellNum() && i < 16; i++) {
                        //读取一行所有数据
                        XSSFCell cell = row1.getCell(i);
                        indexCell = cell;
                        String cellStr = ExcelUtil.getCellValue(row1, i).toString();
                        if (i == 0 || i == 1 || i == 2 || i == 4) {
                            if (StringUtils.isBlank(cellStr)) {
                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                cellComment.setAuthor("DEYE");
                                cellComment.setString(new XSSFRichTextString("The keyword fields cannot be empty or null"));
                                if (ObjectUtil.isNull(cell)) {
                                    cell = row1.createCell(i);
                                }
                                cell.setCellComment(cellComment);
                                writeValidate = true;
                                continue;
                            }
                        }
                        //ipv6名称规范化
                        if (i == 1) {
                            //假如在ipList中存在，表明ip在知识库中已存在
                            String originalIpAddress = cellStr;
                            if (!IpUtil.isipv4(cellStr)) {
                                if (cellStr.matches(IPv6ParseUtil.IPv6Reg)) {
                                    cellStr = IPv6ParseUtil.getShortIPv6(cellStr).toLowerCase();
                                } else {
                                    log.error("ip address is error,and ip is {}", cellStr);
                                    cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                    cellComment.setAuthor("DEYE");
                                    cellComment.setString(new XSSFRichTextString("ip address is error,please modify"));
                                    if (ObjectUtil.isNull(cell)) {
                                        cell = row1.createCell(i);
                                    }
                                    cell.setCellComment(cellComment);
                                    writeValidate = true;
                                    continue;
                                }
                            }
                            if (ipList.contains(cellStr.trim())) {
                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                cellComment.setAuthor("DEYE");
                                cellComment.setString(new XSSFRichTextString("ip address is exists,please delete it"));
                                if (ObjectUtil.isNull(cell)) {
                                    cell = row1.createCell(i);
                                }
                                cell.setCellComment(cellComment);
                                writeValidate = true;
                                continue;
                            }
                            //假如在deleteIpList中存在，表明ip在知识库中存在已删除的数据
                            if (deleteIpList.contains(cellStr.trim())) {
                                Long createTime = fixedIpMapper.selectFixedIpRepair(cellStr.trim());
                                fixedIpEntity.setCreateTime(createTime);
                            }

                            fixedIpEntity.setIpAddress(cellStr);
                            fixedIpEntity.setOriginalIpAddress(originalIpAddress);
                        }
                        //号码非纯数字
                        if (i == 8) {
                            if (StringUtils.isNotBlank(cellStr)) {
                                boolean matches = cellStr.matches("^[0-9]*\\.0+$");
                                boolean matches1 = cellStr.matches("^[[0-9]*]*");
                                if (!matches && !matches1) {
                                    //标记
                                    cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                    cellComment.setAuthor("DEYE");
                                    cellComment.setString(new XSSFRichTextString("The number must consist of digits"));
                                    if (ObjectUtil.isNull(cell)) {
                                        cell = row1.createCell(i);
                                    }
                                    cell.setCellComment(cellComment);
                                    writeValidate = true;
                                    continue;
                                }
                            }
                        }

                        //员工人数必须为正整数、
                        if ((i == 13 && entityType.equals(EntityTypeEnum.ENTERPRISE.getKey())) || (i == 14 && entityType.equals(EntityTypeEnum.ENTERPRISE.getKey()))) {
                            if (ObjectUtil.isNull(cell)) {
                                continue;
                            }
                            try {
                                double numericCellValue = cell.getNumericCellValue();
                                if (numericCellValue < 0) {
                                    log.error("the type of employees must be a positive Integer,and ip is {}", cellStr);
                                    cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                    cellComment.setAuthor("DEYE");
                                    cellComment.setString(new XSSFRichTextString("nums are error,please modify"));
                                    if (ObjectUtil.isNull(cell)) {
                                        cell = row1.createCell(i);
                                    }
                                    cell.setCellComment(cellComment);
                                    writeValidate = true;
                                    continue;
                                }
                                String s = String.valueOf(numericCellValue);
                                if (s.contains(".")) {
                                    String[] split = s.split("\\.");
                                    cellStr = split[0];
                                    boolean matches = cellStr.matches("[\\d]+");
                                    if (!matches) {
                                        log.error("the type of employees must be a positive Integer,and ip is {}", cellStr);
                                        cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                        cellComment.setAuthor("DEYE");
                                        cellComment.setString(new XSSFRichTextString("nums are error,please modify"));
                                        if (ObjectUtil.isNull(cell)) {
                                            cell = row1.createCell(i);
                                        }
                                        cell.setCellComment(cellComment);
                                        writeValidate = true;
                                        continue;
                                    }
                                } else {
                                    boolean matches = s.matches("[\\d]+");
                                    if (!matches) {
                                        log.error("the type of employees must be a positive Integer,and ip is {}", cellStr);
                                        cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                        cellComment.setAuthor("DEYE");
                                        cellComment.setString(new XSSFRichTextString("nums are error,please modify"));
                                        if (ObjectUtil.isNull(cell)) {
                                            cell = row1.createCell(i);
                                        }
                                        cell.setCellComment(cellComment);
                                        writeValidate = true;
                                        continue;
                                    }
                                }
                            } catch (Exception e) {
                                log.error("the type of employees must be a positive Integer,and ip is {}", cellStr);
                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                cellComment.setAuthor("DEYE");
                                cellComment.setString(new XSSFRichTextString("data not are digits,please modify"));
                                if (ObjectUtil.isNull(cell)) {
                                    cell = row1.createCell(i);
                                }
                                cell.setCellComment(cellComment);
                                writeValidate = true;
                                continue;
                            }

                        }

                        Map<String, String> resMap = new HashMap<>(8);
                        //日期如果存在，格式必须为dd-MM-yyyy
                        if ((i == 5 && entityType.equals(EntityTypeEnum.ENTERPRISE.getKey())) || (i == 6 && entityType.equals(EntityTypeEnum.PERSON.getKey()))) {
                            if (ObjectUtil.isNull(cell)) {
                                continue;
                            }
                            try {
                                long maxTime = DateUtil.beginOfDay(new Date()).getTime();
                                if (Cell.CELL_TYPE_STRING != cell.getCellType()) {
                                    //纯数字和日期格式
                                    if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                                        if (cell.getDateCellValue() != null) {
                                            Date dateCellValue = cell.getDateCellValue();
                                            if (dateCellValue.getTime() > maxTime) {
                                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                                cellComment.setAuthor("DEYE");
                                                cellComment.setString(new XSSFRichTextString("The maximum date cannot exceed the current day"));
                                                if (ObjectUtil.isNull(cell)) {
                                                    cell = row1.createCell(i);
                                                }
                                                cell.setCellComment(cellComment);
                                                writeValidate = true;
                                                continue;
                                            } else {
                                                getFixedIpEntity(fixedIpEntity, i, dateCellValue, entityType, lang, resMap);
                                            }

                                            if (resMap.size() != 0) {
                                                String msg = resMap.get("msg");
                                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                                cellComment.setAuthor("DEYE");
                                                cellComment.setString(new XSSFRichTextString(msg));
                                                if (ObjectUtil.isNull(cell)) {
                                                    cell = row1.createCell(i);
                                                }
                                                cell.setCellComment(cellComment);
                                                writeValidate = true;
                                                continue;
                                            }
                                        }

                                    } else {
                                        cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                        cellComment.setAuthor("DEYE");
                                        cellComment.setString(new XSSFRichTextString("date format is error,please modify,format must be yyyy/MM/dd or dd/MM/yyyy"));
                                        if (ObjectUtil.isNull(cell)) {
                                            cell = row1.createCell(i);
                                        }
                                        cell.setCellComment(cellComment);
                                        writeValidate = true;
                                        continue;
                                    }
                                } else {
                                    List<DateTimeFormatter> dateTimeFormatters = new ArrayList<>();
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("dd/M/yyyy"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("d/M/yyyy"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("d/MM/yyyy"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("yyyy/M/dd"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("yyyy/M/d"));
                                    dateTimeFormatters.add(DateTimeFormatter.ofPattern("yyyy/MM/d"));
                                    LocalDate parse = null;
                                    for (DateTimeFormatter dateTimeFormatter : dateTimeFormatters) {
                                        try {
                                            parse = LocalDate.parse(cell.getStringCellValue(), dateTimeFormatter);
                                            if (parse != null) {
                                                break;
                                            }
                                        } catch (Exception e) {
                                            //No code here
                                        }
                                    }
                                    if (parse == null) {
                                        cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                        cellComment.setAuthor("DEYE");
                                        cellComment.setString(new XSSFRichTextString("date format is error,please modify,format must be yyyy/MM/dd or dd/MM/yyyy"));
                                        if (ObjectUtil.isNull(cell)) {
                                            cell = row1.createCell(i);
                                        }
                                        cell.setCellComment(cellComment);
                                        writeValidate = true;
                                        continue;
                                    }
                                    if (DateUtil.parse(parse + "").getTime() > maxTime) {
                                        cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                        cellComment.setAuthor("DEYE");
                                        cellComment.setString(new XSSFRichTextString("The maximum date cannot exceed the current day"));
                                        if (ObjectUtil.isNull(cell)) {
                                            cell = row1.createCell(i);
                                        }
                                        cell.setCellComment(cellComment);
                                        writeValidate = true;
                                        continue;
                                    }
                                    getFixedIpEntity(fixedIpEntity, i, parse, entityType, lang, resMap);
                                }
                            } catch (Exception e) {
                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                cellComment.setAuthor("DEYE");
                                cellComment.setString(new XSSFRichTextString("date format is error,please modify,format must be yyyy/MM/dd or dd/MM/yyyy"));
                                if (ObjectUtil.isNull(cell)) {
                                    cell = row1.createCell(i);
                                }
                                cell.setCellComment(cellComment);
                                writeValidate = true;
                                continue;
                            }

                        } else if (StringUtils.isNotBlank(cellStr)) {
                            getFixedIpEntity(fixedIpEntity, i, cellStr, entityType, lang, resMap);
                            if (resMap.size() != 0) {
                                String msg = resMap.get("msg");
                                cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                                cellComment.setAuthor("DEYE");
                                cellComment.setString(new XSSFRichTextString(msg));
                                if (ObjectUtil.isNull(cell)) {
                                    cell = row1.createCell(i);
                                }
                                cell.setCellComment(cellComment);
                                writeValidate = true;
                                continue;
                            }
                        }


                    }
                    if (fixedIpEntity.getCreateTime() == null || fixedIpEntity.getCreateTime() == 0) {
                        fixedIpEntity.setCreateTime(System.currentTimeMillis());
                    }
                    fixedIpEntity.setModifyTime(System.currentTimeMillis());
                    //校验时建立每列和fixedIpEntity的关系
                    validatorFixedIpParams(fixedIpEntity, entityType, 1, lang);
                    EscapeUtil.escapeReflect(fixedIpEntity);
                    fixedIpEntity.setId(IdUtil.getSnowflakeNextId());
                    fixedIpEntityList.add(fixedIpEntity);

                } catch (Exception e) {
                    log.error("read fixed ip is error {}", fixedIpEntityList.toString(), e.getMessage());
                    if (indexCell == null) {
                        indexCell = row1.getCell(16);
                    }
                    if (Objects.isNull(cellComment)) {
                        cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                    }
                    cellComment.setAuthor("DEYE");
                    cellComment.setString(new XSSFRichTextString("data conversion error"));
                    if (ObjectUtil.isNull(indexCell)) {
                        indexCell = row1.createCell(16);
                    }
                    indexCell.setCellComment(cellComment);
                    writeValidate = true;
                    continue;
                }

            }
            //如果成功导出即不需要返回用户提示excel信息
            if (writeValidate) {
                sheets.write(fileOutputStream);
                fixedIpEntityList.clear();
            }
            inputStream.close();
            sheets.close();
        } catch (Exception e) {
            log.error("read fixed ip is error {}，lang is {}", fixedIpEntityList.toString(), lang, e);
        }

    }

    public static boolean isCellNumberFormatted(Cell cell) {
        if (cell == null) {
            return false;
        } else {
            boolean bDate = false;
            double d = cell.getNumericCellValue();
            return bDate;
        }
    }


    private static void getFixedIpEntity(FixedIpEntity fixedIpEntity, int i, Object cellFormatValue, int entityType, String lang, Map<String, String> resMap) {
        if (entityType == EntityTypeEnum.ENTERPRISE.getKey()) {
            switch (i) {
                case 0:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.IP_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setIpName(cellFormatValue + "");
                    break;
                case 1:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.IP_ADDRESS.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setIpAddress(cellFormatValue + "");
                    break;
                case 2:
                    Integer type = EntityTypeEnum.getKeyByValue(cellFormatValue + "", lang);
                    if (type < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTITY_TYPE.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTITY_TYPE.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTITY_TYPE.getKey(), lang) + "value is out of range");
                        }

                    }
                    fixedIpEntity.setEntityType(type);
                    break;
                case 3:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.REMARK.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setRemark(cellFormatValue + "");
                    break;
                case 4:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.INDIVIDUAL_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setIndividualName(cellFormatValue + "");
                    break;
                case 5:
                    if (cellFormatValue instanceof Date) {
                        java.util.Date date = (java.util.Date) cellFormatValue;
                        fixedIpEntity.setBirth(new Timestamp(date.getTime()));
                    } else if (cellFormatValue instanceof Double || cellFormatValue instanceof Long) {
                        String join = StringUtils.join(cellFormatValue);
                        Long birth = JSONObject.parseObject(join, Long.class);
                        fixedIpEntity.setBirth(new Timestamp(birth));
                    } else {
                        fixedIpEntity.setBirth(new Timestamp(DateUtil.parse(cellFormatValue + "").getTime()));
                    }
                    break;
                case 6:
                    Integer cardType = CardEnterpriseTypeEnum.getKeyByValue(cellFormatValue + "", lang, 0);
                    if (cardType < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.CARD_ENTERPRISE_TYPE.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.CARD_ENTERPRISE_TYPE.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.CARD_ENTERPRISE_TYPE.getKey(), lang) + " value is out of range");
                        }
                    }
                    fixedIpEntity.setCardEnterpriseType(cardType);
                    break;
                case 7:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.CARD_NUM_CODE.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setCardNumCode(cellFormatValue + "");
                    break;
                case 8:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.PHONE_NUM.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setPhoneNum(cellFormatValue + "");
                    break;
                case 9:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setAddress(cellFormatValue + "");
                    break;
                case 10:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.WORK_ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setWorkAddress(cellFormatValue + "");
                    break;
                case 11:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.JURIDICAL_PERSON.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setJuridicalPerson(cellFormatValue + "");
                    break;
                case 12:
                    Integer scale = EnterpriseScaleEnum.getKeyByValue(cellFormatValue + "", lang);
                    if (scale < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTERPRISE_SCALE.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTERPRISE_SCALE.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpEnterpriseEnum.ENTERPRISE_SCALE.getKey() + " value is out of range");
                        }
                    }
                    fixedIpEntity.setEnterpriseScale(scale);
                    break;
                case 13:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTERPRISE_REGISTER_CAPITAL.getKey(), lang), 11, resMap, lang);
                    fixedIpEntity.setEnterpriseEmployeeNum(Long.valueOf(cellFormatValue + ""));
                    break;
                case 14:
                    validateCellLength(cellFormatValue + "", FixedIpEnterpriseEnum.getKeyLang(FixedIpEnterpriseEnum.ENTERPRISE_REGISTER_CAPITAL.getKey(), lang), 11, resMap, lang);
                    fixedIpEntity.setEnterpriseRegisterCapital(Double.valueOf(cellFormatValue + ""));
                    break;
                default:
                    break;
            }
        } else if (entityType == EntityTypeEnum.PERSON.getKey()) {
            switch (i) {
                case 0:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.IP_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setIpName(cellFormatValue + "");
                    break;
                case 1:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.IP_ADDRESS.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setIpAddress(cellFormatValue + "");
                    break;
                case 2:
                    Integer type = EntityTypeEnum.getKeyByValue(cellFormatValue + "", lang);
                    if (type < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.ENTITY_TYPE.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.ENTITY_TYPE.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.ENTITY_TYPE.getKey(), lang) + " value is out of range");
                        }
                    }
                    fixedIpEntity.setEntityType(type);
                    break;
                case 3:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.REMARK.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setRemark(cellFormatValue + "");
                    break;
                case 4:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.INDIVIDUAL_NAME.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setIndividualName(cellFormatValue + "");
                    break;
                case 5:
                    Integer sex = PersonSexEnum.getKeyByValue(cellFormatValue + "", lang);
                    if (sex < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_SEX.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_SEX.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_SEX.getKey(), lang) + " value is out of range");
                        }
                    }
                    fixedIpEntity.setPersonSex(sex);
                    break;
                case 6:
                    if (cellFormatValue instanceof Date) {
                        java.util.Date date = (java.util.Date) cellFormatValue;
                        fixedIpEntity.setBirth(new Timestamp(date.getTime()));
                    } else if (cellFormatValue instanceof Double || cellFormatValue instanceof Long) {
                        String join = StringUtils.join(cellFormatValue);
                        Long birth = JSONObject.parseObject(join, Long.class);
                        fixedIpEntity.setBirth(new Timestamp(birth));
                    } else {
                        fixedIpEntity.setBirth(new Timestamp(DateUtil.parse(cellFormatValue + "").getTime()));
                    }
                    break;
                case 7:
                    Integer cardType = CardEnterpriseTypeEnum.getKeyByValue(cellFormatValue + "", lang, 1);
                    if (cardType < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.CARD_ENTERPRISE_TYPE.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.CARD_ENTERPRISE_TYPE.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.CARD_ENTERPRISE_TYPE.getKey(), lang) + " value is out of range");
                        }
                    }
                    fixedIpEntity.setCardEnterpriseType(cardType);
                    break;
                case 8:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PHONE_NUM.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setPhoneNum(cellFormatValue + "");
                    break;
                case 9:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.CARD_NUM_CODE.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setCardNumCode(cellFormatValue + "");
                    break;
                case 10:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setAddress(cellFormatValue + "");
                    break;
                case 11:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.WORK_ADDRESS.getKey(), lang), FIXED_IP_LONG_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setWorkAddress(cellFormatValue + "");
                    break;
                case 12:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.POSITION.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setPosition(cellFormatValue + "");
                    break;
                case 13:
                    Integer degreeType = PersonDegreeEnum.getKeyByValue(cellFormatValue + "", lang);
                    if (degreeType < 0) {
                        if ("zh_CN".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_DEGREE.getKey(), lang) + " 值超出限定范围");
                        } else if ("fr_DZ".equals(lang)) {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_DEGREE.getKey(), lang) + " valeur hors limites");
                        } else {
                            resMap.put("msg", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_DEGREE.getKey(), lang) + " value is out of range");
                        }
                    }
                    fixedIpEntity.setPersonDegree(degreeType);
                    break;
                case 14:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.PERSON_POLITICAL_STATUS.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setPersonPoliticalStatus(cellFormatValue + "");
                    break;
                case 15:
                    validateCellLength(cellFormatValue + "", FixedIpPersonEnum.getKeyLang(FixedIpPersonEnum.NATIONALITY_INDUSTRY.getKey(), lang), FIXED_IP_STRING_CONSTANT, resMap, lang);
                    fixedIpEntity.setNationalityIndustry(cellFormatValue + "");
                    break;
                default:
                    break;
            }
        }

    }

    /**
     * 固定Ip存储字段长度校验
     *
     * @param cellStr
     * @param field
     * @param len
     * @param resMap
     */
    private static void validateCellLength(String cellStr, String field, int len, Map<String, String> resMap, String lang) {
        if (cellStr.length() > len) {
            if ("zh_CN".equals(lang)) {
                resMap.put("msg", field + " 字符长度超过" + len + " ,请修改");
            } else if ("fr_DZ".equals(lang)) {
                resMap.put("msg", field + " longueur de caractères supérieure à " + len + " ,s’il vous plaît modifier");
            } else {
                resMap.put("msg", field + " string length is over " + len + " ,please modify");
            }
        }
    }

    private static Object getCellFormatValue(Cell cell) {
        Object cellValue;
        if (cell != null) {
            switch (cell.getCellTypeEnum()) {
                case NUMERIC:
                    cellValue = cell.getNumericCellValue();
                    break;
                case FORMULA:
                    if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                        cellValue = cell.getDateCellValue();
                    } else {
                        cellValue = cell.getNumericCellValue();
                    }
                    break;
                case STRING:
                    cellValue = cell.getRichStringCellValue().getString();
                    break;
                default:
                    cellValue = "";
                    break;
            }
        } else {
            cellValue = "";
        }
        return cellValue;
    }

    private boolean batchInsert(List<FixedIpEntity> fixedIpEntityList) {
        String sql = INSERT_TEMPLATE_PERSON;

        boolean result = true;
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                    log.info("sql:" + sql);
                    final FixedIpEntity fixedIpEntity = fixedIpEntityList.get(i);

                    preparedStatement.setLong(1, fixedIpEntity.getId());
                    preparedStatement.setString(2, fixedIpEntity.getIpName());
                    preparedStatement.setString(3, fixedIpEntity.getIpAddress());
                    preparedStatement.setInt(4, fixedIpEntity.getEntityType());
                    preparedStatement.setString(5, fixedIpEntity.getRemark());
                    preparedStatement.setLong(6, fixedIpEntity.getCreateTime());
                    preparedStatement.setLong(7, fixedIpEntity.getModifyTime());
                    preparedStatement.setString(8, fixedIpEntity.getIndividualName());
                    preparedStatement.setInt(9, fixedIpEntity.getPersonSex());
                    if (ObjectUtil.isNull(fixedIpEntity.getBirth())) {
                        preparedStatement.setDate(10, null);
                    } else {
                        preparedStatement.setDate(10, new java.sql.Date(fixedIpEntity.getBirth().getTime()));
                    }
                    preparedStatement.setInt(11, fixedIpEntity.getCardEnterpriseType());
                    preparedStatement.setString(12, fixedIpEntity.getCardNumCode());
                    preparedStatement.setString(13, fixedIpEntity.getAddress());
                    preparedStatement.setString(14, fixedIpEntity.getWorkAddress());
                    preparedStatement.setString(15, fixedIpEntity.getPosition());
                    preparedStatement.setInt(16, fixedIpEntity.getPersonDegree());
                    preparedStatement.setString(17, fixedIpEntity.getPhoneNum());
                    preparedStatement.setString(18, fixedIpEntity.getPersonPoliticalStatus());
                    preparedStatement.setString(19, fixedIpEntity.getNationalityIndustry());
                    preparedStatement.setString(20, fixedIpEntity.getJuridicalPerson());
                    preparedStatement.setInt(21, fixedIpEntity.getEnterpriseScale());
                    preparedStatement.setObject(22, fixedIpEntity.getEnterpriseEmployeeNum());
                    preparedStatement.setObject(23, fixedIpEntity.getEnterpriseRegisterCapital());
                    preparedStatement.setString(24, fixedIpEntity.getOriginalIpAddress());
                    preparedStatement.setString(25, fixedIpEntity.getIpAddress());
                }

                @Override
                public int getBatchSize() {
                    return fixedIpEntityList.size();
                }
            });
        } catch (Exception e) {
            result = false;
            log.error("批量写入失败", e);
        }

        return result;
    }

    @Override
    public ReturnModel scanIpTaskInfo(Long lastScheduledTime, List<String> ipList, Integer onPage, Integer pageSize) {
        Map<String, Object> resultMap = new HashMap<>(8);
        int offsetPoint = (onPage - 1) * pageSize + 1;
        int offsetPage = onPage * pageSize;
        StringJoiner stringJoiner = new StringJoiner(",");
        if (ipList != null && ipList.size() > 0) {
            for (String ip : ipList) {
                stringJoiner.add("'" + ip + "'");
            }
        }
        String ipStr = stringJoiner.toString();
        List<FixedIpEntity> fixedIpEntities = fixedIpMapper.selectFixedIpNew(ipStr, offsetPoint, offsetPage, lastScheduledTime);
        Integer total = fixedIpMapper.countFixedIpNew(ipStr, lastScheduledTime);
        resultMap.put("list", fixedIpEntities);
        resultMap.put("total", total);
        return ReturnModel.getInstance().ok().setData(resultMap);
    }

    @Override
    public ReturnModel repairIpDatabaseInfo(String ipAddress) {
        return ReturnModel.getInstance().ok().setData(false);
    }

    @Override
    public ReturnModel queryFixedIpStatus(String ipList) {
        int resData;

        List<String> addressList = new ArrayList<>();
        for (String address : ipList.split(",")) {
            if (IpUtil.isipv6(address)) {
                address = IPv6ParseUtil.getShortIPv6(address).toLowerCase();
            }

            addressList.add(address);
        }

        ReturnModel returnModel = archiveFeignClient.queryFixedIpStatus(String.join(",", addressList));
        Object data = returnModel.getData();
        Map<String, List<String>> map = JSONObject.parseObject(JSONObject.toJSONString(data), Map.class);
        List<String> arcIpList = map.get("arcIp");
        List<String> careIpList = map.get("careIp");
        if (careIpList != null && !careIpList.isEmpty()) {
            resData = FixedIpArchiveStatus.CARE.getKey();
        } else {
            if (arcIpList != null && !arcIpList.isEmpty()) {
                resData = FixedIpArchiveStatus.ARCHIVES.getKey();
            } else {
                resData = FixedIpArchiveStatus.NOBODY.getKey();
            }
        }
        return ReturnModel.getInstance().ok().setData(resData);
    }

    /**
     * @param ip       ip信息
     * @param startDay 查询开始日期
     * @param endDay   查询结束日期
     * @return 固定IP关联上网用户信息
     */
    @Override
    public ReturnModel<?> queryFixedIpUser(String ip, String startDay, String endDay) {
        Date startDate = com.semptian.utils.DateUtil.parse(startDay, com.semptian.utils.DateUtil.YYYY_MM_DD);
        Date endDate = com.semptian.utils.DateUtil.parse(endDay, com.semptian.utils.DateUtil.YYYY_MM_DD);

        //获取开始日期0点时间戳以及结束日期23点59分59秒时间戳
        long startTime = com.semptian.utils.DateUtil.getDateFirstTimestamp(startDate);
        long endTime = com.semptian.utils.DateUtil.getDateLastTimestamp(endDate);

        QueryWrapper<FixedIpEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ip_address", ip);
        queryWrapper.between("create_time", startTime, endTime);
        queryWrapper.eq("delete_status", 0);

        List<FixedIpEntity> list = this.list(queryWrapper);

        List<RelationUserIpModel> result = new ArrayList<>();

        if (CollUtil.isNotEmpty(list)) {
            //List<FixedIpEntity>转换为List<RelationUserIpModel>
            result = list.stream().map(fixedIpEntity -> {
                RelationUserIpModel relationUserIpModel = new RelationUserIpModel();
                relationUserIpModel.setUserName(fixedIpEntity.getIpName());
                relationUserIpModel.setUserType(USER_TYPE_FIXED_IP);
                relationUserIpModel.setEarliestRelationTime(fixedIpEntity.getCreateTime());

                return relationUserIpModel;
            }).collect(Collectors.toList());
        }

        return ReturnModel.getInstance().ok(result);
    }

}
