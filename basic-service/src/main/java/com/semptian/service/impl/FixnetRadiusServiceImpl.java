package com.semptian.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.semptian.service.FixnetRadiusService;
import com.semptian.service.MobileRadiusService;
import com.semptian.utils.StringUtilNom;
import com.semptian.utils.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/11 10:53
 */
@Slf4j
@Service
public class FixnetRadiusServiceImpl implements FixnetRadiusService {

    @Autowired
    private DorisDatService dorisDatService;

    private final String pageSql = "select DISTINCT account as account from ads_basic_fixnet_radius_info where %s order by account limit %s offset %s ";
    private final String pageCountSql = "select count(DISTINCT account) as cnt from ads_basic_fixnet_radius_info where %s ";

    private final String pageDetailSql = "select account as account,replace(GROUP_CONCAT(mac order by last_time desc),',',';') as mac " +
            "from ads_basic_fixnet_radius_info" +
            " where account in (%s) group by account ";

    private final String ipListCountSql = "SELECT count(1) AS cnt from (select ip,mac,capture_day from dws.dws_element_radius_auth_record where auth_account = '%s' and capture_day >= '%s' and capture_day <= '%s' group by ip,mac,capture_day) tmp";
    private final String ipListSql = "SELECT ip,mac,capture_day as captureDay,max(capture_time) as captureTime from dws.dws_element_radius_auth_record where auth_account = '%s' and capture_day >= '%s' and capture_day <= '%s' group by ip,mac,capture_day order by captureTime desc  limit %s offset %s";

    @Override
    public HashMap<String, Object> queryRadiusList(String keyword, int onPage, int size) {
        HashMap<String, Object> result = Maps.newHashMap();
        int limit = size;
        int offset =(onPage-1) * size;
        String keywordCondition = "1=1";
        if (StringUtils.isNotEmpty(keyword)){
            keywordCondition = String.format("LOWER(account) = LOWER('%s')",keyword);
        }
        String pageSql = String.format(this.pageSql, keywordCondition, limit, offset);
        List<Map<String, Object>> list = dorisDatService.queryForList(pageSql);
        String pageCountSql = String.format(this.pageCountSql, keywordCondition);
        long count = dorisDatService.queryForCount(pageCountSql);

        //补充list信息
        if(CollectionUtil.isNotEmpty(list)){
            List<Object> phoneNumberList = list.stream().map(item -> {
                return "'"+item.get("account")+"'";
            }).collect(Collectors.toList());

            String pageDetailSql = String.format(this.pageDetailSql, StringUtils.join(phoneNumberList, ","));
            Map<String,List<Map<String, Object>>> maps = dorisDatService.queryForList(pageDetailSql).stream().collect(Collectors.groupingBy(map -> (String) map.get("account")));
            list.forEach(item -> {
                String phoneNumber = (String) item.get("account");
                List<Map<String, Object>> detailList = maps.get(phoneNumber);
                if(CollectionUtil.isNotEmpty(detailList)){
                    Map<String, Object> detailMap = detailList.get(0);
                    item.put("mac", StringUtilNom.stringTop(StringUtilNom.distinctString(detailMap.getOrDefault("mac","").toString(),";"),3,";"));
                }else{
                    item.put("mac","");
                }
            });
        }

        if(CollectionUtil.isEmpty(list)){
            list = new ArrayList<>();
        }
        result.put("total",count);
        result.put("records",list);
        return result;
    }

    @Override
    public HashMap<String, Object> authRecord(String account, String startDay, String endDay, String dateOption, int onPage, int size) {
        HashMap<String, Object> result = Maps.newHashMap();
        int limit = size;
        int offset =(onPage-1) * size;

        String pageSql = String.format(this.ipListSql,account,startDay,endDay,limit,offset);
        Callable<List<Map<String, Object>>> listCallable = getListCallable(pageSql);
        List<Map<String, Object>> list = null;
        Future<List<Map<String, Object>>> listFuture = ThreadPoolUtil.getBusinessPoolExecutor().submit(listCallable);

        String pageCountSql =  String.format(this.ipListCountSql,account,startDay,endDay);
        Callable<Long> totalCallable = getTotalCallable(pageCountSql);
        Long count = 0L;
        Future<Long> totalFuture = ThreadPoolUtil.getBusinessPoolExecutor().submit(totalCallable);

        try {
            list = listFuture.get();
            count = totalFuture.get();
        } catch (Exception e) {
            log.error("future error",e);
            throw new RuntimeException(e);
        }

        if(CollectionUtil.isEmpty(list)){
            list = new ArrayList<>();
        }
        result.put("total",count);
        result.put("records",list);
        return result;
    }

    private Callable<List<Map<String, Object>>> getListCallable(String sql){
        Callable<List<Map<String, Object>>> task = new Callable<List<Map<String, Object>>>() {
            @Override
            public List<Map<String, Object>> call() throws Exception {
                return dorisDatService.queryForList(sql);
            }
        };
        return task;
    }

    private Callable<Long> getTotalCallable(String sql){
        Callable<Long> task = new Callable<Long>() {
            @Override
            public Long call() throws Exception {
                return dorisDatService.queryForCount(sql);
            }
        };
        return task;
    }
}
