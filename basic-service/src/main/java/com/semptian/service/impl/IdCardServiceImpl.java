package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.IdCardEntity;
import com.semptian.mapper.IdCardMapper;
import com.semptian.service.IdCardService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class IdCardServiceImpl extends ServiceImpl<IdCardMapper, IdCardEntity> implements IdCardService {
    @Override
    public Long getNextVal() {
        return this.baseMapper.getNextVal();
    }

    @Override
    public void batchInsert(List list) {
        this.baseMapper.batchInsert(list);
    }
}
