package com.semptian.service.impl;


import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.InvalidTelEntity;
import com.semptian.mapper.InvalidTelMapper;
import com.semptian.service.InvalidTelService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class InvalidTelServiceImpl extends ServiceImpl<InvalidTelMapper, InvalidTelEntity> implements InvalidTelService {
    @Override
    public Long getNextVal() {
        return this.baseMapper.getNextVal();
    }

    @Override
    public boolean checkPhoneNumberExist(Long id, String phoneNumber) {
        return this.baseMapper.checkPhoneNumberExist(id,phoneNumber) > 0;
    }

    @Override
    public void batchInsert(List list) {
        this.baseMapper.batchInsert(list);
    }
}
