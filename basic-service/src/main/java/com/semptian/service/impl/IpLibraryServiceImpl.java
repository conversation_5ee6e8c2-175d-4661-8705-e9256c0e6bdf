package com.semptian.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.IpLibraryEntity;
import com.semptian.mapper.IpLibraryMapper;
import com.semptian.service.IpLibraryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class IpLibraryServiceImpl extends ServiceImpl<IpLibraryMapper, IpLibraryEntity> implements IpLibraryService {
    @Override
    public Long getNextVal() {
        return this.baseMapper.getNextVal();
    }

    @Override
    public void batchInsert(List list) {
        this.baseMapper.batchInsert(list);
    }
}
