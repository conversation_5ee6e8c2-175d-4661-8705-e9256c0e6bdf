package com.semptian.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.semptian.service.MobileRadiusService;
import com.semptian.utils.StringUtilNom;
import com.semptian.utils.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/11 10:53
 */
@Slf4j
@Service
public class MobileRadiusServiceImpl implements MobileRadiusService {

    @Autowired
    private DorisDatService dorisDatService;

    private final String pageSql = "select DISTINCT phone_number as phoneNumber from ads_basic_mobile_radius_info where %s order by phone_number limit %s offset %s ";
    private final String pageCountSql = "select count(DISTINCT phone_number) as cnt from ads_basic_mobile_radius_info where %s ";

    private final String pageDetailSql = "select phone_number as phoneNumber,replace(GROUP_CONCAT(imsi order by last_time desc),',',';') as imsi,replace(GROUP_CONCAT(imei order by last_time desc),',',';') as imei " +
            "from ads_basic_mobile_radius_info" +
            " where phone_number  in (%s) group by phone_number ";
    @Override
    public HashMap<String, Object> queryRadiusList(String keyword, int onPage, int size) {
        long s1 = System.currentTimeMillis();
        HashMap<String, Object> result = Maps.newHashMap();
        int limit = size;
        int offset =(onPage-1) * size;
        String keywordCondition = "1=1";
        if (StringUtils.isNotEmpty(keyword)){
            keywordCondition = String.format("LOWER(phone_number) = LOWER('%s')",keyword);
        }
        //优化查询效率，列表和总数同时查询
        String pageSql = String.format(this.pageSql, keywordCondition, limit, offset);
        Callable<List<Map<String, Object>>> listCallable = getListCallable(pageSql);
        List<Map<String, Object>> list = new ArrayList<>();
        Future<List<Map<String, Object>>> listFuture = ThreadPoolUtil.getBusinessPoolExecutor().submit(listCallable);

        String pageCountSql = String.format(this.pageCountSql, keywordCondition);
        Callable<Long> totalCallable = getTotalCallable(pageCountSql);
        Long count = 0L;
        Future<Long> totalFuture = ThreadPoolUtil.getBusinessPoolExecutor().submit(totalCallable);

        try {
            list = listFuture.get();
            count = totalFuture.get();
        } catch (Exception e) {
            log.error("future error",e);
            throw new RuntimeException(e);
        }

        long s2 = System.currentTimeMillis();

        //补充list信息
        if(CollectionUtil.isNotEmpty(list)){
            List<Object> phoneNumberList = list.stream().map(item -> {
                return "'"+item.get("phoneNumber")+"'";
            }).collect(Collectors.toList());

            String pageDetailSql = String.format(this.pageDetailSql, StringUtils.join(phoneNumberList, ","));
            Map<String,List<Map<String, Object>>> maps = dorisDatService.queryForList(pageDetailSql).stream().collect(Collectors.groupingBy(map -> (String) map.get("phoneNumber")));
            list.forEach(item -> {
                String phoneNumber = (String) item.get("phoneNumber");
                List<Map<String, Object>> detailList = maps.get(phoneNumber);
                if(CollectionUtil.isNotEmpty(detailList)){
                    Map<String, Object> detailMap = detailList.get(0);
                    String imsi = detailMap.getOrDefault("imsi","").toString();
                    String imei = detailMap.getOrDefault("imei","").toString();
                    item.put("imsi",StringUtilNom.stringTop(StringUtilNom.distinctString(imsi,";"),3,";"));
                    item.put("imei", StringUtilNom.stringTop(StringUtilNom.distinctString(imei,";"),1,";"));
                }else{
                    item.put("imsi","");
                    item.put("imei","");
                }
            });
        }

        long s3 = System.currentTimeMillis();
        if(CollectionUtil.isEmpty(list)){
            list = new ArrayList<>();
        }
        result.put("total",count);
        result.put("records",list);
        log.info("查询耗时{}，{}",s2-s1,s3-s2);
        return result;
    }

    private Callable<List<Map<String, Object>>> getListCallable(String sql){
        Callable<List<Map<String, Object>>> task = new Callable<List<Map<String, Object>>>() {
            @Override
            public List<Map<String, Object>> call() throws Exception {
                return dorisDatService.queryForList(sql);
            }
        };
        return task;
    }

    private Callable<Long> getTotalCallable(String sql){
        Callable<Long> task = new Callable<Long>() {
            @Override
            public Long call() throws Exception {
                return dorisDatService.queryForCount(sql);
            }
        };
        return task;
    }
}
