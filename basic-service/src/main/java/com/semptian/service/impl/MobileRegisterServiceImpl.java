package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.MobileRegisterEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.MobileRegisterMapper;
import com.semptian.service.MobileRegisterService;
import com.semptian.utils.DateUtil;
import com.semptian.utils.StringUtilNom;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.semptian.constant.CommonConstant.*;

/**
 * @author: ZC
 * @date: 2021/3/12 9:37
 */
@Slf4j
@Service
public class MobileRegisterServiceImpl extends ServiceImpl<MobileRegisterMapper, MobileRegisterEntity> implements MobileRegisterService {

    @Override
    public void batchInsert(List list) {
        this.baseMapper.batchInsert(list);
    }
}
