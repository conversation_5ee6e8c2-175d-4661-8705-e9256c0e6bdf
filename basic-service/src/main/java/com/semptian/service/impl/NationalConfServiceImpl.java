package com.semptian.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.NationalConfigEntity;
import com.semptian.mapper.NationalConfMapper;
import com.semptian.service.NationalConfService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: SunQi
 * @create: 2021/01/08
 * desc:
 **/
@Service
@DS("mysql")
public class NationalConfServiceImpl implements NationalConfService {
    @Autowired
    private NationalConfMapper confMapper;

    @Override
    public ReturnModel getUrlList() {
        QueryWrapper<NationalConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.select("url");
        List<NationalConfigEntity> nationalConfigEntities = confMapper.selectList(wrapper);
        List<String> urlList = nationalConfigEntities.stream().map(NationalConfigEntity::getUrl).collect(Collectors.toList());
        return ReturnModel.getInstance().ok().setData(urlList);
    }

    @Override
    public ReturnModel getKeywordsByUrl(String url) {
        QueryWrapper<NationalConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("url", url);
        List<NationalConfigEntity> confList = confMapper.selectList(wrapper);
        if (confList != null && confList.size() > 0) {
            NationalConfigEntity nationalConfig = confList.get(0);
            String keyword = nationalConfig.getKeyword();
            if (StringUtils.isBlank(keyword)) {
                return new ReturnModel().setData(new ArrayList<>());
            }
            return new ReturnModel().setData(Arrays.asList(keyword.split(",")));
        }
        return new ReturnModel().setData(new ArrayList<>());
    }
}
