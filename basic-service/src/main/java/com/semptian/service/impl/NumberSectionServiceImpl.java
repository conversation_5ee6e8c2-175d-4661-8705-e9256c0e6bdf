package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.NumberSectionEntity;
import com.semptian.mapper.NumberSectionMapper;
import com.semptian.service.NumberSectionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class NumberSectionServiceImpl extends ServiceImpl<NumberSectionMapper, NumberSectionEntity> implements NumberSectionService {
    @Override
    public Long getNextVal() {
        return this.baseMapper.getNextVal();
    }

    @Override
    public boolean checkMsisdnExist(Long id, String msisdn) {
        return this.baseMapper.checkMsisdnExist(id, msisdn) > 0;
    }

    @Override
    public void batchInsert(List list) {
        this.baseMapper.batchInsert(list);
    }
}
