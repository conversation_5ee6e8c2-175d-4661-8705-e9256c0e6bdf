package com.semptian.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.PhoneCodeAreaEntity;
import com.semptian.entity.SpamCallNumberInfoEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.SpamCallNumberTypeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.PhoneCodeAreaMapper;
import com.semptian.mapper.SpamCallNumberInfoMapper;
import com.semptian.param.*;
import com.semptian.service.SpamCallNumberInfoService;
import com.semptian.utils.ExcelUtil;
import com.semptian.vo.PageQueryResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 骚扰号码信息库表 服务实现类
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@Service
@DS("mysql")
public class SpamCallNumberInfoServiceImpl extends ServiceImpl<SpamCallNumberInfoMapper, SpamCallNumberInfoEntity> implements SpamCallNumberInfoService {

    //导入模板号码列所在列
    private static final Integer TEMPLATE_PHONE_NUMBER_COLUMN_INDEX = 0;
    //导入模板号码类型所在列
    private static final Integer TEMPLATE_PHONE_NUMBER_TYPE_COLUMN_INDEX = 2;


    @Resource
    private PhoneCodeAreaMapper phoneCodeAreaMapper;

    @Value("${max.import.rows:10000}")
    private Integer maxImportRows;

    // 编译正则表达式
    private static final Pattern PATTERN_PHONE_NUMBER_REGEX = Pattern.compile("^\\d{4,30}$");

    /**
     * 新增骚扰号码信息
     * @param spamCallNumberInfoModel 骚扰号码信息
     * @return 新增结果
     */
    @Override
    public Object add(SpamCallNumberInfoModel spamCallNumberInfoModel) {
        //判断号码是否存在重复
        Object checkPhoneNumIsExist = checkPhoneNumIsExist(spamCallNumberInfoModel.getPhoneNumber(), null);
        if (checkPhoneNumIsExist != null) {
            return checkPhoneNumIsExist;
        }

        SpamCallNumberInfoEntity spamCallNumberInfoEntity = new SpamCallNumberInfoEntity();
        BeanUtil.copyProperties(spamCallNumberInfoModel, spamCallNumberInfoEntity);

        this.save(spamCallNumberInfoEntity);

        return ReturnModel.getInstance().ok(spamCallNumberInfoModel);
    }

    /**
     * 修改骚扰号码信息
     * @param spamCallNumberInfoModel 骚扰号码信息
     * @return 修改结果
     */
    @Override
    public Object update(SpamCallNumberInfoModel spamCallNumberInfoModel) {
        //判断号码是否存在重复
        Object checkPhoneNumIsExist = checkPhoneNumIsExist(spamCallNumberInfoModel.getPhoneNumber(), spamCallNumberInfoModel.getId());
        if (checkPhoneNumIsExist != null) {
            return checkPhoneNumIsExist;
        }

        SpamCallNumberInfoEntity spamCallNumberInfoEntity = new SpamCallNumberInfoEntity();
        BeanUtil.copyProperties(spamCallNumberInfoModel, spamCallNumberInfoEntity);

        this.updateById(spamCallNumberInfoEntity);

        return ReturnModel.getInstance().ok(spamCallNumberInfoModel);
    }

    /**
     * 批量删除骚扰号码信息
     * @param deleteModel 批量删除参数
     * @return 删除结果
     */
    @Override
    public Object delete(DeleteModel deleteModel) {
        //批量删除骚扰号码信息
        this.lambdaUpdate().set(SpamCallNumberInfoEntity::getIsDel, 1).in(SpamCallNumberInfoEntity::getId, deleteModel.getIds()).update();
        return ReturnModel.getInstance().ok();
    }

    /**
     * 骚扰号码列表分页查询
     * @param spamCallNumberQueryParamModel 骚扰号码查询参数
     * @return 骚扰号码列表
     */
    @Override
    public Object list(SpamCallNumberQueryParamModel spamCallNumberQueryParamModel) {
        QueryWrapper<SpamCallNumberInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);

        //号码模糊查询
        if (StrUtil.isNotBlank(spamCallNumberQueryParamModel.getPhoneNumber())) {
            queryWrapper.like("phone_number", spamCallNumberQueryParamModel.getPhoneNumber());
        }

        //类型查询
        if (ObjectUtil.isNotNull(spamCallNumberQueryParamModel.getType())) {
            queryWrapper.eq("type", spamCallNumberQueryParamModel.getType());
        }

        queryWrapper.orderByDesc("modify_time").orderByAsc("id");

        //分页查询
        IPage<SpamCallNumberInfoEntity> page = new Page<>();
        page.setCurrent(spamCallNumberQueryParamModel.getOnPage());
        page.setSize(spamCallNumberQueryParamModel.getSize());
        page = this.page(page, queryWrapper);

        List<SpamCallNumberInfoModel> list = page.getRecords().stream().map(item -> {
            SpamCallNumberInfoModel model = new SpamCallNumberInfoModel();
            BeanUtil.copyProperties(item, model);
            model.setTypeName(I18nUtils.getMessage("phone.number.type." + item.getType()));
            return model;
        }).collect(Collectors.toList());

        PageQueryResponseVo<SpamCallNumberInfoModel> pageQueryResponseVo = new PageQueryResponseVo<>();
        pageQueryResponseVo.setTotal(page.getTotal());
        pageQueryResponseVo.setList(list);

        return ReturnModel.getInstance().ok(pageQueryResponseVo);
    }

    /**
     * @return 国际区号列表
     */
    @Override
    public Object phoneCodeList() {
        return phoneCodeAreaMapper.selectList().stream().map(PhoneCodeAreaEntity::getPhoneCode).collect(Collectors.toList());
    }

    /**
     * 判断号码是否存在重复
     * @param phoneNum 号码
     * @param id 号码id
     * @return 号码是否存在重复
     */
    private Object checkPhoneNumIsExist(String phoneNum, Integer id) {
        String[] phoneNumberArr = phoneNum.split(",");
        for (String phoneNumber : phoneNumberArr) {
            String name = baseMapper.phoneNumIsExist(phoneNumber, id);
            if (StrUtil.isNotBlank(name)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode())
                        .setMsg(I18nUtils.getMessage("phone.number.is.exist")  + "[" + name + "]" );
            }
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object importBatch(MultipartFile file, Integer userId, Integer type, HttpServletResponse response) {
        List<SpamCallNumberInfoEntity> spamCallNumberInfoEntities = Lists.newArrayList();

        try {
            // 添加一下国际化的导入语言
            ExcelUtil<SpamCallNumberExcelModel> excelUtil = new ExcelUtil<>(SpamCallNumberExcelModel.class);
            List<SpamCallNumberExcelModel> excelModelList = excelUtil.importExcel(file.getInputStream(),type);

            if (CollectionUtils.isEmpty(excelModelList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.isEmpty"));
            }

            if (excelModelList.size() > maxImportRows) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.exceed.max.rows"));
            }

            //校验导入数据是否符合规范
            List<ImportExcelErrorMsgModel> importExcelErrorMsgModelList = validatorDataList(excelModelList);
            if (CollUtil.isNotEmpty(importExcelErrorMsgModelList)) {
                response.setContentType("application/x-download");
                response.setHeader("Content-Disposition", "attachment;filename=" + file.getOriginalFilename());
                try (OutputStream fileOutputStream = response.getOutputStream(); InputStream inputStream = file.getInputStream()) {
                    XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                    XSSFSheet sheet = workbook.getSheetAt(0);

                    //创建绘图对象
                    XSSFDrawing drawing = sheet.createDrawingPatriarch();

                    for (ImportExcelErrorMsgModel importExcelErrorMsgModel : importExcelErrorMsgModelList) {
                        XSSFRow row = sheet.getRow(importExcelErrorMsgModel.getRow());
                        XSSFCell cell = row.getCell(importExcelErrorMsgModel.getColumn());
                        if (ObjectUtil.isNull(cell)) {
                            cell = row.createCell(importExcelErrorMsgModel.getColumn());
                        }

                        XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                        cellComment.setAuthor("CBP");
                        cellComment.setString(new XSSFRichTextString(importExcelErrorMsgModel.getErrorMsg()));
                        cell.setCellComment(cellComment);
                    }

                    workbook.write(fileOutputStream);
                } catch (Exception e) {
                    log.error("response error ", e);
                }

                return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
            }

            //号码去重
            Set<SpamCallNumberExcelModel> spamCallNumberExcelModels = new TreeSet<>(Comparator.comparing(SpamCallNumberExcelModel::getPhoneNumber));
            spamCallNumberExcelModels.addAll(excelModelList);

            for (SpamCallNumberExcelModel spamCallNumberExcelModel : spamCallNumberExcelModels) {

                SpamCallNumberInfoEntity spamCallNumberInfo = new SpamCallNumberInfoEntity();
                spamCallNumberInfo.setPhoneNumber(spamCallNumberExcelModel.getPhoneNumber());
                spamCallNumberInfo.setName(spamCallNumberExcelModel.getName());
                if (StringUtils.isEmpty(spamCallNumberExcelModel.getType())) {
                    spamCallNumberInfo.setType(SpamCallNumberTypeEnum.OTHER.getCode());
                } else {
                    spamCallNumberInfo.setType(Integer.parseInt(spamCallNumberExcelModel.getType()));
                }
                spamCallNumberInfo.setRemark(spamCallNumberExcelModel.getRemark());
                spamCallNumberInfo.setIsDel(0);
                spamCallNumberInfo.setCreateTime(System.currentTimeMillis());
                spamCallNumberInfo.setModifyTime(System.currentTimeMillis());

                spamCallNumberInfoEntities.add(spamCallNumberInfo);
            }

            this.saveBatch(spamCallNumberInfoEntities);

            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.info("import error", e);
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.white.isException"));
        }
    }

    private List<ImportExcelErrorMsgModel> validatorDataList(List<SpamCallNumberExcelModel> excelModelList) {
        List<ImportExcelErrorMsgModel> errorMsgList = new ArrayList<>();

        //获取所有未删除的号码
        List<SpamCallNumberInfoEntity> existNumList = this.lambdaQuery().eq(SpamCallNumberInfoEntity::getIsDel, 0).list();

        List<String> numberList = new ArrayList<>();

        if (CollUtil.isNotEmpty(existNumList)) {
            existNumList.forEach(existNum -> {
                List<String> list = Arrays.asList(existNum.getPhoneNumber().split(","));
                numberList.addAll(list);
            });
        }

        //获取所有的号码分类枚举
        Map<String, Integer> spamCallNumberTypeEnumMap = new HashMap<>();
        for (SpamCallNumberTypeEnum spamCallNumberTypeEnum : SpamCallNumberTypeEnum.values()) {
            String value = I18nUtils.getMessage("phone.number.type." + spamCallNumberTypeEnum.getCode());

            spamCallNumberTypeEnumMap.put(value, spamCallNumberTypeEnum.getCode());
        }

        //校验要点1、号码不能为空;2、号码必须纯数字格式且长度在4-30位;3、号码不能重复（包括库中和导入模板均不能重复）;
        for (int i = 0; i < excelModelList.size(); i++) {
            SpamCallNumberExcelModel spamCallNumberExcelModel = excelModelList.get(i);

            //号码不能为空
            if (StrUtil.isBlank(spamCallNumberExcelModel.getPhoneNumber())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(i + 1).column(TEMPLATE_PHONE_NUMBER_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("phone.number.is.empty")).build());
            }else {

                String[] phoneNumberArr = spamCallNumberExcelModel.getPhoneNumber().split(",");
                if (ArrayUtil.isNotEmpty(phoneNumberArr)) {
                    for (String phoneNumber : phoneNumberArr) {
                        //号码长度必须在4-30位之间且为纯数字
                        if (!PATTERN_PHONE_NUMBER_REGEX.matcher(phoneNumber).matches()) {
                            errorMsgList.add(ImportExcelErrorMsgModel.builder().row(i + 1).column(TEMPLATE_PHONE_NUMBER_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("phone.number.format.error")).build());
                            break;
                        }

                        //号码不能重复
                        if (numberList.contains(phoneNumber)) {
                            errorMsgList.add(ImportExcelErrorMsgModel.builder().row(i + 1).column(TEMPLATE_PHONE_NUMBER_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("phone.number.is.exist")).build());
                            break;
                        }

                        numberList.add(phoneNumber);
                    }
                }
            }

            //号码类型不能为空
            String type = spamCallNumberExcelModel.getType();
            if (StrUtil.isBlank(type)) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(i + 1).column(TEMPLATE_PHONE_NUMBER_TYPE_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("phone.number.type.is.empty")).build());
            }else {
                //号码类型必须符合枚举值
                if (!spamCallNumberTypeEnumMap.containsKey(type)) {
                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(i + 1).column(TEMPLATE_PHONE_NUMBER_TYPE_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("phone.number.type.error")).build());
                }else {
                    spamCallNumberExcelModel.setType(spamCallNumberTypeEnumMap.get(type).toString());
                }
            }
        }

        return errorMsgList;
    }
}
