package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.SpecialNumberEntity;
import com.semptian.enums.NumberStatusEnum;
import com.semptian.mapper.SpecialNumberMapper;
import com.semptian.service.SpecialNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 特殊号码数据库服务提供者
 *
 * @Author: sk
 * @Date: 2020/12/17 14:57
 */

@Service
@Slf4j
public class SpecialNumberImpl extends ServiceImpl<SpecialNumberMapper, SpecialNumberEntity> implements SpecialNumberService {

    @Override
    public int querySpecialNumberCount(String keyword, Long startTime,Long endTime) {
        return baseMapper.querySpecialNumberCount(keyword,startTime,endTime);
    }


    @Override
    public List<ClueDto> querySpecialNumberList(String keyword, int startIndex, int endIndex, String orderField, int orderType, Long startTime,Long endTime) {
        return baseMapper.querySpecialNumberList(keyword, startIndex, endIndex, orderField, orderType, startTime,endTime);
    }

    @Override
    public boolean checkSpecialNumber(String telephoneNum, String countryCode, Long id) {
        return baseMapper.checkSpecialNumber(telephoneNum,countryCode,id) == 0 ? Boolean.FALSE : Boolean.TRUE;
    }

    @Override
    public ClueDto querySpecialNumberDetail(Long id) {
        return baseMapper.querySpecialNumberDetail(id);
    }

    @Override
    public Long getNextVal() {
        return baseMapper.getNextVal();
    }

    @Override
    public boolean updateBatchByStatus(List<Long> specialList, Integer toStatus) {
        return baseMapper.updateBatchByStatus(specialList,toStatus) > 0;
    }

    @Override
    public List<ClueDto> selectOrdinaryExport() {
        return baseMapper.selectOrdinaryExport();
    }

    @Override
    public List<ClueDto> selectOrdinaryByIdsExport(List<Long> specialIds ) {
        return baseMapper.selectOrdinaryByIdsExport(specialIds);
    }

    @Override
    public List<String> getALLOn() {
        QueryWrapper<SpecialNumberEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("PHONESTATE", NumberStatusEnum.ON.getCode());
        return baseMapper.selectList(wrapper).stream().map(SpecialNumberEntity::getPhoneNum).collect(Collectors.toList());
    }


}
