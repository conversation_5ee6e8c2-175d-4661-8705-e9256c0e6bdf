package com.semptian.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.base.service.ReturnModel;
import com.semptian.dto.usercategory.UserCategoryCountDto;
import com.semptian.entity.UserCategoryEntity;
import com.semptian.entity.UserInfoEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.UserCategoryMapper;
import com.semptian.param.DeleteModel;
import com.semptian.param.UserCategoryModel;
import com.semptian.redis.config.RedisConfig;
import com.semptian.redis.template.RedisOps;
import com.semptian.service.UserCategoryService;
import com.semptian.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 上网用户分类 服务实现类
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@Service
@DS("mysql")
public class UserCategoryServiceImpl extends ServiceImpl<UserCategoryMapper, UserCategoryEntity> implements UserCategoryService {

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private RedisOps redisOps;

    @Resource
    private RedisConfig redisConfig;

    @Resource
    private RedisTemplate redisTemplate;

    public static final String USER_CATEGORY_INFO_KEY = "user_category_info_key:";

    public static final String USER_CATEGORY_INFO_NAME = "name";

    public static final String USER_CATEGORY_INFO_PID = "pid";

    public static String getUserCategoryInfoKey(Integer userCategoryId) {
        return USER_CATEGORY_INFO_KEY + userCategoryId;
    }

    @PostConstruct
    public void initUserCategoryCache() {
        QueryWrapper<UserCategoryEntity> categoryQueryWrapper = new QueryWrapper<>();
        categoryQueryWrapper.eq("is_del", 0);

        List<UserCategoryEntity> list = this.list(categoryQueryWrapper);

        if (CollUtil.isNotEmpty(list)) {
            redisOps.set(USER_CATEGORY_INFO_KEY + "all", list.size());
            list.forEach(userCategoryEntity -> {
                redisOps.hset(getUserCategoryInfoKey(userCategoryEntity.getId()), USER_CATEGORY_INFO_NAME, userCategoryEntity.getName());
                redisOps.hset(getUserCategoryInfoKey(userCategoryEntity.getId()), USER_CATEGORY_INFO_PID, userCategoryEntity.getPid());
            });
        }
    }

    /**
     * 清除用户分类缓存
     */
    private void clearUserCategoryCache() {
        try {
            Set<String> keys = redisOps.keys(redisConfig.getCachePrefix() + USER_CATEGORY_INFO_KEY + "*");
            log.info("clearUserCategoryCache, size:{}, keys:{}", keys.size(), keys);

            redisTemplate.delete(keys);
        }catch (Exception e) {
            log.error("clearUserCategoryCache error", e);
        }
    }

    /**
     * 添加用户分类
     * @param userCategoryModel 用户分类
     * @return 用户分类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object add(UserCategoryModel userCategoryModel) {
        //判断同级分类下分类名称是否已存在
        Object object = checkUserCategoryExists(userCategoryModel);
        if (ObjectUtil.isNotNull(object)) {
            return object;
        }

        UserCategoryEntity userCategoryEntity = new UserCategoryEntity();
        BeanUtil.copyProperties(userCategoryModel, userCategoryEntity);

        //判断pid是否为0，如果为0则将level设置为1;不是则根据pid查询上级level并+1
        //pid不为0时，需要将上级的is_last设置为0
        if (userCategoryModel.getPid() == 0) {
            userCategoryEntity.setLevel(1);
        } else {
            UserCategoryEntity parent = this.getById(userCategoryModel.getPid());

            //上级的is_last设置为0
            parent.setIsLast(0);
            this.updateById(parent);

            userCategoryEntity.setLevel(parent.getLevel() + 1);
        }

        //保存用户分类
        this.save(userCategoryEntity);

        //清空上网用户分类缓存
        clearUserCategoryCache();

        BeanUtil.copyProperties(userCategoryEntity, userCategoryModel);
        return ReturnModel.getInstance().ok(userCategoryModel);
    }

    /**
     * 修改用户分类
     * @param userCategoryModel 用户分类
     * @return 用户分类
     */
    @Override
    public Object update(UserCategoryModel userCategoryModel) {
        //判断同级分类下分类名称是否已存在
        Object checkUserCategoryExists = checkUserCategoryExists(userCategoryModel);
        if (ObjectUtil.isNotNull(checkUserCategoryExists)) {
            return checkUserCategoryExists;
        }

        //判断当前pid和数据库中的pid是否一致
        UserCategoryEntity userCategoryEntity = this.getById(userCategoryModel.getId());
        if (!userCategoryEntity.getPid().equals(userCategoryModel.getPid())) {
            //如果修改了pid则需要判断该分类下是否存在未删除的上网用户，如果存在则不允许修改
            Object checkedResult = checkUserCategoryExistsUser(userCategoryModel.getId());
            if (ObjectUtil.isNotNull(checkedResult)) {
                return checkedResult;
            }
        }

        //修改用户分类
        BeanUtil.copyProperties(userCategoryModel, userCategoryEntity);
        this.updateById(userCategoryEntity);

        //清空上网用户分类缓存
        clearUserCategoryCache();
        return ReturnModel.getInstance().ok(userCategoryModel);
    }

    /**
     * 删除用户分类
     * @param deleteModel 删除参数
     * @return 删除用户分类接口
     */
    @Override
    public Object delete(DeleteModel deleteModel) {
        for (Integer id : deleteModel.getIds()) {
            //判断该分类下是否存在未删除的上网用户，如果存在则不允许删除
            Object checkedResult = checkUserCategoryExistsUser(id);
            if (ObjectUtil.isNotNull(checkedResult)) {
                return checkedResult;
            }
        }

        //批量删除用户分类
        this.lambdaUpdate().set(UserCategoryEntity::getIsDel, 1).in(UserCategoryEntity::getId, deleteModel.getIds()).update();

        //清空上网用户分类缓存
        clearUserCategoryCache();
        return ReturnModel.getInstance().ok();
    }

    /**
     * 用户分类树形列表
     *
     * @param name 叶子节点分类名称
     * @return 用户分类树形列表
     */
    @Override
    public Object tree(String name) {
        //查询所有用户分类,name模糊匹配且is_last=1
        QueryWrapper<UserCategoryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);

        List<UserCategoryEntity> list = this.list(queryWrapper);

        //按照name模糊匹配,返回匹配到节点的所有父节点
        if (StrUtil.isNotEmpty(name)) {
            list = this.findParentCategories(list, name);
        }

        Map<Integer, Integer> allUserCategoryMap = new HashMap<>(16);
        List<UserCategoryCountDto> allUserCategoryList = userInfoService.getAllUserCategoryList();
        if (CollUtil.isNotEmpty(allUserCategoryList)) {
            allUserCategoryList.forEach(dto -> allUserCategoryMap.put(dto.getUserCategoryId(), dto.getNum()));
        }

        List<UserCategoryModel> result = list.stream().map(userCategoryEntity -> {
            UserCategoryModel userCategoryModel = new UserCategoryModel();

            BeanUtil.copyProperties(userCategoryEntity, userCategoryModel);
            Integer num = allUserCategoryMap.getOrDefault(userCategoryEntity.getId(), 0);
            userCategoryModel.setNum(num);
            return userCategoryModel;
        }).collect(Collectors.toList());

        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 判断同级分类下分类名称是否已存在
     * @param userCategoryModel 用户分类
     * @return 返回提示已存在
     */
    private Object checkUserCategoryExists(UserCategoryModel userCategoryModel) {
        //判断同级分类下分类名称是否已存在未删除的用户分类，如果存在则返回提示已存在
        List<UserCategoryEntity> list = this.lambdaQuery().eq(UserCategoryEntity::getPid, userCategoryModel.getPid())
                .eq(UserCategoryEntity::getName, userCategoryModel.getName())
                .eq(UserCategoryEntity::getIsDel, 0).list();

        if (ObjectUtil.isNotNull(userCategoryModel.getId())) {
            list = list.stream().filter(userCategoryEntity -> !userCategoryEntity.getId().equals(userCategoryModel.getId())).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(list)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("user.category.exists"));
        }

        return null;
    }

    /**
     * 判断该分类下是否存在未删除的上网用户
     * @param id 用户分类id
     * @return 返回提示已存在
     */
    private Object checkUserCategoryExistsUser(Integer id) {
        if (userInfoService.lambdaQuery().eq(UserInfoEntity::getUserCategoryId, id).eq(UserInfoEntity::getIsDel, 0).count() > 0) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("user.category.exists.user"));
        }

        return null;
    }

    @Override
    public List<String> getUserCategoryNameList(UserInfoEntity userInfoEntity) {
        return this.getUserCategoryNameList(userInfoEntity, false);
    }

    @Override
    public List<String> getUserCategoryNameList(UserInfoEntity userInfoEntity, boolean onlyShowCurrentNode) {
        List<String> userCategoryNameList = new ArrayList<>();

        if (ObjectUtil.isNull(userInfoEntity)) {
            return userCategoryNameList;
        }

        if (!redisOps.hasKey(USER_CATEGORY_INFO_KEY + "all")) {
            initUserCategoryCache();
        }

        //根据userCategoryId，递归查询出所有的userCategoryEntity的pid，直到不为0
        Integer userCategoryId = userInfoEntity.getUserCategoryId();
        while (userCategoryId != 0) {
            Object nameValue = redisOps.hget(getUserCategoryInfoKey(userCategoryId), USER_CATEGORY_INFO_NAME);

            Object pidValue = redisOps.hget(getUserCategoryInfoKey(userCategoryId), USER_CATEGORY_INFO_PID);

            // 如果无法找到对应的父级节点，或者已经到达根节点，退出循环
            if (null == pidValue) {
                break;
            }

            // 更新userCategoryId为父级节点的ID
            userCategoryId = Integer.parseInt(pidValue.toString());

            if (ObjectUtil.isNotNull(nameValue) && StrUtil.isNotEmpty(nameValue.toString())) {
                userCategoryNameList.add(nameValue.toString());
            }

            if (onlyShowCurrentNode) {
                break;
            }
        }
        Collections.reverse(userCategoryNameList);
        return userCategoryNameList;
    }

    @Override
    public List<UserCategoryEntity> getAllLastUserCategoryList() {
        return this.baseMapper.getAllLastUserCategoryList();
    }

    public List<UserCategoryEntity> findParentCategories(List<UserCategoryEntity> userCategories, String name) {
        List<UserCategoryEntity> result = new ArrayList<>();
        for (UserCategoryEntity category : userCategories) {
            if (category.getName().contains(name)) {
                // 匹配到节点，递归查找其所有上级节点
                findParentCategories(userCategories, category, result);
            }
        }

        //对所有结果去重排序
        result = result.stream()
                .collect(Collectors.toMap(UserCategoryEntity::getId, userCategory -> userCategory, (existing, replacement) -> existing))
                .values().stream().sorted(Comparator.comparing(UserCategoryEntity::getId)).collect(Collectors.toList());

        return result;
    }

    private void findParentCategories(List<UserCategoryEntity> userCategories, UserCategoryEntity category, List<UserCategoryEntity> result) {
        // 添加当前节点
        result.add(category);
        // 查找父节点
        for (UserCategoryEntity parent : userCategories) {
            if (parent.getId().equals(category.getPid())) {
                // 递归查找父节点的父节点
                findParentCategories(userCategories, parent, result);
                break;
            }
        }
    }
}
