package com.semptian.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.dto.usercategory.UserCategoryCountDto;
import com.semptian.entity.FixedIpEntity;
import com.semptian.entity.UserCategoryEntity;
import com.semptian.entity.UserInfoEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.UserTypeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.UserInfoMapper;
import com.semptian.param.*;
import com.semptian.service.FixedIpService;
import com.semptian.service.UserCategoryService;
import com.semptian.service.UserInfoService;
import com.semptian.utils.ExcelUtil;
import com.semptian.utils.IPv6ParseUtil;
import com.semptian.vo.PageQueryResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 上网用户信息库表 服务实现类
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@Service
@DS("mysql")
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfoEntity> implements UserInfoService {

    //导入模板上网用户账号列所在列
    private static final Integer TEMPLATE_USER_NAME_COLUMN_INDEX = 0;
    //导入模板上网用户类型所在列
    private static final Integer TEMPLATE_USER_TYPE_COLUMN_INDEX = 1;
    //导入模板备注名称所在列
    private static final Integer TEMPLATE_REMARK_COLUMN_INDEX = 2;
    //导入模板所属分类所在列
    private static final Integer TEMPLATE_USER_CATEGORY_ID_COLUMN_INDEX = 3;

    private static final Pattern PATTERN_IPV4_REGEX = Pattern.compile("^((\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])\\.){3}(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\/(0|[1-9]|1[0-9]|2[0-9]|3[0-2]))?$");
    private static final Pattern PATTERN_IPV6_REGEX = Pattern.compile("^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?(\\/(0|[1-9]|[1-9][0-9]|1[0-2][0-8]))?$");

    //移动网radius账号正则表达式
    private static final Pattern PATTERN_MOBILE_RADIUS_REGEX = Pattern.compile("^\\d{4,30}$");

    @Resource
    private UserCategoryService userCategoryService;

    @Resource
    private FixedIpService fixedIpService;

    @Value("${max.import.rows:10000}")
    private Integer maxImportRows;

    /**
     * 新增上网用户信息
     * @param userInfoModel 上网用户信息
     * @return 上网用户信息
     */
    @Override
    public Object add(UserInfoModel userInfoModel) {
        Object checkUserRepeat = checkUserRepeat(userInfoModel);
        if (ObjectUtil.isNotNull(checkUserRepeat)) {
            return checkUserRepeat;
        }

        //校验在用户分类下是否存在相同的备注
        Object checkUserCategoryRemark = checkUserCategoryRemark(userInfoModel);
        if (ObjectUtil.isNotNull(checkUserCategoryRemark)) {
            return checkUserCategoryRemark;
        }

        //新增上网用户信息
        UserInfoEntity userInfoEntity = new UserInfoEntity();
        BeanUtil.copyProperties(userInfoModel, userInfoEntity);
        this.save(userInfoEntity);

        BeanUtil.copyProperties(userInfoEntity, userInfoModel);
        return ReturnModel.getInstance().ok(userInfoModel);
    }

    /**
     * @param userInfoModel 用户信息
     * @return 校验信息
     */
    public Object checkUserRepeat(UserInfoModel userInfoModel) {
        if (!UserTypeEnum.CUSTOM.getCode().equals(userInfoModel.getUserType())) {
            //用户类型userType为非自定义时，需要强校验是否已存在未删除的上网用户
            List<UserInfoEntity> entities = this.list(new QueryWrapper<UserInfoEntity>().eq("user_name", userInfoModel.getUserName()).eq("user_type", userInfoModel.getUserType()).eq("is_del", 0));

            //获取已存在上网用户用逗号拼接
            StringBuilder userName = new StringBuilder();
            if (CollUtil.isNotEmpty(entities)) {
                entities.forEach(userInfoEntity -> userName.append(userInfoEntity.getUserName()).append(","));

                userName.delete(userName.length() - 1, userName.length());
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode())
                        .setMsg(I18nUtils.getMessage("user.name.exists") + "[" + userName + "]");
            }

            //固定IP类型账号需要先在固定IP库中建档
            if (UserTypeEnum.FIXED_IP.getCode().equals(userInfoModel.getUserType())) {
                String username = userInfoModel.getUserName();
                if (PATTERN_IPV6_REGEX.matcher(username).matches() && !username.contains("/")) {
                    username = IPv6ParseUtil.getShortIPv6(userInfoModel.getUserName());
                }

                List<FixedIpEntity> ipAddressList = fixedIpService.list(new QueryWrapper<FixedIpEntity>().eq("ip_address", username).eq("delete_status", 0));

                if (CollUtil.isEmpty(ipAddressList)) {
                    return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("user.ip.not.in.fixed.ip"));
                }
            }
        } else {
            //TODO 1.自定义用户时需要判断输入的IP地址，是否存在关联的固网radius和移动网radius账号。当前无法获取IP与固网/移动网radius账号关联。后续实现
            //1.判断用户输入的自定义IP是否为固网radius账号或移动网radius账号

            //2.判断用户输入的自定义IP是否为固定IP库中IP地址
            List<FixedIpEntity> ipAddressList = fixedIpService.list(new QueryWrapper<FixedIpEntity>().eq("ip_address", userInfoModel.getUserName()).eq("delete_status", 0));
            if (CollUtil.isNotEmpty(ipAddressList)) {
                String ipName = ipAddressList.get(0).getIpName();
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode())
                        .setMsg(I18nUtils.getMessage("user.ip.belong.fixed.ip") + "[" + ipName + "]");
            }
        }

        return null;
    }

    /**
     * 修改上网用户信息
     * @param userInfoModel 上网用户信息
     * @return  上网用户信息
     */
    @Override
    public Object update(UserInfoModel userInfoModel) {
        //上网用户备注信息和所属分类支持修改

        //校验在用户分类下是否存在相同的备注
        Object checkUserCategoryRemark = checkUserCategoryRemark(userInfoModel);
        if (ObjectUtil.isNotNull(checkUserCategoryRemark)) {
            return checkUserCategoryRemark;
        }

        //修改上网用户信息
        UserInfoEntity userInfoEntity = new UserInfoEntity();
        userInfoEntity.setId(userInfoModel.getId());
        userInfoEntity.setRemark(userInfoModel.getRemark());
        userInfoEntity.setUserCategoryId(userInfoModel.getUserCategoryId());
        this.updateById(userInfoEntity);

        return ReturnModel.getInstance().ok();
    }

    /**
     * 批量删除上网用户信息
     * @param deleteModel 删除参数
     * @return 删除结果
     */
    @Override
    public Object delete(DeleteModel deleteModel) {
        //批量删除上网用户信息
        this.lambdaUpdate().set(UserInfoEntity::getIsDel, 1).in(UserInfoEntity::getId, deleteModel.getIds()).update();

        return ReturnModel.getInstance().ok();
    }

    /**
     * 批量更新上网用户状态
     * @param batchUpdateStatusModel 批量更新状态参数
     * @return 更新结果
     */
    @Override
    public Object status(BatchUpdateStatusModel batchUpdateStatusModel) {
        //批量更新上网用户状态
        this.lambdaUpdate().set(UserInfoEntity::getStatus, batchUpdateStatusModel.getStatus()).in(UserInfoEntity::getId, batchUpdateStatusModel.getIds()).update();

        return ReturnModel.getInstance().ok();
    }

    /**
     * 分页查询上网用户信息
     * @param userInfoQueryModel  查询参数
     * @return 分页查询结果
     */
    @Override
    public Object list(UserInfoQueryParamModel userInfoQueryModel) {
        QueryWrapper<UserInfoEntity> queryWrapper = getUserInfoQueryWrapper(userInfoQueryModel);

        //分页查询
        Page<UserInfoEntity> page = new Page<>();
        page.setCurrent(userInfoQueryModel.getOnPage());
        page.setSize(userInfoQueryModel.getSize());

        page= this.page(page, queryWrapper);

        List<UserInfoModel> list = page.getRecords().stream().map(userInfoEntity -> {
            UserInfoModel userInfoModel = new UserInfoModel();
            BeanUtil.copyProperties(userInfoEntity, userInfoModel);

            userInfoModel.setUserTypeName(UserTypeEnum.getDescByCode(userInfoEntity.getUserType()));
            List<String> userCategoryNameList = userCategoryService.getUserCategoryNameList(userInfoEntity, true);
            if (CollUtil.isNotEmpty(userCategoryNameList)) {
                userInfoModel.setUserCategoryName(userCategoryNameList.get(0));
            }

            return userInfoModel;
        }).collect(Collectors.toList());

        //返回分页查询结果
        PageQueryResponseVo<UserInfoModel> userInfoQueryResponseVo = new PageQueryResponseVo<>();
        userInfoQueryResponseVo.setTotal(page.getTotal());
        userInfoQueryResponseVo.setList(list);

        return ReturnModel.getInstance().ok(userInfoQueryResponseVo);
    }

    /**
     * 根据档案账号和档案类型查询是否为重点目标
     *
     * @param arcType    档案类型
     * @param arcAccount 档案账号
     * @return 是否为重点目标
     */
    @Override
    public Object isImportantTarget(Integer arcType, String arcAccount) {

        //根据档案账号和档案类型查询是否为重点目标
        QueryWrapper<UserInfoEntity> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("user_name", arcAccount);
        queryWrapper.eq("is_del", 0);

        switch (arcType) {
            case 1:
                queryWrapper.eq("user_type", UserTypeEnum.FIXED_LINE_RADIUS.getCode());
                break;
            case 5:
                queryWrapper.eq("user_type", UserTypeEnum.MOBILE_RADIUS.getCode());
                break;
            case 8:
                queryWrapper.in("user_type", UserTypeEnum.FIXED_IP.getCode(), UserTypeEnum.CUSTOM.getCode());
                break;
        }

        UserInfoEntity userInfoEntity = this.getOne(queryWrapper);

        if (ObjectUtil.isNotNull(userInfoEntity)) {
            Map<String, Object> resultMap = new HashMap<>();

            resultMap.put("userName", StrUtil.isNotBlank(userInfoEntity.getRemark()) ? userInfoEntity.getRemark() : "");


            List<String> userCategoryNameList = userCategoryService.getUserCategoryNameList(userInfoEntity);
            resultMap.put("userCategoryName", userCategoryNameList);
            return resultMap;
        }

        return null;
    }

    /**
     * 根据重要目标和重要目标获取重要目标分类
     * @param importantTargetList 重要目标和重要目标
     * @return 重要目标分类信息
     */
    @Override
    public Object getImportantTargetCategoryByBatch(List<ImportantTargetModel> importantTargetList) {
        List<ImportantTargetModel> result = new ArrayList<>();

        if (CollUtil.isEmpty(importantTargetList)) {
            return result;
        }

        //importantTargetList转为List<UserInfoEntity>
        List<UserInfoEntity> userInfoEntityList = importantTargetList.stream().filter(importantTargetModel -> NumberUtil.isInteger(importantTargetModel.getImportantTargetType())).map(importantTargetModel -> {
            UserInfoEntity userInfoEntity = new UserInfoEntity();
            userInfoEntity.setUserName(importantTargetModel.getImportantTarget());
            userInfoEntity.setUserType(Integer.valueOf(importantTargetModel.getImportantTargetType()));
            return userInfoEntity;
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(userInfoEntityList)) {
            return result;
        }

        List<UserInfoEntity> list = this.getBaseMapper().getImportantTargetCategoryByBatch(userInfoEntityList);

        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(userInfoEntity -> {
                ImportantTargetModel importantTargetModel = new ImportantTargetModel();
                importantTargetModel.setImportantTarget(userInfoEntity.getUserName());
                importantTargetModel.setImportantTargetType(userInfoEntity.getUserType().toString());

                List<String> userCategoryNameList = userCategoryService.getUserCategoryNameList(userInfoEntity, false);
                if (CollUtil.isNotEmpty(userCategoryNameList)) {
                    importantTargetModel.setImportantTargetCategory(userCategoryNameList.get(userCategoryNameList.size() - 1));
                    importantTargetModel.setImportantTargetCategoryList(userCategoryNameList);
                }

                return importantTargetModel;
            }).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * @return 所有用户分类
     */
    @Override
    public List<UserCategoryCountDto> getAllUserCategoryList() {
        return this.baseMapper.getAllUserCategoryList();
    }

    /**
     * 模糊查询上网用户信息
     * @param keyword 模糊查询关键字
     * @return 用户信息
     */
    @Override
    public Object getUserInfoByKeyword(String keyword) {
        List<UserInfoModel> resultList = new ArrayList<>();

        QueryWrapper<UserInfoEntity> queryWrapper = new QueryWrapper<>();

        queryWrapper.likeRight("user_name", keyword);
        queryWrapper.eq("is_del", 0);
        queryWrapper.last("ORDER BY user_name LIMIT 10");

        List<UserInfoEntity> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            resultList = list.stream().map(userInfoEntity -> {
                UserInfoModel userInfoModel = new UserInfoModel();
                BeanUtil.copyProperties(userInfoEntity, userInfoModel);
                return userInfoModel;
            }).collect(Collectors.toList());
        }

        return resultList;
    }

    /**
     * 校验在用户分类下是否存在相同的备注
     * @param userInfoModel 用户信息
     * @return 返回提示已存在
     */
    private Object checkUserCategoryRemark(UserInfoModel userInfoModel) {
        List<UserInfoEntity> entities = this.lambdaQuery().eq(UserInfoEntity::getUserCategoryId, userInfoModel.getUserCategoryId())
                .eq(UserInfoEntity::getRemark, userInfoModel.getRemark()).eq(UserInfoEntity::getIsDel, 0).list();

        if(ObjectUtil.isNotNull(userInfoModel.getId())) {
            entities = entities.stream().filter(userInfoEntity -> !userInfoEntity.getId().equals(userInfoModel.getId())).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(entities)) {
            //同一用户分类下已存在相同的备注名称，请重新输入
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode())
                    .setMsg(I18nUtils.getMessage("user.remark.exists"));
        }

        return null;
    }

    private QueryWrapper<UserInfoEntity> getUserInfoQueryWrapper(UserInfoQueryParamModel userInfoQueryModel) {
        QueryWrapper<UserInfoEntity> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("is_del", 0);

        //上网用户名称或备注名称模糊查询
        if (StrUtil.isNotBlank(userInfoQueryModel.getName())) {
            queryWrapper.and(wrapper -> wrapper.like("user_name", userInfoQueryModel.getName()).or().like("remark", userInfoQueryModel.getName()));
        }

        //用户类型查询
        if (ObjectUtil.isNotNull(userInfoQueryModel.getUserType()) && 0 != userInfoQueryModel.getUserType()) {
            queryWrapper.eq("user_type", userInfoQueryModel.getUserType());
        }

        //启用/停用状态查询
        if (ObjectUtil.isNotNull(userInfoQueryModel.getStatus()) && 0 != userInfoQueryModel.getStatus()) {
            queryWrapper.eq("status", userInfoQueryModel.getStatus());
        }

        //用户分类查询
        if (ObjectUtil.isNotNull(userInfoQueryModel.getUserCategoryId()) && 0 != userInfoQueryModel.getUserCategoryId()) {
            queryWrapper.eq("user_category_id", userInfoQueryModel.getUserCategoryId());
        }
        return queryWrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object importBatch(MultipartFile file, Integer userId, Integer type, HttpServletResponse response) {
        List<UserInfoEntity> userInfoEntities = Lists.newArrayList();

        try {
            ExcelUtil<UserInfoExcelModel> excelUtil = new ExcelUtil<>(UserInfoExcelModel.class);
            List<UserInfoExcelModel> excelModelList = excelUtil.importExcel(file.getInputStream(),type);
            if (CollectionUtils.isEmpty(excelModelList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.isEmpty"));
            }

            if (excelModelList.size() > maxImportRows) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.exceed.max.rows"));
            }

            //校验导入数据是否符合规范
            List<ImportExcelErrorMsgModel> importExcelErrorMsgModelList = validatorDataList(excelModelList);

            if (CollUtil.isNotEmpty(importExcelErrorMsgModelList)) {
                response.setContentType("application/x-download");
                response.setHeader("Content-Disposition", "attachment;filename=" + file.getOriginalFilename());
                try (OutputStream fileOutputStream = response.getOutputStream(); InputStream inputStream = file.getInputStream()) {
                    XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                    XSSFSheet sheet = workbook.getSheetAt(0);

                    //创建绘图对象
                    XSSFDrawing drawing = sheet.createDrawingPatriarch();

                    for (ImportExcelErrorMsgModel importExcelErrorMsgModel : importExcelErrorMsgModelList) {
                        XSSFRow row = sheet.getRow(importExcelErrorMsgModel.getRow());
                        XSSFCell cell = row.getCell(importExcelErrorMsgModel.getColumn());
                        if (ObjectUtil.isNull(cell)) {
                            cell = row.createCell(importExcelErrorMsgModel.getColumn());
                        }

                        XSSFComment cellComment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 10));
                        cellComment.setAuthor("CBP");
                        cellComment.setString(new XSSFRichTextString(importExcelErrorMsgModel.getErrorMsg()));
                        cell.setCellComment(cellComment);
                    }

                    workbook.write(fileOutputStream);
                } catch (Exception e) {
                    log.error("response error ", e);
                }

                return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
            }

            for (UserInfoExcelModel userInfoExcelModel : excelModelList) {

                UserInfoEntity userInfoEntity = new UserInfoEntity();
                userInfoEntity.setUserName(userInfoExcelModel.getUserName());
                userInfoEntity.setUserType(Integer.valueOf(userInfoExcelModel.getUserType()));
                userInfoEntity.setRemark(userInfoExcelModel.getRemark());

                userInfoEntity.setUserCategoryId(Integer.valueOf(userInfoExcelModel.getUserCategoryId()));
                userInfoEntity.setIsDel(0);
                userInfoEntity.setStatus(1);
                userInfoEntity.setModifyTime(System.currentTimeMillis());
                userInfoEntity.setCreateTime(System.currentTimeMillis());

                userInfoEntities.add(userInfoEntity);
            }

            //插入上网用户
            this.saveBatch(userInfoEntities);

            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.info("import error ", e);
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.white.isException"));
        }
    }

    private List<ImportExcelErrorMsgModel> validatorDataList(List<UserInfoExcelModel> excelModelList) {
        //上网用户类型不能为空且枚举值符合预期
        //校验要点1.上网用户账号不能为空2.非自定义时上网用户必须唯一3.自定义用户为ip时判断在固定IP库中是否存在
        //备注名称在同一上网用户分类下必须唯一
        //所属分类不能为空且必须是最末级，并且可根据所属分类名称查询出唯一的所属分类ID(否则返回提示填写完整分类路径名称,用/拼接)
        //匹配所属分类时优先按全路径进行匹配，匹配不到再按照叶子节点名称匹配
        List<ImportExcelErrorMsgModel> errorMsgList = new ArrayList<>();

        //获取所有上网用户类型
        Map<String, Integer> userTypeEnumMap = new HashMap<>();
        for (UserTypeEnum userTypeEnum : UserTypeEnum.values()) {
            String value = I18nUtils.getMessage("user.type." + userTypeEnum.getCode());

            userTypeEnumMap.put(value, userTypeEnum.getCode());
        }

        //获取所有的上网用户信息
        List<UserInfoEntity> userInfoEntityList = this.lambdaQuery().eq(UserInfoEntity::getIsDel, 0).list();
        List<String> userNameList = userInfoEntityList.stream().map(UserInfoEntity::getUserName).collect(Collectors.toList());
        //获取所有备注名称和对应的所属分类
        List<String> allRemarkAndUserCategoryList = userInfoEntityList.stream().filter(userInfoEntity -> StrUtil.isNotBlank(userInfoEntity.getRemark())).map(userInfoEntity -> userInfoEntity.getUserCategoryId() + "," + userInfoEntity.getRemark()).collect(Collectors.toList());

        //获取所有的上网用户分类叶子节点(根据名称作为key，可能会存在叶子节点名称重复的情况)
        Map<String, List<UserCategoryEntity>> userCategoryMap = new HashMap<>();
        //获取所有上网用户分类叶子节点(根据全路径作为key，用/拼接)
        Map<String, Integer> fullPathuserCategoryMap = new HashMap<>();

        List<UserCategoryEntity> userCategoryEntityList = userCategoryService.getAllLastUserCategoryList();
        if (CollUtil.isNotEmpty(userCategoryEntityList)) {
            for (UserCategoryEntity userCategoryEntity : userCategoryEntityList) {
                if (userCategoryMap.containsKey(userCategoryEntity.getName())) {
                    userCategoryMap.get(userCategoryEntity.getName()).add(userCategoryEntity);
                }else {
                    userCategoryMap.put(userCategoryEntity.getName(), Lists.newArrayList(userCategoryEntity));
                }

                UserInfoEntity userInfoEntity = new UserInfoEntity();
                userInfoEntity.setUserCategoryId(userCategoryEntity.getId());
                List<String> userCategoryNameList = userCategoryService.getUserCategoryNameList(userInfoEntity, false);

                String userCategoryName = String.join("/", userCategoryNameList);
                fullPathuserCategoryMap.put(userCategoryName, userCategoryEntity.getId());
            }
        }

        //获取所有的固定IP
        List<FixedIpEntity> fixedIpEntityList = fixedIpService.list(new QueryWrapper<FixedIpEntity>().eq("delete_status", 0));
        List<String> fixedIpAddressList = fixedIpEntityList.stream().map(FixedIpEntity::getIpAddress).collect(Collectors.toList());

        for (int i = 0; i < excelModelList.size(); i++) {
            int row = i + 1;

            UserInfoExcelModel userInfoExcelModel = excelModelList.get(i);

            Integer userType = null;

            if (StrUtil.isBlank(userInfoExcelModel.getUserType())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_TYPE_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.type.is.empty")).build());
            } else if (!userTypeEnumMap.containsKey(userInfoExcelModel.getUserType())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_TYPE_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.type.is.error")).build());
            } else {
                userType = userTypeEnumMap.get(userInfoExcelModel.getUserType());
                userInfoExcelModel.setUserType(userType.toString());
            }

            if (StrUtil.isBlank(userInfoExcelModel.getUserName())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_NAME_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.name.is.empty")).build());
            }else {
                if (userNameList.contains(userInfoExcelModel.getUserName())) {
                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_NAME_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.name.exists")).build());
                }else {
                    String userName = userInfoExcelModel.getUserName();
                    userNameList.add(userName);

                    boolean ipCheck = true;
                    if ((UserTypeEnum.CUSTOM.getCode().equals(userType) || UserTypeEnum.FIXED_IP.getCode().equals(userType))) {
                        //校验是否符合ipv4或ipv6格式
                        if (!PATTERN_IPV4_REGEX.matcher(userName).matches() && !PATTERN_IPV6_REGEX.matcher(userName).matches()) {
                            errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_NAME_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.name.format.error")).build());

                            ipCheck = false;
                        }
                    }

                    if (UserTypeEnum.MOBILE_RADIUS.getCode().equals(userType)) {
                        //校验是否为纯数字且长度在4-30位
                        if (!PATTERN_MOBILE_RADIUS_REGEX.matcher(userInfoExcelModel.getUserName()).matches()) {
                            errorMsgList.add(ImportExcelErrorMsgModel.builder().row(i + 1).column(TEMPLATE_USER_NAME_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.name.format.error")).build());
                        }
                    }

                    if (UserTypeEnum.FIXED_IP.getCode().equals(userType) && ipCheck) {
                        if (PATTERN_IPV6_REGEX.matcher(userName).matches() && !userName.contains("/")) {
                            userName = IPv6ParseUtil.getShortIPv6(userName);

                            userNameList.add(userName);
                        }

                        if (!fixedIpAddressList.contains(userName)) {
                            errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_NAME_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.ip.not.in.fixed.ip")).build());
                        }
                    }
                }
            }

            if (StrUtil.isBlank(userInfoExcelModel.getUserCategoryId())) {
                errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_CATEGORY_ID_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.category.is.empty")).build());
            }else {
                //根据叶子节点名称或者全路径名称获取，都获取不到则校验失败
                if (!userCategoryMap.containsKey(userInfoExcelModel.getUserCategoryId()) && !fullPathuserCategoryMap.containsKey(userInfoExcelModel.getUserCategoryId())) {
                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_CATEGORY_ID_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.category.not.exists")).build());
                }

                boolean userCategoryValid = false;
                if (fullPathuserCategoryMap.containsKey(userInfoExcelModel.getUserCategoryId())) {
                    //优先按照全路径分类名称进行匹配

                    userCategoryValid = true;
                    userInfoExcelModel.setUserCategoryId(fullPathuserCategoryMap.get(userInfoExcelModel.getUserCategoryId()).toString());
                }else if (CollUtil.isNotEmpty(userCategoryMap.get(userInfoExcelModel.getUserCategoryId())) && userCategoryMap.get(userInfoExcelModel.getUserCategoryId()).size() == 1) {
                    //全路径分类名称匹配不到则按照叶子节点名称进行匹配

                    userCategoryValid = true;
                    userInfoExcelModel.setUserCategoryId(userCategoryMap.get(userInfoExcelModel.getUserCategoryId()).get(0).getId().toString());
                }else {
                    //存在相同名称的叶子节点，需要填写全路径节点名称

                    errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_USER_CATEGORY_ID_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.category.name.is.duplicate")).build());
                }

                //相同所属分类下备注名称不能重复
                if (userCategoryValid && StrUtil.isNotBlank(userInfoExcelModel.getRemark())) {
                    String val = userInfoExcelModel.getUserCategoryId() + "," + userInfoExcelModel.getRemark();

                    if (allRemarkAndUserCategoryList.contains(val)) {
                        errorMsgList.add(ImportExcelErrorMsgModel.builder().row(row).column(TEMPLATE_REMARK_COLUMN_INDEX).errorMsg(I18nUtils.getMessage("user.remark.exists")).build());
                    } else {
                        allRemarkAndUserCategoryList.add(val);
                    }
                }
            }
        }

        return errorMsgList;
    }
}