package com.semptian.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.constant.ExportConstant;
import com.semptian.entity.*;
import com.semptian.enums.*;
import com.semptian.external.YJApiService;
import com.semptian.i18n.I18nUtils;
import com.semptian.mapper.WhiteListMapper;
import com.semptian.model.ExportModel;
import com.semptian.model.ExportWhiteListModel;
import com.semptian.param.*;
import com.semptian.service.WhiteListService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 白名单service impl
 * @date 2024/9/14
 */
@Service
@Transactional(rollbackFor = Exception.class)
@DS("mysql")
@Slf4j
public class WhiteListServiceImpl extends ServiceImpl<WhiteListMapper, WhiteListEntity> implements WhiteListService {
    /**
     * 导出选择所有时，ids为-1
     */
    private static final String ALL_DATA_IDS = "-1";

    private static final String FILE_NAME = "white_list_";

    @Autowired
    private YJApiService yjApiService;

    @Autowired
    private RetryTemplate retryTemplate;

    @Override
    public ReturnModel addOrUpdate(WhiteListModel param) {
        WhiteListEntity entity = param.toEntity();

        //1. 参数校验
        String errorMsg = addOrUpdateValidate(entity, null);
        if (StringUtils.isNotEmpty(errorMsg)){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg(errorMsg);
        }

        //2. 重复内容校验
        WhiteListEntity sameEntity = getSame(entity);
        if (sameEntity.getId() != null){
            return ReturnModel.getInstance().error()
                    .setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode())
                    .setMsg(I18nUtils.getMessage("white.list.rule.already.exists"))
                    .setData(sameEntity.getId());
        }

        //3. 新增时，校验白名单上限
        if(param.getId() == null && isGTMaxCount(entity.getType(), 1)){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("white.list.rule.num.limit"));
        }

        boolean flag = this.saveOrUpdate(entity);
        // 只有下发给YJ的线索状态才会是在控中，其它都为在控
        boolean isIssuedYJ = WhiteListStatusEnum.TO_BE_START.getCode().equals(entity.getStatus());
        if (flag && isIssuedYJ){
            // 如果保存数据库成功,且状态为在控中，则需要下发给YJ
            ThreadPoolUtil.getDefaultExecutorInstance().submit(() -> {
                // 异步执行
                issuedWhiteListToYJ(Arrays.asList(entity), entity.getStatus());
            });
        }

        return ReturnModel.getInstance().ok();
    }

    @Override
    public ReturnModel updateStatus(List<Long> ids, Integer status) {
        List<WhiteListEntity> updateLists = new ArrayList<>();
        for (Long id : ids){
            WhiteListEntity entity = this.getById(id);
            if (entity == null){
                continue;
            }

            WhiteListStatusEnum statusEnum = WhiteListStatusEnum.getByCode(status);
            Integer newStatus;
            // 白名单线索类型为手机号码且生效范围包含phone，需要下发给YJ
            boolean isIssuedYJ = (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(entity.getType()) || WhiteListTypeEnum.FIXED_PHONE.getCode().equals(entity.getType())) &&
                    entity.getEffectiveScope().contains(WhiteListScopeEnum.PHONE.getCode().toString());

            switch (statusEnum){
                case IN_CONTROL:
                    // 判断数据是否为停控，只有停控才能转在控中
                    if (!WhiteListStatusEnum.STOP_CONTROL.getCode().equals(entity.getStatus())){
                        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.status.is.not.stopControl"));
                    }
                    if (isIssuedYJ){
                        newStatus = WhiteListStatusEnum.TO_BE_START.getCode();
                    }else {
                        newStatus = WhiteListStatusEnum.IN_CONTROL.getCode();
                    }

                    break;
                case STOP_CONTROL:
                    // 判断数据是否为在控，只有在控才能转停控
                    if (!WhiteListStatusEnum.IN_CONTROL.getCode().equals(entity.getStatus())){
                        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.status.is.not.control"));
                    }
                    if (isIssuedYJ){
                        newStatus = WhiteListStatusEnum.TO_BE_STOP.getCode();
                    }else {
                        newStatus = WhiteListStatusEnum.STOP_CONTROL.getCode();
                    }

                    break;
                case DELETE:
                    // 判断数据是否为停控，只有停控才能删除
                    if (!WhiteListStatusEnum.STOP_CONTROL.getCode().equals(entity.getStatus())){
                        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.status.is.not.stopControl"));
                    }
                    newStatus = WhiteListStatusEnum.DELETE.getCode();
                    break;
                default:
                    return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.status.is.error"));
            }
            WhiteListEntity updateEntity = WhiteListEntity.builder()
                    .id(id)
                    .formatRule(entity.getFormatRule())
                    .status(newStatus)
                    .build();
            updateLists.add(updateEntity);
        }

        if (CollectionUtil.isNotEmpty(updateLists)){
            boolean flag = this.updateBatchById(updateLists);
            if (flag){
                // 获取待下发给YJ的白名单
                List<WhiteListEntity> whiteLists = updateLists.stream()
                        .filter(item -> WhiteListStatusEnum.TO_BE_START.getCode().equals(item.getStatus()) ||
                                WhiteListStatusEnum.TO_BE_STOP.getCode().equals(item.getStatus()))
                        .collect(Collectors.toList());
                ThreadPoolUtil.getDefaultExecutorInstance().submit(() -> {
                    // 异步执行
                    issuedWhiteListToYJ(whiteLists, whiteLists.get(0).getStatus());
                });
            }
        }

        return ReturnModel.getInstance().ok();
    }

    @Override
    public Map<String, Object> queryList(WhiteListQueryParam queryParam) {
        // 查询白名单
        Page<WhiteListEntity> page = new Page<>(queryParam.getOnPage(), queryParam.getSize());
        LambdaQueryWrapper<WhiteListEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WhiteListEntity::getType, queryParam.getType());
        wrapper.ne(WhiteListEntity::getStatus, WhiteListStatusEnum.DELETE.getCode());

        if (StringUtils.isNotEmpty(queryParam.getKeyword())){
            wrapper.and(w ->
                    w.like(WhiteListEntity::getFormatRule, queryParam.getKeyword())
                    .or()
                    .like(WhiteListEntity::getRule, queryParam.getKeyword())
            );
        }
        if (queryParam.getStartTime() != null){
            wrapper.ge(WhiteListEntity::getCreateTime, queryParam.getStartTime());
        }
        if (queryParam.getEndTime() != null){
            wrapper.le(WhiteListEntity::getCreateTime, queryParam.getEndTime());
        }
        // 在末尾手动添加sql
        String orderField;
        String orderType = " DESC";
        if (Integer.valueOf(1).equals(queryParam.getOrderType())){
            orderType = " ASC";
        }
        if (StringUtils.isEmpty(queryParam.getOrderField())){
            orderField = "modify_time";
        }else if ("createTime".equals(queryParam.getOrderField())){
            orderField = "create_time";
        }else if ("rule".equals(queryParam.getOrderField())){
            orderField = "format_rule";
        }else {
            orderField = queryParam.getOrderField();
        }

        wrapper.last("ORDER BY " + orderField + orderType);
        List<WhiteListEntity> records = this.page(page, wrapper).getRecords();

        Map<String, Object> result = new HashMap<>();
        List<WhiteListVo> dataList = records.stream().map(WhiteListVo::new).collect(Collectors.toList());
        result.put("total", page.getTotal());
        result.put("list", dataList);

        return result;
    }

    public Object importBatch(MultipartFile file, String userId, Integer whiteListType, Integer langType) {
        try {
            //1. 解析excel获取数据
            List excelModelList = analyzeExcelData(file, whiteListType, langType);

            if (CollectionUtils.isEmpty(excelModelList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.isEmpty"));
            }

            // 2. 获取数据库中的该类型的所有白名单，用来做重复性校验
            List<Integer> types = new ArrayList<>();
            if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(whiteListType)){
                // 号码导入同时包含固话和移动电话
                types.add(WhiteListTypeEnum.MOBILE_PHONE.getCode());
                types.add(WhiteListTypeEnum.FIXED_PHONE.getCode());
            }else {
                types.add(whiteListType);
            }
            List<WhiteListEntity> dbWhiteList = this.list(Wrappers.<WhiteListEntity>lambdaQuery()
                    .in(WhiteListEntity::getType, types)
                    .ne(WhiteListEntity::getStatus, WhiteListStatusEnum.DELETE.getCode())
            );

            // 3. 校验并转换成entity
            List<String> errorList = Lists.newArrayList();
            List<WhiteListEntity> insertList = dataTranslate(userId, whiteListType, excelModelList, errorList, dbWhiteList);

            if (errorList.size() != 0){
                String msg = String.join("\n", errorList);
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.VIOLATION_EXCEPTION.getCode()).setMsg(msg);
            }

            //4. 校验白名单上限
            if (isGTMaxCount(whiteListType, insertList.size())){
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("white.list.rule.num.limit"));
            }

            //5. 插入白名单表
            boolean flag = this.saveBatch(insertList, 500);

            // 待下发给YJ的为在控中
            List<WhiteListEntity> issuedYJList = insertList.stream()
                    .filter(item -> WhiteListStatusEnum.TO_BE_START.getCode().equals(item.getStatus()))
                    .collect(Collectors.toList());

            if (flag && issuedYJList.size() != 0){
                // 如果保存数据库成功,且存在状态为在控中的数据，则需要下发给YJ
                ThreadPoolUtil.getDefaultExecutorInstance().submit(() -> {
                    // 异步执行
                    issuedWhiteListToYJ(issuedYJList, WhiteListStatusEnum.TO_BE_START.getCode());
                });
            }

            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error(e.toString());
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage(e.getMessage()));
        }
    }

    private List<WhiteListEntity> dataTranslate(String userId, Integer whiteListType, List excelModelList, List<String> errorList, List<WhiteListEntity> dbWhiteList) {
        Set<String> existRuleSet = new HashSet<>();
        List<WhiteListEntity> insertList = new ArrayList<>();
        for (int i = 0; i < excelModelList.size(); i++) {
            Object excelModel = excelModelList.get(i);
            WhiteListEntity entity;
            if (excelModel instanceof IpWhiteListExcelModel){
                IpWhiteListExcelModel ipWhiteListExcelModel = (IpWhiteListExcelModel) excelModel;
                entity = ipWhiteListExcelModel.toEntity(userId);
            }else if (excelModel instanceof RadiusWhiteListExcelModel){
                RadiusWhiteListExcelModel radiusWhiteListExcelModel = (RadiusWhiteListExcelModel) excelModel;
                entity = radiusWhiteListExcelModel.toEntity(userId);
            }else {
                PhoneWhiteListExcelModel phoneWhiteListExcelModel = (PhoneWhiteListExcelModel) excelModel;
                entity = phoneWhiteListExcelModel.toEntity(userId);
            }

            // 数据格式校验
            String errorMsg = addOrUpdateValidate(entity, whiteListType);
            if (StringUtils.isNotEmpty(errorMsg)){
                String errorMsg1 = String.format(I18nUtils.getMessage("import.excel.data.error"), i + 1, errorMsg);
                errorList.add(errorMsg1);
                continue;
            }

            // 文件内部重复性校验
            if (existRuleSet.contains(entity.getFormatRule())){
                String errorMsg1 = String.format(I18nUtils.getMessage("import.excel.data.repeat"), i + 1, entity.getFormatRule());
                errorList.add(errorMsg1);
                continue;
            }

            // 数据库数据重复性校验
            errorMsg = importRepeatValidate(entity, dbWhiteList);
            if (StringUtils.isNotEmpty(errorMsg)){
                String errorMsg1 = String.format(I18nUtils.getMessage("import.excel.data.exist"), i + 1, errorMsg);
                errorList.add(errorMsg1);
                continue;
            }

            insertList.add(entity);
            existRuleSet.add(entity.getFormatRule());
        }

        return insertList;
    }

    private String importRepeatValidate(WhiteListEntity checkEntity, List<WhiteListEntity> dbWhiteLists) {
        if (CollectionUtils.isEmpty(dbWhiteLists)){
            return "";
        }

        for (WhiteListEntity dbWhiteList : dbWhiteLists) {
            if (checkEntity.getFormatRule().equals(dbWhiteList.getFormatRule())){
                // 内容重复
                return dbWhiteList.getFormatRule();
            }
        }

        return "";
    }

    private List analyzeExcelData(MultipartFile file, Integer whiteListType, Integer langType) throws Exception {
        List excelModelList = new ArrayList();
        if (WhiteListTypeEnum.IP.getCode().equals(whiteListType)){
            ExcelUtil<IpWhiteListExcelModel> excelUtil = new ExcelUtil<>(IpWhiteListExcelModel.class);
            excelModelList = excelUtil.importExcel(file.getInputStream(), langType);
        }else if (WhiteListTypeEnum.RADIUS.getCode().equals(whiteListType)){
            ExcelUtil<RadiusWhiteListExcelModel> excelUtil = new ExcelUtil<>(RadiusWhiteListExcelModel.class);
            excelModelList = excelUtil.importExcel(file.getInputStream(), langType);
        }else if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(whiteListType)){
            ExcelUtil<PhoneWhiteListExcelModel> excelUtil = new ExcelUtil<>(PhoneWhiteListExcelModel.class);
            excelModelList = excelUtil.importExcel(file.getInputStream(), langType);
        }
        return excelModelList;
    }

    /**
     * 导出白名单信息
     * @param exportType 导出文件格式类型, 2=txt;3=Excel;其它=csv
     * @param whiteListTypes 白名单类型
     * @param ids 导出数据ID, 如果是全部则为-1
     */
    @Override
    public void export(Integer exportType, List<Integer> whiteListTypes, String ids, HttpServletResponse response) {

        List<WhiteListEntity> whiteLists;

        //导出白名单信息
        if (ALL_DATA_IDS.equals(ids)) {
            whiteLists = this.list(new LambdaQueryWrapper<WhiteListEntity>()
                    .in(WhiteListEntity::getType, whiteListTypes)
                    .ne(WhiteListEntity::getStatus, WhiteListStatusEnum.DELETE.getCode())
            );
        }else {
            List<Integer> idList = Arrays.stream(ids.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            whiteLists = this.listByIds(idList);
        }

        try {
            List<ExportWhiteListModel> exportModels = new ArrayList<>();
            // 转成导出model，并将类型和生效范围转码
            whiteLists.forEach(item -> {
                String effectiveScope = item.getEffectiveScope();
                String[] split = effectiveScope.split(",");
                List<String> collect = Arrays.stream(split)
                        .map(code -> WhiteListScopeEnum.getByCode(Integer.valueOf(code)))
                        .collect(Collectors.toList());

                String effectiveScopeStr = String.join(",", collect);
                ExportWhiteListModel exportModel = ExportWhiteListModel.builder()
                        .countryCode(item.getCountryCode())
                        .rule(item.getRule())
                        .typeStr(WhiteListTypeEnum.getByCode(item.getType()))
                        .effectiveScope(effectiveScopeStr)
                        .remark(item.getRemark())
                        .build();

                exportModels.add(exportModel);
            });

            ExportModel exportModel = new ExportModel();
            exportModel.setExportType(exportType);
            exportModel.setIds(ids);
            String[] exportFields;
            if (whiteListTypes.contains(WhiteListTypeEnum.IP.getCode()) || whiteListTypes.contains(WhiteListTypeEnum.RADIUS.getCode())){
                exportFields = ExportConstant.IP_RADIUS_WHITE_LIST_EXPORT_FIELDS;
            }else {
                exportFields = ExportConstant.PHONE_WHITE_LIST_EXPORT_FIELDS;
            }

            ExportUtils.export(exportModel, exportModels, response, FILE_NAME + System.currentTimeMillis(), exportFields, getBaseStationExportI18Title(exportFields));
        }catch (Exception e){
            log.error("导出白名单信息失败", e);
        }
    }

    /**
     * @param whiteLists 下发的白名单内容
     * @param status 状态
     * @desc 下发白名单到YJ, 失败重试3次
     */
    public void issuedWhiteListToYJ(List<WhiteListEntity> whiteLists, Integer status) {
        if (CollectionUtil.isEmpty(whiteLists)){
            return;
        }
        // 号码下发给YJ时需要在前面加上 +
        List<String> numbers = whiteLists.stream().map(e -> "+" + e.getFormatRule()).collect(Collectors.toList());
        List<Long> ids = whiteLists.stream().map(WhiteListEntity::getId).collect(Collectors.toList());

        // 配置重试策略
        retryTemplate.execute(context -> {
            IcsWhitelistRuleModel icsWhitelistRuleModel;
            Integer newStatus;
            if (WhiteListStatusEnum.TO_BE_START.getCode().equals(status)){
                icsWhitelistRuleModel = new IcsWhitelistRuleModel("add", numbers);
                newStatus = WhiteListStatusEnum.IN_CONTROL.getCode();
            }else if (WhiteListStatusEnum.TO_BE_STOP.getCode().equals(status)){
                icsWhitelistRuleModel = new IcsWhitelistRuleModel("del", numbers);
                newStatus = WhiteListStatusEnum.STOP_CONTROL.getCode();
            }else {
                log.error("Invalid status code: {}", status);
                return null;
            }

            ReturnModel resp = yjApiService.whitelistRule(icsWhitelistRuleModel);
            if (resp == null){
                log.error("YJ API call failed, please check the network connection or the YJ API service status");
                // 抛出异常，进行重试
                throw new RuntimeException("YJ API call failed, response is null");
            }

            if (resp.getCode() == CommonConstant.ICS_INVALID_SESSION_ID) {
                //sessionid失效，重新登录
                yjApiService.initSessionId();
                // 抛出异常，进行重试
                throw new RuntimeException("Invalid sessionId");
            } else if (resp.getCode() == CommonConstant.ICS_SUCCESS) {
                //批量修改白名单状态
                List<WhiteListEntity> updateLists = ids.stream()
                        .map(id -> WhiteListEntity.builder().id(id).status(newStatus).build())
                        .collect(Collectors.toList());

                // 切换数据源为mysql
                DynamicDataSourceContextHolder.push("mysql");

                boolean flag = this.updateBatchById(updateLists);
                if (flag){
                    log.info("Update white list status success, ids: {}", ids);
                } else {
                    log.error("Update white list status failed, ids: {}", ids);
                }
            }
            return null;
        }, ex -> {
            // 当重试次数耗尽且所有尝试都失败时，打印异常
            log.error("All retries failed for initializing MntId map", ex.getLastThrowable());
            return null;
        });
    }

    /**
     * 获取白名单信息导出国际化标题
     * @return 基站位置信息导出国际化标题
     */
    private String[] getBaseStationExportI18Title(String[] exportTitles) {
        String[] titleArr = new String[exportTitles.length];

        for (int i = 0; i < exportTitles.length; i++) {
            titleArr[i] = I18nUtils.getMessage("white.list.export.title." + exportTitles[i]);
        }
        return titleArr;
    }

    private boolean isGTMaxCount(Integer whiteListType, int addNum) {
        List<Integer> types = new ArrayList<>();
        if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(whiteListType) || WhiteListTypeEnum.FIXED_PHONE.getCode().equals(whiteListType)){
            types.add(WhiteListTypeEnum.MOBILE_PHONE.getCode());
            types.add(WhiteListTypeEnum.FIXED_PHONE.getCode());
        }else {
            types.add(whiteListType);
        }

        int count = this.count(new LambdaQueryWrapper<WhiteListEntity>()
                .in(WhiteListEntity::getType, types)
                .ne(WhiteListEntity::getStatus, WhiteListStatusEnum.DELETE.getCode())
        );

        return count + addNum > 1000;
    }

    private WhiteListEntity getSame(WhiteListEntity param) {

        Integer type = param.getType();
        List<WhiteListEntity> sameEntities = this.list(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(StringUtils.isNotEmpty(param.getCountryCode()), WhiteListEntity::getCountryCode, param.getCountryCode())
                .eq(WhiteListEntity::getRule, param.getRule())
                .eq(WhiteListEntity::getType, type)
                .ne(WhiteListEntity::getStatus, WhiteListStatusEnum.DELETE.getCode())
                .ne(param.getId() != null, WhiteListEntity::getId, param.getId())
        );

        if (CollectionUtil.isEmpty(sameEntities)){

            return WhiteListEntity.builder().build();
        }

        return sameEntities.get(0);
    }

    private String addOrUpdateValidate(WhiteListEntity param, Integer importType) {
        String rule = param.getRule();
        if (StringUtils.isEmpty(rule)){
            return I18nUtils.getMessage("white.list.rule.is.empty");
        }

        Integer type = param.getType();
        if (importType == null){
            if (type == null || type < 1 || type > 4){
                return I18nUtils.getMessage("white.list.type.is.error");
            }
        } else if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(importType)) {
            // 导入类型是号码时， excel里必须填3或4
            if (!(WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(type) || WhiteListTypeEnum.FIXED_PHONE.getCode().equals(type))){
                return I18nUtils.getMessage("white.list.type.is.error");
            }
        }

        if (StringUtils.isEmpty(param.getEffectiveScope())){
            return I18nUtils.getMessage("white.list.effective.scope.is.empty");
        }

        if (WhiteListTypeEnum.FIXED_PHONE.getCode().equals(type)){
            //固定电话白名单生效范围只能是phone
            if (param.getEffectiveScope().contains(WhiteListScopeEnum.ONLINE_DATA.getCode().toString())){
                return I18nUtils.getMessage("white.list.effective.scope.is.error");
            }
        }

        if (WhiteListTypeEnum.IP.getCode().equals(type) || WhiteListTypeEnum.RADIUS.getCode().equals(type)){
            //ip、radius白名单生效范围只能是online data
            if (param.getEffectiveScope().contains(WhiteListScopeEnum.PHONE.getCode().toString())){
                return I18nUtils.getMessage("white.list.effective.scope.is.error");
            }
        }

        if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(type) || WhiteListTypeEnum.FIXED_PHONE.getCode().equals(type)){
            String countryCode = param.getCountryCode();
            String phone = param.getRule();

            if (StringUtils.isEmpty(countryCode)){
                return I18nUtils.getMessage("white.list.country.code.is.empty");
            }
            int length = countryCode.length() + phone.length();
            // 号码需要做长度和格式校验
            if (length < 4 || length > 30){
                return I18nUtils.getMessage("white.list.phone.length.is.error");
            }
            // 国家码和号码必须为纯数字
            if (!StringUtils.isNumeric(countryCode) || !StringUtils.isNumeric(phone)){
                return I18nUtils.getMessage("white.list.phone.is.error");
            }
        }else if (WhiteListTypeEnum.IP.getCode().equals(type)){
            if (!IpUtil.isipv4(rule) && !IpUtil.isipv6(rule)){
                return I18nUtils.getMessage("white.list.ip.is.error");
            }
        }else {
            // radius白名单校验长度不大于64
            if (rule.length() > 64){
                return I18nUtils.getMessage("white.list.radius.length.is.error");
            }
        }

        return "";
    }

    @Override
    public void syncAllWhiteList() {
        // 1. 查询数据库所有生效范围包含phone的在控白名单
        List<WhiteListEntity> whiteLists = this.list(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.IN_CONTROL.getCode())
                .in(WhiteListEntity::getType, WhiteListTypeEnum.MOBILE_PHONE.getCode(), WhiteListTypeEnum.FIXED_PHONE.getCode())
                .like(WhiteListEntity::getEffectiveScope, WhiteListScopeEnum.PHONE.getCode())
        );
        Set<String> dbPhones = whiteLists.stream().map(e -> "+" + e.getFormatRule()).collect(Collectors.toSet());

        // 2. 查询YJ所有在控白名单
        IcsWhitelistRuleModel icsWhitelistRuleModel = new IcsWhitelistRuleModel("ls", null);
        ReturnModel resp = yjApiService.whitelistRule(icsWhitelistRuleModel);
        if (resp == null){
            log.error("query YJ all whiteList fail, please check the YJ API service status");
            return;
        }
        if (resp.getCode() == CommonConstant.ICS_INVALID_SESSION_ID) {
            //sessionid失效，重新登录重试一次
            yjApiService.initSessionId();
            resp = yjApiService.whitelistRule(icsWhitelistRuleModel);
        }
        if (resp == null || resp.getCode() != CommonConstant.ICS_SUCCESS){
            log.error("query YJ all whiteList fail");
            return;
        }

        List<String> YJWhiteList = (List<String>) resp.getData();

        // 获取所有待停控的白名单
        List<String> toBeStopWhiteLists = new ArrayList<>();
        for (String phone : YJWhiteList) {
            if (!dbPhones.contains(phone)){
                toBeStopWhiteLists.add(phone);
            }
        }

        // 获取所有待在控的白名单
        List<String> toBeStartWhiteLists = new ArrayList<>();
        for (String phone : dbPhones){
            if (!YJWhiteList.contains(phone)){
                toBeStartWhiteLists.add(phone);
            }
        }

        // 将待停控和待在控的数据重新下发给YJ
        IcsWhitelistRuleModel addRuleModel = new IcsWhitelistRuleModel("add", toBeStartWhiteLists);
        IcsWhitelistRuleModel stopRuleModel = new IcsWhitelistRuleModel("del", toBeStopWhiteLists);
        resp = yjApiService.whitelistRule(addRuleModel);
        if (resp == null || resp.getCode() != CommonConstant.ICS_SUCCESS){
            log.error("Failed to synchronize the whitelist under control to YJ");
        }
        resp = yjApiService.whitelistRule(stopRuleModel);
        if (resp == null || resp.getCode() != CommonConstant.ICS_SUCCESS){
            log.error("Synchronization of stop control whitelist to YJ failed");
        }
    }
}
