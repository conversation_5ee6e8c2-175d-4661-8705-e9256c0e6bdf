server.port = 8098
server.tomcat.uri-encoding = utf-8
server.context-path = /basic_library
mybatis-plus.configuration.jdbc-type-for-null = null
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.id-type = 1
spring.thymeleaf.mode = LEGACYHTML5
spring.jackson.time-zone = GMT+8
spring.jackson.date-format = yyyy-MM-dd HH:mm:ss
spring.resources.static-locations = classpath:/img/,classpath:/META-INF/resources/,classpath:/file/

#数据源配置

spring.autoconfigure.exclude = com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.datasource.dynamic.primary = oracle
spring.datasource.dynamic.strict = false
spring.datasource.dynamic.datasource.mysql.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.mysql.url = *********************************************************************************************************************************************
spring.datasource.dynamic.datasource.mysql.username = de_dev
spring.datasource.dynamic.datasource.mysql.password = 123456
spring.datasource.dynamic.datasource.mysql.driver-class-name = com.mysql.jdbc.Driver

spring.datasource.dynamic.datasource.oracle.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.oracle.url = ********************************************
spring.datasource.dynamic.datasource.oracle.username = fhnsdb
spring.datasource.dynamic.datasource.oracle.password = fhnsdb
spring.datasource.dynamic.datasource.oracle.driver-class-name = oracle.jdbc.driver.OracleDriver


spring.datasource.druid.initial-size = 5
spring.datasource.druid.max-active = 30
spring.datasource.druid.min-idle = 5
spring.datasource.druid.max-wait = 60000
spring.datasource.druid.pool-prepared-statements = true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size = 20
spring.datasource.druid.time-between-eviction-runs-millis = 60000
spring.datasource.druid.min-evictable-idle-time-millis = 30000
spring.datasource.druid.max-evictable-idle-time-millis = 60000
spring.datasource.druid.validation-query = SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle = true
spring.datasource.druid.test-on-borrow = false
spring.datasource.druid.test-on-return = false
spring.datasource.druid.stat-view-servlet.enabled = true
spring.datasource.druid.stat-view-servlet.url-pattern = /druid/*
spring.datasource.druid.stat-view-servlet.login-username = admin
spring.datasource.druid.stat-view-servlet.login-password = admin
spring.datasource.druid.filter.stat.log-slow-sql = true
spring.datasource.druid.filter.stat.slow-sql-millis = 1000
spring.datasource.druid.filter.stat.merge-sql = true
spring.datasource.druid.filter.wall.config.multi-statement-allow = true

spring.application.name = basic_library
feign.hystrix.enabled = true
spring.messages.basename = i18n.messages
system.appId = 76
server.tomcat.basedir = /semptian/tmp
# kafka key 消费者 序列化者
# kafka value 消费者 序列化者
# kafka 消费者 自动提交间隔
# kafka 消费者 是否自动提交
spring.kafka.consumer.enable-auto-commit = true
# kafka 消费者  偏移量消费方式
spring.kafka.consumer.auto-offset-reset = earliest
# kafka 消费者 单次拉去最大数据条数
spring.kafka.consumer.max-poll-records = 500
# kafka 消费者 服务地址 （需要修改）
spring.kafka.consumer.bootstrap-servers = 192.168.80.103:6667,192.168.80.104:6667,192.168.80.105:6667,192.168.80.106:6667,192.168.80.107:6667
spring.kafka.consumer.fetch-max-wait = 3000
# kafka 生产者 服务地址 （需要修改）
spring.kafka.producer.bootstrap-servers = 192.168.80.103:6667,192.168.80.104:6667,192.168.80.105:6667,192.168.80.106:6667,192.168.80.107:6667
# kafka value 生产者 序列化者
spring.kafka.producer.value-serializer = org.apache.kafka.common.serialization.StringSerializer
# kafka key 生产者 序列化者
spring.kafka.producer.key-serializer = org.apache.kafka.common.serialization.StringSerializer
# kafka 生产者批量提交数据条数
spring.kafka.producer.batch-size = 16384
# kafka 生产者 最大缓存内存
spring.kafka.producer.buffer-memory = 33554432
spring.kafka.producer.acks = all
#spring.kafka.properties.keberos-enable = true
#spring.kafka.properties.sasl.mechanism = GSSAPI
#spring.kafka.properties.sasl.kerberos.service.name = kafka
#spring.kafka.properties.security.protocol = SASL_PLAINTEXT
# eureka 注册中心地址 (需要修改)
#是否注册到服务中心
# 注册的时候使用 ip 而不是主机名

spring.kafka.consumer.key-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.auto-commit-interval = 500