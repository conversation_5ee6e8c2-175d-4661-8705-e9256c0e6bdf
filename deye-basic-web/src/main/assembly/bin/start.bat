echo off

set PRO_NAME=primary-library

set BIN_DIR=%cd%
cd..
set PRO_NAME_DIR=%cd%
set PRO_JAR=%PRO_NAME_DIR%\lib\%PRO_NAME%.jar
set CONF_DIR=%PRO_NAME_DIR%\conf
set LOG_DIR=%PRO_NAME_DIR%\logs
set LIB_DIR=%PRO_NAME_DIR%\lib

set CONFIG= -Dlogging.path=%LOG_DIR% -Dlogging.config=%CONF_DIR%\logback-spring.xml -Dspring.config.location=%CONF_DIR%

set DEBUG_OPTS=
if ""%1"" == ""debug"" (
   set DEBUG_OPTS= -Xloggc:%PRO_NAME_DIR%/logs/gc.log -verbose:gc -XX:+PrintGCDetails -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=%LOG_DIR%
   goto debug
)

set JMX_OPTS=
if ""%1"" == ""jmx"" (
   set JMX_OPTS= -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9888 -Dcom.sun.management.jmxremote.ssl=FALSE -Dcom.sun.management.jmxremote.authenticate=FALSE 
   goto jmx
)

echo "Starting the %APP_NAME%"
java -Xms512m -Xmx512m -server %DEBUG_OPTS% %JMX_OPTS% %CONFIG% -jar %PRO_JAR%
goto end

:debug
echo "debug"
java -Xms512m -Xmx512m -server %DEBUG_OPTS% %CONFIG% -jar %PRO_JAR%
goto end

:jmx
java -Xms512m -Xmx512m -server %JMX_OPTS% %CONFIG% -jar %PRO_JAR%
goto end

:end
pause