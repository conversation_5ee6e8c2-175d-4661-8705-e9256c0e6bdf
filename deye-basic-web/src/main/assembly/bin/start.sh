#!/bin/bash
source /etc/profile

PRO_NAME=primary-library

cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
CONF_DIR=$DEPLOY_DIR/conf

JAVA_MEM_OPTS='-server -Xms2g -Xmx2g -Xss256k -XX:-UseGCOverheadLimit -XX:+DisableExplicitGC -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+HeapDumpOnOutOfMemoryError -Xloggc:logs/gc.log'
echo "$JAVA_MEM_OPTS"

PIDS=`ps -f | grep java | grep "$DEPLOY_DIR" |awk '{print $2}'`
if [ -n "$PIDS" ]; then
    echo "ERROR:  already started!"
    echo "PID: $PIDS"
    exit 1
fi

LOGS_DIR=$DEPLOY_DIR/logs
if [ ! -d $LOGS_DIR ]; then
    mkdir $LOGS_DIR
fi
STDOUT_FILE=$LOGS_DIR/$PRO_NAME.log

LIB_DIR=$DEPLOY_DIR/lib
LIB_JARS=`ls $LIB_DIR|grep .jar|awk '{print "'$LIB_DIR'/"$0}'|tr "\n" ":"`

CONFIG_FILES=" -Dlogging.path=$LOGS_DIR  -Dspring.config.location=$CONF_DIR/"

jarfile=`find . -name 'primary-library*.jar'`
#JAR_NAME=case-web*.jar
nohup java $JAVA_MEM_OPTS -Dfile.encoding="UTF-8" -Duser.timezone=Asia/Shanghai $CONFIG_FILES -jar $jarfile > $STDOUT_FILE >/dev/null 2>&1 &

echo "OK!"
PIDS=`ps -f | grep java | grep "$DEPLOY_DIR" | awk '{print $2}'`
echo "PID: $PIDS"
echo "STDOUT: $STDOUT_FILE"
