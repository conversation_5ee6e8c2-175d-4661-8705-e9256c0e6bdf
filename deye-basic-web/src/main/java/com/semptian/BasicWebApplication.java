package com.semptian;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

/**
 * web模块启动类
 *
 * @Author: sk
 * @date: 2020/12/15
 * @params:
 */
@EnableFeignClients
@SpringBootApplication
@EnableScheduling
public class BasicWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(BasicWebApplication.class, args);
    }

    @Bean
    public RestTemplate rest() {
        return new RestTemplate();
    }
}
