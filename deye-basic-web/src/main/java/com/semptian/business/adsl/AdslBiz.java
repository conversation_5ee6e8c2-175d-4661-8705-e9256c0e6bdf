package com.semptian.business.adsl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.AssemblyCondition;
import com.semptian.utils.ExportUtils;
import com.semptian.entity.AdslEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.AdslModel;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import com.semptian.service.AdslService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.semptian.constant.CommonConstant.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AdslBiz {

    @Autowired
    private AdslService adslService;

    private static final String FILE_NAME = "ADSL_";

    public Object save(AdslModel adslModel) {

        if (adslService.checkOnlineAccountExist(adslModel.getId(), adslModel.getOnlineAccount())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("adsl.repeat.onlineAccount"));
        }

        AdslEntity adslEntity = new AdslEntity();
        BeanUtil.copyProperties(adslModel, adslEntity);
        adslEntity.setStatus(VALID);
        adslEntity.setUpdateTime(DateUtil.getSeconds());
        //新增
        if (adslModel.getId() == null) {
            adslEntity.setCreateTime(DateUtil.getSeconds());
            adslEntity.setId(adslService.getNextVal());
            adslService.save(adslEntity);
        } else {
            //修改
            adslService.updateById(adslEntity);
        }
        return ReturnModel.getInstance().ok();
    }

    public Object detail(Long id) {
        return adslService.getById(id);
    }

    public Object delete(DelModel model) {
        return adslService.removeByIds(model.getIds());
    }

    public Object pageQuery(
            String onlineAccount,
            Integer onlineAccountFlag,
            String identityCardNumber,
            Integer identityCardNumberFlag,
            String accountHolderName,
            Integer accountHolderNameFlag,
            String assemblyPhone,
            Integer assemblyPhoneFlag,
            String installationPosition,
            Integer installationPositionFlag,
            String accountHolderPhone,
            Integer accountHolderPhoneFlag,
            String longitude,
            String latitude,
            String address,
            Integer addressFlag,
            Integer ispCode,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> resultMap = Maps.newHashMap();
        QueryWrapper<AdslEntity> entityWrapper = new QueryWrapper<>();

        Page<AdslEntity> page = new Page<>(onPage, size);
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtilNom.humpToLine(orderField));
        if (orderType == 1) {
            orderItem.setAsc(true);
        } else {
            orderItem.setAsc(false);
        }
        page.addOrder(orderItem);

        if (StringUtils.isNotBlank(onlineAccount)) {
            AssemblyCondition.pageAssemblyCondition(onlineAccountFlag, "ONLINE_ACCOUNT", onlineAccount, entityWrapper);
        }
        if (StringUtils.isNotBlank(identityCardNumber)) {
            AssemblyCondition.pageAssemblyCondition(identityCardNumberFlag, "IDENTITY_CARD_NUMBER", identityCardNumber, entityWrapper);
        }
        if (StringUtils.isNotBlank(accountHolderName)) {
            AssemblyCondition.pageAssemblyCondition(accountHolderNameFlag, "ACCOUNT_HOLDER_NAME", accountHolderName, entityWrapper);
        }
        if (StringUtils.isNotBlank(assemblyPhone)) {
            AssemblyCondition.pageAssemblyCondition(assemblyPhoneFlag, "ASSEMBLY_PHONE", assemblyPhone, entityWrapper);
        }
        if (StringUtils.isNotBlank(installationPosition)) {
            AssemblyCondition.pageAssemblyCondition(installationPositionFlag, "INSTALLATION_POSITION", installationPosition, entityWrapper);
        }
        if (StringUtils.isNotBlank(accountHolderPhone)) {
            AssemblyCondition.pageAssemblyCondition(accountHolderPhoneFlag, "ACCOUNT_HOLDER_PHONE", accountHolderPhone, entityWrapper);

        }
        if (StringUtils.isNotBlank(address)) {
            AssemblyCondition.pageAssemblyCondition(addressFlag, "ADDRESS", address, entityWrapper);
        }
        if (ispCode != null) {
            AssemblyCondition.pageAssemblyCondition(PRECISE_MATCH, "ISP_CODE", ispCode.toString(), entityWrapper);
        }
        if (StringUtils.isNotBlank(longitude)) {
            int index = longitude.indexOf(",");
            List<String> longitudeList = Arrays.stream(longitude.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (index > 0) {
                entityWrapper.ge("to_number(LONGITUDE)", longitudeList.get(0));
                if (longitudeList.size() > 1) {
                    entityWrapper.le("to_number(LONGITUDE)", longitudeList.get(1));
                }
            } else {
                if (longitudeList.size() > 0) {
                    entityWrapper.le("to_number(LONGITUDE)", longitudeList.get(0));
                }
            }

        }
        if (StringUtils.isNotBlank(latitude)) {
            int index = latitude.indexOf(",");
            List<String> latitudeList = Arrays.stream(latitude.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (index > 0) {
                entityWrapper.ge("to_number(LATITUDE)", latitudeList.get(0));
                if (latitudeList.size() > 1) {
                    entityWrapper.le("to_number(LATITUDE)", latitudeList.get(1));
                }
            } else {
                if (latitudeList.size() > 0) {
                    entityWrapper.le("to_number(LATITUDE)", latitudeList.get(0));
                }
            }
        }

        Page<AdslEntity> pageList = adslService.page(page, entityWrapper);
        int total = adslService.count(entityWrapper);
        resultMap.put("total", total);
        resultMap.put("resultList", pageList.getRecords());
        return resultMap;
    }

    public void export(ExportModel model, HttpServletResponse response) {
        QueryWrapper<AdslEntity> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(model.getIds())) {
            List<Long> ids = Stream.of(model.getIds()).map(Long::parseLong).collect(Collectors.toList());
            wrapper.in("ID", ids);
        }
        List<AdslEntity> list = adslService.getBaseMapper().selectList(wrapper);
        ExportUtils.export(model,list,response,FILE_NAME,assemblyField(),assemblyTitle());
    }

    private String[] assemblyTitle() {
        return new String[]{
                i18("adsl.entity.onlineAccount"),
                i18("adsl.entity.accountHolderName"),
                i18("adsl.entity.identityCardNumber"),
                i18("adsl.entity.accountHolderPhone"),
                i18("adsl.entity.contactName"),
                i18("adsl.entity.contactIdNumber"),
                i18("adsl.entity.contactNumber"),
                i18("adsl.entity.assemblyPhone"),
                i18("adsl.entity.installationPosition"),
                i18("adsl.entity.cityCode"),
                i18("adsl.entity.address"),
                i18("adsl.entity.longitude"),
                i18("adsl.entity.latitude")
        };
    }

    private String[] assemblyField() {
        return new String[]{
                "onlineAccount",
                "accountHolderName",
                "identityCardNumber",
                "accountHolderPhone",
                "contactName",
                "contactIdNumber",
                "contactNumber",
                "assemblyPhone",
                "installationPosition",
                "cityCode",
                "address",
                "longitude",
                "latitude"
        };
    }


    public String i18(String key) {
        return I18nUtils.getMessage(key);
    }

}
