package com.semptian.business.countrycode;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.AssemblyCondition;
import com.semptian.utils.ExportUtils;
import com.semptian.entity.CountryCodeEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.CountryCodeModel;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import com.semptian.service.CountryCodeService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CountryCodeBiz {

    @Autowired
    private CountryCodeService countryCodeService;

    private static final String FILE_NAME = "Country_Code_";


    public Object save(CountryCodeModel model) {
        if (countryCodeService.checkCountryCodeExist(model.getId(), model.getCountryCode())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("countryCode.repeat.code"));
        }

        CountryCodeEntity entity = new CountryCodeEntity();
        BeanUtil.copyProperties(model, entity);
        entity.setStatus(VALID);
        entity.setUpdateTime(DateUtil.getSeconds());
        //新增
        if (model.getId() == null) {
            entity.setCreateTime(DateUtil.getSeconds());
            entity.setId(countryCodeService.getNextVal());
            countryCodeService.save(entity);
        } else {
            //修改
            countryCodeService.updateById(entity);
        }
        return ReturnModel.getInstance().ok();
    }

    public Object detail(Long id) {
        return countryCodeService.getById(id);
    }

    public Object delete(DelModel model) {
        return countryCodeService.removeByIds(model.getIds());
    }

    public Object pageQuery(
            String countryCode,
            Integer countryCodeFlag,
            String countryName,
            Integer countryNameFlag,
            String countryEnName,
            Integer countryEnNameFlag,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> resultMap = Maps.newHashMap();
        QueryWrapper<CountryCodeEntity> entityWrapper = new QueryWrapper<>();

        Page<CountryCodeEntity> page = new Page<>(onPage, size);

        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtilNom.humpToLine(orderField));
        if (orderType == 1) {
            orderItem.setAsc(true);
        } else {
            orderItem.setAsc(false);
        }
        page.addOrder(orderItem);

        if (StringUtils.isNotBlank(countryCode)) {
            AssemblyCondition.pageAssemblyCondition(countryCodeFlag, "COUNTRY_CODE", countryCode, entityWrapper);
        }
        if (StringUtils.isNotBlank(countryName)) {
            AssemblyCondition.pageAssemblyCondition(countryNameFlag, "COUNTRY_NAME", countryName, entityWrapper);
        }
        if (StringUtils.isNotBlank(countryEnName)) {
            AssemblyCondition.pageAssemblyCondition(countryEnNameFlag, "COUNTRY_EN_NAME", countryEnName, entityWrapper);
        }

        Page<CountryCodeEntity> pageList = countryCodeService.page(page, entityWrapper);
        int total = countryCodeService.count(entityWrapper);
        resultMap.put("total", total);
        resultMap.put("resultList", pageList.getRecords());
        return resultMap;

    }

    public void export(ExportModel model, HttpServletResponse response) {
        QueryWrapper<CountryCodeEntity> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(model.getIds())) {
            List<Long> ids = Stream.of(model.getIds()).map(Long::parseLong).collect(Collectors.toList());
            wrapper.in("ID", ids);
        }
        List<CountryCodeEntity> list = countryCodeService.getBaseMapper().selectList(wrapper);
        ExportUtils.export(model, list, response, FILE_NAME, assemblyField(), assemblyTitle());
    }

    private String[] assemblyTitle() {
        return new String[]{
                i18("countryCode.entity.countryCode"),
                i18("countryCode.entity.countryName"),
                i18("countryCode.entity.countryEnName")
        };
    }

    private String[] assemblyField() {
        return new String[]{
                "countryCode",
                "countryName",
                "countryEnName"
        };
    }


    public String i18(String key) {
        return I18nUtils.getMessage(key);
    }


}
