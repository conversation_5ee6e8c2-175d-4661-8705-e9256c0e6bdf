package com.semptian.business.homepage;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.dto.common.HomePageInfoDto;
import com.semptian.dto.common.StatisticsDto;
import com.semptian.entity.*;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.ImportModel;
import com.semptian.param.UploadFileModel;
import com.semptian.service.*;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HomePageBiz {

    @Value("${upload.path}")
    private String uploadFilePath;

    @Autowired
    private CommonService commonService;
    @Autowired
    private AdslService adslService;
    @Autowired
    private IpLibraryService ipLibraryService;
    @Autowired
    private MobileRegisterService mobileRegisterService;
    @Autowired
    private IdCardService idCardService;
    @Autowired
    private NumberSectionService numberSectionService;
    @Autowired
    private CountryCodeService countryCodeService;
    @Autowired
    private InvalidTelService invalidTelService;

    public Object upload(MultipartFile file) {
        try {
            Map<String, Object> result = Maps.newHashMap();
            FileUtil.createDir(uploadFilePath);
            String originalFilename = file.getOriginalFilename();
            String fileType = FileUtil.getFileType(originalFilename);
            String newFileName = FileUtil.renameFile(originalFilename);
            FileUtil.saveFile(file, uploadFilePath, newFileName);
            result.put("fileName", originalFilename);
            result.put("filePath", uploadFilePath + newFileName);
            result.put("extension", fileType);
            result.put("fileSize", file.getSize());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("--------Fail to upload file: {} ", e.toString());
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("Fail to upload");
    }

    public Object delFile(String filePath) {
        try {
            FileUtil.deleteFile(filePath);
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error("-------------Fail to delete file:{}", e.toString());
            e.printStackTrace();
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("Fail to delete file");
    }

    public Object dataStatistics() {
        Map<String, Object> resultMap = Maps.newHashMap();
        List<StatisticsDto> statistics = commonService.statistics();
        List<HomePageInfoDto> homePageInfo = commonService.queryInfo();
        statistics.forEach(item -> resultMap.put(item.getItem(), item.getCount()));
        homePageInfo.forEach(item -> {
            String type = item.getType() + "";
            if (resultMap.containsKey(type)) {
                item.setCount((int) resultMap.get(type));
            }
        });
        return homePageInfo;
    }

    public Object fileImport(ImportModel model, String lang) {
        String[] moduleField = getModuleField(model.getModuleType());
        List<UploadFileModel> fileModelList = model.getUploadModel();
        List<String> filePathList = Lists.newLinkedList();
        long size;
        if (CollectionUtil.isNotEmpty(fileModelList)) {
            List<Map<String, Object>> mapList = Lists.newArrayList();
            for (UploadFileModel uploadFileModel : fileModelList) {
                String filename = uploadFileModel.getFileName();
                size = uploadFileModel.getFileSize();
                String filePath = uploadFileModel.getFilePath();
                filePathList.add(filePath);
                String fileType = uploadFileModel.getExtension();
                List<Map<String, Object>> list = Lists.newLinkedList();
                if (fileType.equalsIgnoreCase(XLS) || fileType.equalsIgnoreCase(XLSX)) {
                    if (size >= XLS_FILE_SIZE) {
                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("File size exceeds 100M");
                    }
                    list = ExcelUtil.excelImport(filePath, moduleField);
                } else if (fileType.equalsIgnoreCase(CSV)) {
                    if (size >= CSV_FILE_SIZE) {
                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("File size exceeds 2G");
                    }
                    list = CsvUtils.readCsv(filePath, getModuleField(model.getModuleType()));
                } else if (fileType.equalsIgnoreCase(TXT)) {
                    if (size >= CSV_FILE_SIZE) {
                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("File size exceeds 2G");
                    }
                    list = CsvUtils.readTxt(filePath, getModuleField(model.getModuleType()));
                }
                if (list == null) {
                    return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("file.parse.exception"));
                }
                //校验数据
                if (CollectionUtil.isNotEmpty(list)) {
                    String msg = checkContent(list, filename, model.getModuleType(), lang);
                    if (msg != null) {
                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(msg);
                    }
                    mapList.addAll(list);
                }
            }

            //删除文件
            ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
            service.schedule(() -> {
                filePathList.forEach(FileUtil::deleteFile);
            }, 30, TimeUnit.SECONDS);

            if (CollectionUtil.isNotEmpty(mapList)) {
                mapList.forEach(item -> {
                            item.put("status", VALID);
                            item.put("createTime", DateUtil.getSeconds());
                            item.put("updateTime", DateUtil.getSeconds());
                            item.forEach((k, v) -> {
                                if (v != null && StringUtils.isBlank(v.toString())) {
                                    item.put(k, null);
                                }
                            });
                        }
                );
                String msg = insertData(model, mapList);
                if (msg != null) {
                    return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(msg);
                }

            }

        }
        return ReturnModel.getInstance().ok();
    }

    private String insertData(ImportModel model, List<Map<String, Object>> mapList) {
        String msg = null;
        try {
            switch (model.getModuleType()) {
                case ONE:
                    List<AdslEntity> entityList1 = JSON.parseArray(JSON.toJSONString(mapList), AdslEntity.class);
                    entityList1 = entityList1.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AdslEntity::getOnlineAccount))), ArrayList::new));
                    adslService.batchInsert(entityList1);
                    break;
                case TWO:
                    break;
                case THREE:
                    List<IpLibraryEntity> entityList3 = JSON.parseArray(JSON.toJSONString(mapList), IpLibraryEntity.class);
                    entityList3 = entityList3.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(IpLibraryEntity::getIpId))), ArrayList::new));
                    entityList3.forEach(item -> {
                        item.setStartIp(String.valueOf(IpUtil.iptolong(item.getStartIp())));
                        item.setEndIp(String.valueOf(IpUtil.iptolong(item.getEndIp())));
                    });
                    ipLibraryService.batchInsert(entityList3);
                    break;
                case FOUR:
                    List<MobileRegisterEntity> entityList4 = JSON.parseArray(JSON.toJSONString(mapList), MobileRegisterEntity.class);
                    entityList4 = entityList4.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MobileRegisterEntity::getMsisdn))), ArrayList::new));
                    mobileRegisterService.batchInsert(entityList4);
                    break;
                case FIVE:
                    List<IdCardEntity> entityList5 = JSON.parseArray(JSON.toJSONString(mapList), IdCardEntity.class);
                    entityList5 = entityList5.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(IdCardEntity::getNetSiteId))), ArrayList::new));
                    idCardService.batchInsert(entityList5);
                    break;
                case SIX:
                    List<NumberSectionEntity> entityList6 = JSON.parseArray(JSON.toJSONString(mapList), NumberSectionEntity.class);
                    entityList6 = entityList6.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(NumberSectionEntity::getMsisdn))), ArrayList::new));
                    numberSectionService.batchInsert(entityList6);
                    break;
                case SEVEN:
                    List<CountryCodeEntity> entityList7 = JSON.parseArray(JSON.toJSONString(mapList), CountryCodeEntity.class);
                    entityList7 = entityList7.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CountryCodeEntity::getCountryCode))), ArrayList::new));
                    countryCodeService.batchInsert(entityList7);
                    break;
                case EIGHT:
                    List<InvalidTelEntity> entityList8 = JSON.parseArray(JSON.toJSONString(mapList), InvalidTelEntity.class);
                    entityList8 = entityList8.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvalidTelEntity::getPhoneNumber))), ArrayList::new));
                    invalidTelService.batchInsert(entityList8);
                    break;
                default:
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("----------batch insert error : {}", e.toString());
            msg = "Bulk insert failed!!!";
        }
        return msg;
    }

    private String checkContent(List<Map<String, Object>> list, String fileName, int fileType, String lang) {
        List<String> fieldList = null;
        switch (fileType) {
            case ONE:
                fieldList = Lists.newArrayList("onlineAccount", "identityCardNumber", "accountHolderName", "assemblyPhone", "cityCode");
                break;
            case TWO:
                fieldList = Lists.newArrayList("baseStationId");
                break;
            case THREE:
                fieldList = Lists.newArrayList("startIp", "endIp", "ipId");
                break;
            case FOUR:
            case SIX:
                fieldList = Lists.newArrayList("msisdn");
                break;
            case FIVE:
                fieldList = Lists.newArrayList("netSiteId", "cardId", "certificateCode");
                break;
            case SEVEN:
                fieldList = Lists.newArrayList("countryCode");
                break;
            case EIGHT:
                fieldList = Lists.newArrayList("phoneNumber");
                break;
            default:
        }
        return checkContent(list, fileName, lang, fieldList, fileType);
    }

    private String checkContent(List<Map<String, Object>> list, String fileName, String lang, List<String> importantField, Integer moduleType) {
        if (CollectionUtil.isNotEmpty(importantField)) {
            for (int i = 0; i < list.size(); i++) {
                String column = null;
                Map<String, Object> map = list.get(i);
                for (String field : importantField) {
                    Object obj = map.get(field);
                    if (obj == null || StringUtils.isBlank(obj.toString())) {
                        String key = getModuleName(moduleType) + "." + "entity" + "." + field;
                        column = I18nUtils.getMessage(key);
                    }
                }
                if (column != null) {
                    return getReturnMsg(lang, i + 2, column, fileName);
                }
            }
        }
        return null;
    }

    private String getReturnMsg(String lang, int rowNum, String column, String fileName) {
        if (LANG_CN.equals(lang)) {
            return String.format("%s中第%s行%s为空", fileName, rowNum, column);
        } else if (LANG_FR.equals(lang)) {
            return String.format("%s la ligne %s du fichier %s est vide", column, rowNum, fileName);
        } else {
            return String.format("%s on line %s in file %s is empty", column, rowNum, fileName);
        }

    }

    private String getModuleName(Integer moduleType) {
        String moduleName = "";
        switch (moduleType) {
            case ONE:
                moduleName = "adsl";
                break;
            case TWO:
                moduleName = "station";
                break;
            case THREE:
                moduleName = "ipLibrary";
                break;
            case FOUR:
                moduleName = "mobileRegister";
                break;
            case FIVE:
                moduleName = "idcard";
                break;
            case SIX:
                moduleName = "numberSection";
                break;
            case SEVEN:
                moduleName = "countryCode";
                break;
            case EIGHT:
                moduleName = "invalidNumber";
                break;
            default:
        }
        return moduleName;
    }

    private String[] getModuleField(int type) {
        String[] fieldStr = new String[0];
        switch (type) {
            case ONE:
                fieldStr = adslField();
                break;
            case TWO:
                fieldStr = baseStationField();
                break;
            case THREE:
                fieldStr = ipLibraryField();
                break;
            case FOUR:
                fieldStr = mobileRegisterField();
                break;
            case FIVE:
                fieldStr = idCardField();
                break;
            case SIX:
                fieldStr = numberSectionField();
                break;
            case SEVEN:
                fieldStr = countryCodeField();
                break;
            case EIGHT:
                fieldStr = invalidPhoneField();
                break;
            default:
        }
        return fieldStr;
    }

    private String[] adslField() {
        return new String[]{
                "onlineAccount",
                "identityCardNumber",
                "accountHolderName",
                "assemblyPhone",
                "cityCode",
                "installationPosition",
                "contactNumber",
                "longitude",
                "latitude",
                "address",
                "ispCode",
                "accessMode",
                "remark"
        };
    }

    private String[] baseStationField() {
        return new String[]{
                "baseStationId",
                "baseStationName",
                "baseStationAddress",
                "geographicalIndication",
                "cityCode",
                "longitude",
                "latitude",
                "ispCode",
                "baseStationType",
                "remark"
        };
    }

    private String[] countryCodeField() {
        return new String[]{
                "countryCode",
                "countryName",
                "countryEnName",
                "remark"
        };
    }

    private String[] idCardField() {
        return new String[]{
                "netSiteId",
                "netSiteName",
                "cardId",
                "userName",
                "sex",
                "certificateCode",
                "certificateType",
                "cityCode",
                "certificationUnit",
                "nationality",
                "companyName",
                "mobile",
                "roomId",
                "remark"
        };
    }

    private String[] invalidPhoneField() {
        return new String[]{
                "phoneNumber",
                "remark"
        };
    }

    private String[] ipLibraryField() {
        return new String[]{
                "startIp",
                "endIp",
                "ipId",
                "companyName",
                "address",
                "contactName",
                "contactNumber",
                "remark"
        };
    }

    private String[] mobileRegisterField() {
        return new String[]{
                "imsi",
                "msisdn",
                "name",
                "sex",
                "age",
                "nationality",
                "certificateCode",
                "certificateType",
                "companyName",
                "registeredAddress",
                "nowAddress",
                "education",
                "email",
                "roomId",
                "residenceType",
                "remark"
        };
    }

    private String[] numberSectionField() {
        return new String[]{
                "msisdn",
                "cityCode",
                "cityName",
                "netType",
                "postCode",
                "areaCode",
                "ispCode",
                "remark"
        };
    }
}
