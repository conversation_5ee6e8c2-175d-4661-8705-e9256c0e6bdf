package com.semptian.business.idcard;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.semptian.common.AssemblyCondition;
import com.semptian.utils.ExportUtils;
import com.semptian.entity.IdCardEntity;
import com.semptian.i18n.I18nUtils;
import com.semptian.model.ExportModel;
import com.semptian.param.IdCardModel;
import com.semptian.service.IdCardService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IdCardBiz {

    @Autowired
    private IdCardService idCardService;

    private static final String FILE_NAME = "IDCARD_";

    public Object pageQuery(
            String netSiteId,
            Integer netSiteIdFlag,
            String netSiteName,
            Integer netSiteNameFlag,
            String cardId,
            Integer cardFlag,
            String userName,
            Integer userNameFlag,
            String roomId,
            Integer roomIdFlag,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> resultMap = Maps.newHashMap();
        QueryWrapper<IdCardEntity> entityWrapper = new QueryWrapper<>();
        Page<IdCardEntity> page = new Page<>(onPage, size);

        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtilNom.humpToLine(orderField));
        if (orderType == 1) {
            orderItem.setAsc(true);
        } else {
            orderItem.setAsc(false);
        }
        page.addOrder(orderItem);

        if (StringUtils.isNotBlank(netSiteId)) {
            AssemblyCondition.pageAssemblyCondition(netSiteIdFlag, "NET_SITE_ID", netSiteId, entityWrapper);
        }
        if (StringUtils.isNotBlank(netSiteName)) {
            AssemblyCondition.pageAssemblyCondition(netSiteNameFlag, "NET_SITE_NAME", netSiteName, entityWrapper);
        }
        if (StringUtils.isNotBlank(cardId)) {
            AssemblyCondition.pageAssemblyCondition(cardFlag, "CARD_ID", cardId, entityWrapper);
        }
        if (StringUtils.isNotBlank(userName)) {
            AssemblyCondition.pageAssemblyCondition(userNameFlag, "USERNAME", userName, entityWrapper);
        }
        if (StringUtils.isNotBlank(roomId)) {
            AssemblyCondition.pageAssemblyCondition(roomIdFlag, "ROOM_ID", roomId, entityWrapper);
        }

        Page<IdCardEntity> pageList = idCardService.page(page, entityWrapper);
        int total = idCardService.count(entityWrapper);
        resultMap.put("total", total);
        resultMap.put("resultList", pageList.getRecords());
        return resultMap;

    }

    public Object detail(Long id) {
        return idCardService.getById(id);
    }

    public Object update(IdCardModel model) {
        IdCardEntity entity = new IdCardEntity();
        BeanUtil.copyProperties(model, entity);
        entity.setStatus(VALID);
        entity.setUpdateTime(DateUtil.getSeconds());
        return idCardService.updateById(entity);
    }

    public void export(ExportModel model, HttpServletResponse response) {
        QueryWrapper<IdCardEntity> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(model.getIds())) {
            List<Long> ids = Stream.of(model.getIds()).map(Long::parseLong).collect(Collectors.toList());
            wrapper.in("ID", ids);
        }
        List<IdCardEntity> list = idCardService.getBaseMapper().selectList(wrapper);
        ExportUtils.export(model, list, response, FILE_NAME, assemblyField(), assemblyTitle());
    }

    private String[] assemblyTitle() {
        return new String[]{
                i18("idcard.entity.netSiteId"),
                i18("idcard.entity.netSiteName"),
                i18("idcard.entity.cityCode"),
                i18("idcard.entity.cardId"),
                i18("idcard.entity.userName"),
                i18("idcard.entity.certificateCode"),
                i18("idcard.entity.certificateType"),
                i18("idcard.entity.certificationUnit"),
                i18("idcard.entity.nationality"),
                i18("idcard.entity.companyName"),
                i18("idcard.entity.mobile"),
                i18("idcard.entity.roomId"),
        };
    }

    private String[] assemblyField() {
        return new String[]{
                "netSiteId",
                "netSiteName",
                "cityCode",
                "cardId",
                "userName",
                "certificateCode",
                "certificateType",
                "certificationUnit",
                "nationality",
                "companyName",
                "mobile",
                "roomId"
        };
    }

    public String i18(String key) {
        return I18nUtils.getMessage(key);
    }

}
