package com.semptian.business.invalidtel;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.AssemblyCondition;
import com.semptian.utils.ExportUtils;
import com.semptian.entity.InvalidTelEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import com.semptian.param.InvalidTelModel;
import com.semptian.service.InvalidTelService;
import com.semptian.utils.DateUtil;
import com.semptian.utils.StringUtilNom;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.semptian.constant.CommonConstant.VALID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InvalidTelBiz {

    @Autowired
    private InvalidTelService invalidTelService;

    private static final String FILE_NAME = "Invalid_Number_";

    public Object save(InvalidTelModel model) {
        if (invalidTelService.checkPhoneNumberExist(model.getId(), model.getPhoneNumber())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("invalidPhone.repeat.phone"));
        }

        InvalidTelEntity entity = new InvalidTelEntity();
        BeanUtil.copyProperties(model, entity);
        entity.setStatus(VALID);
        entity.setUpdateTime(DateUtil.getSeconds());
        //新增
        if (model.getId() == null) {
            entity.setCreateTime(DateUtil.getSeconds());
            entity.setId(invalidTelService.getNextVal());
            invalidTelService.save(entity);
        } else {
            //修改
            invalidTelService.updateById(entity);
        }
        return ReturnModel.getInstance().ok();
    }

    public Object detail(Long id) {
        return invalidTelService.getById(id);
    }

    public Object delete(DelModel model) {
        return invalidTelService.removeByIds(model.getIds());
    }

    public Object pageQuery(
            String phoneNumber,
            Integer phoneNumberFlag,
            String remark,
            Integer remarkFlag,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> resultMap = Maps.newHashMap();
        QueryWrapper<InvalidTelEntity> entityWrapper = new QueryWrapper<>();

        Page<InvalidTelEntity> page = new Page<>(onPage, size);

        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtilNom.humpToLine(orderField));
        if (orderType == 1) {
            orderItem.setAsc(true);
        } else {
            orderItem.setAsc(false);
        }
        page.addOrder(orderItem);

        if (StringUtils.isNotBlank(phoneNumber)) {
            AssemblyCondition.pageAssemblyCondition(phoneNumberFlag, "PHONE_NUMBER", phoneNumber, entityWrapper);
        }
        if (StringUtils.isNotBlank(remark)) {
            AssemblyCondition.pageAssemblyCondition(remarkFlag, "REMARK", remark, entityWrapper);
        }


        Page<InvalidTelEntity> pageList = invalidTelService.page(page, entityWrapper);
        int total = invalidTelService.count(entityWrapper);
        resultMap.put("total", total);
        resultMap.put("resultList", pageList.getRecords());
        return resultMap;
    }

    public void export(ExportModel model, HttpServletResponse response) {
        QueryWrapper<InvalidTelEntity> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(model.getIds())) {
            List<Long> ids = Stream.of(model.getIds()).map(Long::parseLong).collect(Collectors.toList());
            wrapper.in("ID", ids);
        }
        List<InvalidTelEntity> list = invalidTelService.getBaseMapper().selectList(wrapper);
        ExportUtils.export(model, list, response, FILE_NAME, assemblyField(), assemblyTitle());
    }

    private String[] assemblyTitle() {
        return new String[]{
                i18("invalidNumber.entity.phoneNumber"),
        };
    }

    private String[] assemblyField() {
        return new String[]{
                "phoneNumber"
        };
    }


    public String i18(String key) {
        return I18nUtils.getMessage(key);
    }
}
