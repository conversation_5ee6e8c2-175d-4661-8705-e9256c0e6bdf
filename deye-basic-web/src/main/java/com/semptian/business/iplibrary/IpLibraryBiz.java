package com.semptian.business.iplibrary;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.AssemblyCondition;
import com.semptian.entity.IpLibraryEntity;
import com.semptian.param.IpLibraryModel;
import com.semptian.service.IpLibraryService;
import com.semptian.utils.DateUtil;
import com.semptian.utils.IpUtil;
import com.semptian.utils.StringUtilNom;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import static com.semptian.constant.CommonConstant.ONE;
import static com.semptian.constant.CommonConstant.VALID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IpLibraryBiz {

    @Autowired
    private IpLibraryService ipLibraryService;

    public Object update(IpLibraryModel model) {
        IpLibraryEntity ipLibraryEntity = new IpLibraryEntity();
        BeanUtil.copyProperties(model, ipLibraryEntity);
        ipLibraryEntity.setStartIp(String.valueOf(IpUtil.iptolong(model.getStartIp())));
        ipLibraryEntity.setEndIp(String.valueOf(IpUtil.iptolong(model.getEndIp())));
        ipLibraryEntity.setUpdateTime(DateUtil.getSeconds());
        ipLibraryEntity.setStatus(VALID);
        return ipLibraryService.updateById(ipLibraryEntity);
    }

    public Object detail(Long id) {
        IpLibraryEntity entity = ipLibraryService.getById(id);
        entity.setStartIp(IpUtil.longtoip(Long.parseLong(entity.getStartIp())));
        entity.setEndIp(IpUtil.longtoip(Long.parseLong(entity.getEndIp())));
        return entity;
    }

    public Object pageQuery(
            String companyName,
            Integer companyNameFlag,
            String address,
            Integer addressFlag,
            String ipAddr,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> resultMap = Maps.newHashMap();
        QueryWrapper<IpLibraryEntity> entityWrapper = new QueryWrapper<>();
        Page<IpLibraryEntity> page = new Page<>(onPage, size);

        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtilNom.humpToLine(orderField));
        if (orderType == 1) {
            orderItem.setAsc(true);
        } else {
            orderItem.setAsc(false);
        }
        page.addOrder(orderItem);

        if (StringUtils.isNotBlank(companyName)) {
            AssemblyCondition.pageAssemblyCondition(companyNameFlag, "COMPANY_NAME", companyName, entityWrapper);
        }
        if (StringUtils.isNotBlank(address)) {
            AssemblyCondition.pageAssemblyCondition(addressFlag, "ADDRESS", address, entityWrapper);
        }
        if (StringUtils.isNotBlank(ipAddr)) {
            entityWrapper = new QueryWrapper<>();
            entityWrapper.eq("START_IP",String.valueOf(IpUtil.iptolong(ipAddr))).or().eq("END_IP",String.valueOf(IpUtil.iptolong(ipAddr)));
        }

        Page<IpLibraryEntity> pageList = ipLibraryService.page(page, entityWrapper);
        List<IpLibraryEntity> records = pageList.getRecords();
        resultHandle(records);
        int total = ipLibraryService.count(entityWrapper);
        resultMap.put("total", total);
        resultMap.put("resultList", records);
        return resultMap;
    }

    public void resultHandle(List<IpLibraryEntity> result) {
        result.forEach(item -> {
            item.setStartIp(IpUtil.longtoip(Long.parseLong(item.getStartIp())));
            item.setEndIp(IpUtil.longtoip(Long.parseLong(item.getEndIp())));
        });
    }
}
