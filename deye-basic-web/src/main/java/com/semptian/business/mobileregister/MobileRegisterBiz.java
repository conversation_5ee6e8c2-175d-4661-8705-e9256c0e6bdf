package com.semptian.business.mobileregister;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.MobileRegisterEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.service.MobileRegisterService;
import com.semptian.utils.DateUtil;
import com.semptian.utils.StringUtilNom;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.semptian.common.AssemblyCondition.pageAssemblyCondition;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MobileRegisterBiz {

    @Autowired
    private MobileRegisterService mobileRegisterService;

    public Object updateMsg(MobileRegisterEntity registerEntity) {
        if (registerEntity == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        //去重
        QueryWrapper<MobileRegisterEntity> wr = new QueryWrapper<>();
        wr.eq("MSISDN", registerEntity.getMsisdn()).ne("ID", registerEntity.getId());
        int count = mobileRegisterService.count(wr);
        if (count > 0) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("mobile.same.number"));
        }
        registerEntity.setUpdateTime(DateUtil.getSeconds());
        boolean flag = mobileRegisterService.updateById(registerEntity);
        if (flag) {
            return ReturnModel.getInstance().ok().setData(registerEntity.getId());
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
    }

    public Object detailMsg(Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.id"));
        }
        return mobileRegisterService.getById(id);
    }

    public Object selectMsg(
            String msisdn,
            Integer msisdnFlag,
            String imsi,
            Integer imsiFlag,
            String name,
            Integer nameFlag,
            String nowAddress,
            Integer nowAddressFlag,
            Integer residenceType,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> map = Maps.newHashMap();
        List<MobileRegisterEntity> list;
        int count;

        try {
            Page<MobileRegisterEntity> page = new Page<>(onPage, size);

            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(StringUtilNom.humpToLine(orderField));
            if (orderType == 1) {
                orderItem.setAsc(true);
            } else {
                orderItem.setAsc(false);
            }
            page.addOrder(orderItem);

            QueryWrapper<MobileRegisterEntity> wr = new QueryWrapper<>();
            if (StringUtils.isNotEmpty(msisdn)) {
                pageAssemblyCondition(msisdnFlag, "MSISDN", msisdn, wr);
            }
            if (StringUtils.isNotEmpty(imsi)) {
                pageAssemblyCondition(imsiFlag, "IMSI", imsi, wr);
            }
            if (StringUtils.isNotEmpty(name)) {
                pageAssemblyCondition(nameFlag, "NAME", name, wr);
            }
            if (StringUtils.isNotEmpty(nowAddress)) {
                pageAssemblyCondition(nowAddressFlag, "NOW_ADDRESS", nowAddress, wr);
            }
            if (residenceType != null) {
                wr.eq("RESIDENCE_TYPE", residenceType);
            }
            list = mobileRegisterService.page(page, wr).getRecords();
            count = mobileRegisterService.count(wr);
            map.put("total", count);
            map.put("resultList", list);
            return map;
        } catch (Exception e) {
            log.info(e.toString());
            return ReturnModel.getInstance().setMsg(ErrorCodeEnum.SERVICE_EXCEPTION.getMsg()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }
    }
}
