package com.semptian.business.number;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.dto.clue.ClueDto;
import com.semptian.entity.CallClueEntity;
import com.semptian.entity.ClueEntity;
import com.semptian.entity.SpecialNumberEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.NumberStatusEnum;
import com.semptian.enums.PhoneTypeEnum;
import com.semptian.enums.UserPositionEnum;
import com.semptian.feign.portal.PortalInterfaceService;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.NumberExportModel;
import com.semptian.param.PhoneExcelModel;
import com.semptian.param.PhoneNumberModel;
import com.semptian.service.CallClueService;
import com.semptian.service.ClueService;
import com.semptian.service.SpecialNumberService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * @Author: sk
 * @Date: 2020/12/17 11:46
 */
@Service
@Slf4j
public class PhoneNumberBiz {

    @Resource
    private PortalInterfaceService portalInterfaceService;
    @Resource
    private ClueService clueService;
    @Resource
    private SpecialNumberService specialNumberService;
    @Resource
    private CallClueService callClueService;

    private final static Pattern PATTERN = Pattern.compile("[0-9]*");

    private static String ALL_EXPORT = "-1";
    private static int CSV_EXPORT = 0;
    private static int EXCEL_EXPORT = 1;
    private static int TXT_EXPORT = 2;
    private static String FILE_NAME_CSV = "NumWhite.csv";
    private static String FILE_NAME_EXCEL = "NumWhite.xls";
    private static String FILE_NAME_TXT = "NumWhite.txt";

    /**
     * 排序字段
     */
    private static final String PHONE_TYPE = "phoneType";
    private static final String CREATE_TIME = "createTime";
    private static final String PHONE_STATE = "phoneState";
    private static final String FULL_NUM = "fullNum";


    public Object queryPageList(String keyword, int onPage, int size, String orderField, int orderType, Long startTime, Long endTime, Integer phoneType) {

        int startIndex = (onPage - 1) * size;
        int endIndex = onPage * size;
        Map<String, Object> map = Maps.newHashMap();
        String orderFieldName = orderFieldName(orderField);
        try {
            if (phoneType == PhoneTypeEnum.WHOLE.getCode()) {
                int total = clueService.queryWhiteNumberCount(keyword.trim(), startTime, endTime);
                List<ClueDto> phoneList = clueService.queryWhiteNumberList(keyword.trim(), startIndex, endIndex, orderFieldName, orderType, startTime, endTime);
                map.put("total", total);
                map.put("phoneList", phoneList);
            } else if (phoneType == PhoneTypeEnum.ORDINARY.getCode()) {
                int total = clueService.queryOrdinaryNumberCount(keyword.trim(), startTime, endTime);
                List<ClueDto> phoneList = clueService.queryOrdinaryNumberList(keyword.trim(), startIndex, endIndex, orderFieldName, orderType, startTime, endTime);
                map.put("total", total);
                map.put("phoneList", phoneList);
            } else {
                int total = specialNumberService.querySpecialNumberCount(keyword.trim(), startTime, endTime);
                List<ClueDto> phoneList = specialNumberService.querySpecialNumberList(keyword.trim(), startIndex, endIndex, orderFieldName, orderType, startTime, endTime);
                map.put("total", total);
                map.put("phoneList", phoneList);
            }


        } catch (Exception e) {
            log.error("Fail to query number list,error message: {}", e.toString());
        }
        return map;
    }

    /**
     * 校验手机号
     *
     * @param telephoneNum
     * @param phoneType
     * @param countryCode
     * @param id
     * @param
     * @return
     */
    public boolean checkNumberName(String telephoneNum, Integer phoneType, String countryCode, Long id) {

        if (PhoneTypeEnum.ORDINARY.getCode() == phoneType) {
            return clueService.checkOrdinaryNumber(telephoneNum, countryCode, id);
        } else {
            return specialNumberService.checkSpecialNumber(telephoneNum, countryCode, id);
        }

    }

    /**
     * 保存
     *
     * @param phoneNumberModel
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object saveNumber(PhoneNumberModel phoneNumberModel) {

        boolean b = checkNumberName(phoneNumberModel.getTelephoneNum(), phoneNumberModel.getPhoneType(), phoneNumberModel.getCountryCode(), null);
        if (b) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("phonenumber.name.exists"));
        }


        //TODO 调用门户根据用户id查询用户名称
        boolean returnStatus = false;
        String account = "";
        Map<String, Object> data = portalInterfaceService.getUserInfoByUserId(phoneNumberModel.getUserId());
        if (null != data && !data.isEmpty()) {
            account = data.getOrDefault("account", "").toString();
        }

        try {
            if (phoneNumberModel.getPhoneType() == PhoneTypeEnum.ORDINARY.getCode()) {

                ClueEntity clueEntity = new ClueEntity();
                CallClueEntity callClueEntity = new CallClueEntity();
                BeanUtil.copyProperties(phoneNumberModel, callClueEntity);
                BeanUtil.copyProperties(phoneNumberModel, clueEntity);

                Long nextVal = clueService.nextVal();
                clueEntity.setClueId(nextVal);
                clueEntity.setObjectId(-100L);
                clueEntity.setClueName(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                clueEntity.setClueType(200);
                clueEntity.setCreateTime(DateUtil.getSeconds());
                clueEntity.setUpdateTime(DateUtil.getSeconds());
                clueEntity.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
                clueEntity.setDataCount(0L);
                clueEntity.setClueInfo(phoneNumberModel.getRemark());
                clueEntity.setCreateUserId(phoneNumberModel.getUserId());
                clueService.save(clueEntity);

                callClueEntity.setClueId(clueEntity.getClueId());
                callClueEntity.setId(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                callClueEntity.setIdType(4);
                callClueEntity.setCbId(null);
                callClueEntity.setActionType(1L);
                callClueEntity.setKeywordFlag(16);
                returnStatus = callClueService.save(callClueEntity);
            } else {

                SpecialNumberEntity specialNumberEntity = new SpecialNumberEntity();
                Long nextVal = specialNumberService.getNextVal();
                specialNumberEntity.setId(nextVal);
                specialNumberEntity.setPhoneNum(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                specialNumberEntity.setPhoneState(NumberStatusEnum.OFF.getCode());
                specialNumberEntity.setRemark(phoneNumberModel.getRemark());
                specialNumberEntity.setCreatorNm(account);
                specialNumberEntity.setCreateTime(DateUtil.getSeconds());
                specialNumberEntity.setUpdateTime(DateUtil.getSeconds());
                specialNumberEntity.setTelephoneNum(phoneNumberModel.getTelephoneNum());
                specialNumberEntity.setCountryCode(phoneNumberModel.getCountryCode());
                returnStatus = specialNumberService.save(specialNumberEntity);
            }
            if (returnStatus) {
                return ReturnModel.getInstance().ok();
            }
        } catch (Exception e) {
            log.error("Fail to save the number,error message: {}", e.toString());
        }

        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(ErrorCodeEnum.SERVICE_EXCEPTION.getMsg());
    }

    /**
     * 查询号码详情
     *
     * @Author: sk
     * @date: 2020/12/19
     * @params: [phoneType, id, userId]
     */
    public Object queryDetail(Integer phoneType, Long id) {

        try {
            if (phoneType == PhoneTypeEnum.ORDINARY.getCode()) {
                return clueService.queryOrdinaryNumberDetail(id);
            } else {
                return specialNumberService.querySpecialNumberDetail(id);
            }
        } catch (Exception e) {
            log.error("Fail to get detail of the number,error message: {}", e.toString());
        }

        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(ErrorCodeEnum.SERVICE_EXCEPTION.getMsg());
    }

    /**
     * 修改号码
     *
     * @Author: sk
     * @date: 2020/12/19
     * @params: [phoneNumberModel]
     */
    public Object modifyNumber(PhoneNumberModel phoneNumberModel) {


        boolean b = checkNumberName(phoneNumberModel.getTelephoneNum(), phoneNumberModel.getPhoneType(), phoneNumberModel.getCountryCode(), phoneNumberModel.getId());
        if (b) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode()).setMsg(I18nUtils.getMessage("phonenumber.name.exists"));
        }

        //TODO 调用门户根据用户id查询用户名称
        String account = "";
        Map<String, Object> data = portalInterfaceService.getUserInfoByUserId(phoneNumberModel.getUserId());
        if (null != data && !data.isEmpty()) {
            account = data.getOrDefault("account", "").toString();
        }

        try {
            //检查号码是否为停控状态
            if (phoneNumberModel.getPhoneType() == PhoneTypeEnum.ORDINARY.getCode()) {
                ClueDto clueDto = clueService.queryOrdinaryNumberDetail(phoneNumberModel.getId());
                if (null != clueDto) {

                    if (clueDto.getPhoneState() == NumberStatusEnum.ON.getCode()) {

                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("Number status is not control!");
                    }
                    clueService.modifyNumber(phoneNumberModel);
                } else {
                    ClueEntity clueEntity = new ClueEntity();
                    CallClueEntity callClueEntity = new CallClueEntity();
                    BeanUtil.copyProperties(phoneNumberModel, callClueEntity);
                    BeanUtil.copyProperties(phoneNumberModel, clueEntity);

                    Long nextVal = clueService.nextVal();
                    clueEntity.setClueId(nextVal);
                    clueEntity.setObjectId(-100L);
                    clueEntity.setClueName(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                    clueEntity.setClueType(200);
                    clueEntity.setCreateTime(DateUtil.getSeconds());
                    clueEntity.setUpdateTime(DateUtil.getSeconds());
                    clueEntity.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
                    clueEntity.setDataCount(0L);
                    clueEntity.setClueInfo(phoneNumberModel.getRemark());
                    clueEntity.setCreateUserId(phoneNumberModel.getUserId());
                    clueService.save(clueEntity);

                    callClueEntity.setClueId(clueEntity.getClueId());
                    callClueEntity.setId(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                    callClueEntity.setIdType(4);
                    callClueEntity.setCbId(null);
                    callClueEntity.setActionType(1L);
                    callClueEntity.setKeywordFlag(16);
                    callClueService.save(callClueEntity);
                    specialNumberService.getBaseMapper().delete(new QueryWrapper<SpecialNumberEntity>().eq("ID", phoneNumberModel.getId()));
                }

            } else {
                SpecialNumberEntity s = specialNumberService.getById(phoneNumberModel.getId());
                if (null != s) {

                    if (s.getPhoneState() == NumberStatusEnum.ON.getCode()) {

                        return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("Number status is not control!");
                    }
                    SpecialNumberEntity specialNumberEntity = new SpecialNumberEntity();
                    specialNumberEntity.setId(phoneNumberModel.getId());
                    specialNumberEntity.setPhoneNum(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                    specialNumberEntity.setPhoneState(NumberStatusEnum.OFF.getCode());
                    specialNumberEntity.setRemark(phoneNumberModel.getRemark());
                    specialNumberEntity.setCreatorNm(account);
                    specialNumberEntity.setCreateTime(DateUtil.getSeconds());
                    specialNumberEntity.setUpdateTime(DateUtil.getSeconds());
                    specialNumberEntity.setTelephoneNum(phoneNumberModel.getTelephoneNum());
                    specialNumberEntity.setCountryCode(phoneNumberModel.getCountryCode());

                    specialNumberService.update(specialNumberEntity, new QueryWrapper<SpecialNumberEntity>().eq("ID", phoneNumberModel.getId()));

                } else {
                    SpecialNumberEntity specialNumberEntity = new SpecialNumberEntity();
                    Long nextVal = specialNumberService.getNextVal();
                    specialNumberEntity.setId(nextVal);
                    specialNumberEntity.setPhoneNum(phoneNumberModel.getCountryCode() + phoneNumberModel.getTelephoneNum());
                    specialNumberEntity.setPhoneState(NumberStatusEnum.OFF.getCode());
                    specialNumberEntity.setRemark(phoneNumberModel.getRemark());
                    specialNumberEntity.setCreatorNm(account);
                    specialNumberEntity.setCreateTime(DateUtil.getSeconds());
                    specialNumberEntity.setUpdateTime(DateUtil.getSeconds());
                    specialNumberEntity.setTelephoneNum(phoneNumberModel.getTelephoneNum());
                    specialNumberEntity.setCountryCode(phoneNumberModel.getCountryCode());
                    specialNumberService.save(specialNumberEntity);

                    ClueEntity clueEntity = new ClueEntity();
                    clueEntity.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
                    clueService.update(clueEntity, new QueryWrapper<ClueEntity>().eq("CLUEID", phoneNumberModel.getId()));
                    callClueService.getBaseMapper().delete(new QueryWrapper<CallClueEntity>().eq("CLUEID", phoneNumberModel.getId()));
                }

            }

        } catch (Exception e) {
            log.error("Fail to modify the number,error message: {}", e.toString());
        }

        return ReturnModel.getInstance().ok();
    }

    /**
     * 删除号码
     *
     * @Author: sk
     * @date: 2020/12/19
     * @params: [map, userId]
     */
    public Object delete(Map<String, String> map) {

        // 判断状态是否为停控
        String inspectNum = map.get("2");
        String phoneNum = map.get("1");
        //普通
        List<Long> collect1 = Lists.newArrayList();
        //特殊
        List<Long> collect2 = Lists.newArrayList();
        if (inspectNum != null) {
            collect2 = Arrays.stream(inspectNum.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<SpecialNumberEntity> specialNumberEntityList = specialNumberService.getBaseMapper().selectList(new QueryWrapper<SpecialNumberEntity>().in("ID", collect2));
            //特殊
            for (SpecialNumberEntity clueDto : specialNumberEntityList) {
                if (clueDto.getPhoneState() == NumberStatusEnum.ON.getCode()) {
                    return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("status.notDelete"));
                }
                if (clueDto.getPhoneState() == NumberStatusEnum.WAITFORCONTROL.getCode() || clueDto.getPhoneState() == NumberStatusEnum.WAITFORCANCELCONTROL.getCode()) {
                    return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("inProcess.data.cannot.delete"));
                }
            }
        }
        if (phoneNum != null) {
            collect1 = Arrays.stream(phoneNum.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<ClueDto> cd2 = clueService.selectListByClueIds(collect1);
            //普通
            for (ClueDto clueDto : cd2) {
                if (clueDto.getPhoneState() == NumberStatusEnum.ON.getCode()) {
                    return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("status.notDelete"));
                }
                if (clueDto.getPhoneState() == NumberStatusEnum.WAITFORCONTROL.getCode() || clueDto.getPhoneState() == NumberStatusEnum.WAITFORCANCELCONTROL.getCode()) {
                    return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("inProcess.data.cannot.delete"));
                }
            }
        }
        if (inspectNum == null && phoneNum == null) {
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }


        List<Long> ordinaryIds = new ArrayList<>(collect1);
        List<Long> specialIds = new ArrayList<>(collect2);


        try {
            //删除关联表
            if (CollectionUtil.isNotEmpty(ordinaryIds)) {
                callClueService.getBaseMapper().delete(new QueryWrapper<CallClueEntity>().in("CLUEID", ordinaryIds));
                //修改线索状态
                clueService.updateBatchByStatus(ordinaryIds, NumberStatusEnum.TO_BE_DELETED.getCode());
            }

            //删除特殊号码表
            if (CollectionUtil.isNotEmpty(specialIds)) {
                QueryWrapper<SpecialNumberEntity> specialNumberEntityQueryWrapper = new QueryWrapper<>();
                specialNumberEntityQueryWrapper.in("ID", specialIds);
                specialNumberService.getBaseMapper().delete(specialNumberEntityQueryWrapper);
            }
        } catch (Exception e) {
            log.error("Fail to delete the number,error message: {}", e.toString());
        }


        return ReturnModel.getInstance().ok();
    }


    /**
     * 切换号码状态
     *
     * @Author: sk
     * @date: 2020/12/19
     * @params: [clueId, toStatus]
     */
    @Transactional
    public Object updateStatus(PhoneNumberModel phoneNumberModel) {

        Integer toStatus = phoneNumberModel.getToStatus();
        Map<String, String> map = phoneNumberModel.getMap();

        //boolean flag = true;

        List<Long> list = new ArrayList<>();
        List<Long> specialList = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            //根据key判断号码类型
            if (entry.getKey().equals(String.valueOf(PhoneTypeEnum.ORDINARY.getCode()))) {
                if (Objects.nonNull(entry.getValue())) {
                    //遍历value值
                    List<Long> ids = Arrays.stream(entry.getValue().split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    list.addAll(ids);
                    // 判断状态是否为停控
                    List<ClueDto> clueDtos = clueService.selectListByClueIds(list);
                    List<ClueDto> onList = clueDtos.stream().filter(item -> item.getPhoneState() == NumberStatusEnum.ON.getCode()).collect(Collectors.toList());
                    List<ClueDto> offList = clueDtos.stream().filter(item -> item.getPhoneState() == NumberStatusEnum.OFF.getCode()).collect(Collectors.toList());
                    if (toStatus == NumberStatusEnum.ON.getCode()) {
                        if (onList.size() > 0) {

                            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("repeat.state.notDo"));
                        }
                    } else {
                        if (offList.size() > 0) {
                            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("repeat.state.notDo"));
                        }
                    }
                    for (ClueDto clueDto : clueDtos) {

                        Integer phoneState = clueDto.getPhoneState();

                        //更新线索状态
                        if (phoneState == NumberStatusEnum.ON.getCode()) {
                            clueService.updateBatchByStatus(list, NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
                        } else {
                            clueService.updateBatchByStatus(list, NumberStatusEnum.WAITFORCONTROL.getCode());
                        }
                    }
                }
            } else if (entry.getKey().equals(String.valueOf(PhoneTypeEnum.SPECIAL.getCode()))) {
                if (Objects.nonNull(entry.getValue())) {
                    //遍历value值
                    List<Long> ids1 = Arrays.stream(entry.getValue().split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    specialList.addAll(ids1);
                    // 判断状态是否为停控
                    List<SpecialNumberEntity> lists = specialNumberService.getBaseMapper().selectList(new QueryWrapper<SpecialNumberEntity>().in("ID", specialList));

                    List<SpecialNumberEntity> onList = lists.stream().filter(a -> a.getPhoneState() == NumberStatusEnum.ON.getCode()).collect(Collectors.toList());
                    List<SpecialNumberEntity> offList = lists.stream().filter(a -> a.getPhoneState() == NumberStatusEnum.OFF.getCode()).collect(Collectors.toList());

                    if (toStatus == NumberStatusEnum.ON.getCode()) {
                        if (onList.size() > 0) {

                            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("repeat.state.notDo"));
                        }
                    } else {
                        if (offList.size() > 0) {
                            return ReturnModel.getInstance().setCode(ErrorCodeEnum.DATA_STATUS_NOT_MET.getCode()).setMsg(I18nUtils.getMessage("repeat.state.notDo"));
                        }
                    }
                    //更新特殊号码表
                    specialNumberService.updateBatchByStatus(specialList, toStatus);
                }
            }
        }


        return ReturnModel.getInstance().ok();
    }


    /**
     * 号码的导出
     *
     * @Author: sk
     * @date: 2020/12/21
     * @params: [fileType, ids, userId]
     */
    @SuppressWarnings(value = "all")
    public Object numberExport(Integer fileType, String ids, String sids, Long userId, HttpServletResponse response) throws Exception {

        UserPositionEnum position = portalInterfaceService.getUserPositionByUserId(userId);
        if (position == null) {
            return ReturnModel.getInstance().error().setMsg("Position not exist");
        }
        if (Objects.isNull(ids) || Objects.isNull(sids)) {
            return ReturnModel.getInstance().error();
        }

        //获取全部字段
        if (ids.equals(ALL_EXPORT) || sids.equals(ALL_EXPORT)) {
            List<ClueDto> clueDtoList = clueService.selectAllExport();
            if (fileType == CSV_EXPORT) {
                //生成名称
                String exportName = ExportName(FILE_NAME_CSV);
                //设置导出字段
                File file = csvFile(clueDtoList, null, exportName);
                //压缩文件
                exportToCsv(response, file, exportName);
                //异步删除保存在项目的文件
                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();

            } else {
                String exportName;
                //Excel格式
                if (fileType == EXCEL_EXPORT) {
                    exportName = ExportName(FILE_NAME_EXCEL);
                    File file = excelFile(clueDtoList, null, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();

                } else {
                    exportName = ExportName(FILE_NAME_TXT);
                    File file = txtFile(clueDtoList, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();

                }
            }
        } else if (ids.length() > 0 && sids.length() < 1) {
            //获取普通号码Ids集合
            List<Long> numberIds = Arrays.stream(ids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<ClueDto> clueDtoList = clueService.selectOrdinaryByIdsExport(numberIds);
            if (fileType == CSV_EXPORT) {
                String exportName = ExportName(FILE_NAME_CSV);
                File file = csvFile(clueDtoList, null, exportName);
                exportToCsv(response, file, exportName);
                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();

            } else {
                String exportName;
                if (fileType == EXCEL_EXPORT) {
                    exportName = ExportName(FILE_NAME_EXCEL);
                    File file = excelFile(clueDtoList, null, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                } else {
                    exportName = ExportName(FILE_NAME_TXT);
                    File file = txtFile(clueDtoList, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                }
            }
        } else if (!ids.equals(ALL_EXPORT) && !sids.equals(ALL_EXPORT) && ids.length() > 0 && sids.length() > 0) {
            List<Long> numberIds = Arrays.stream(ids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<Long> specialIds = Arrays.stream(sids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<ClueDto> clueDtoList1 = clueService.selectOrdinaryByIdsExport(numberIds);
            List<ClueDto> clueDtoList2 = specialNumberService.selectOrdinaryByIdsExport(specialIds);
            List<ClueDto> clueDtos = Lists.newArrayList();
            clueDtos.addAll(clueDtoList1);
            clueDtos.addAll(clueDtoList2);

            if (fileType == CSV_EXPORT) {
                String exportName = ExportName(FILE_NAME_CSV);
                File file = csvFile(clueDtos, null, exportName);
                exportToCsv(response, file, exportName);
                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();

            } else {
                String exportName;
                if (fileType == EXCEL_EXPORT) {
                    exportName = ExportName(FILE_NAME_EXCEL);
                    File file = excelFile(clueDtos, null, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                } else {
                    exportName = ExportName(FILE_NAME_TXT);
                    File file = txtFile(clueDtos, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                }
            }
        } else {
            //获取特殊号码Ids集合
            List<Long> specialIds = Arrays.stream(sids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<ClueDto> clueDtoList = specialNumberService.selectOrdinaryByIdsExport(specialIds);
            if (fileType == CSV_EXPORT) {
                String exportName = ExportName(FILE_NAME_CSV);
                File file = csvFile(clueDtoList, null, exportName);
                exportToCsv(response, file, exportName);

                ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                service.schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (FileUtils.deleteQuietly(file)) {
                            log.info("file :{} is deleted.", file.getName());
                        }
                    }
                }, 30, TimeUnit.SECONDS);
                return ReturnModel.getInstance().ok();

            } else {
                String exportName;
                if (fileType == EXCEL_EXPORT) {
                    exportName = ExportName(FILE_NAME_EXCEL);
                    File file = excelFile(clueDtoList, null, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();
                } else {
                    exportName = ExportName(FILE_NAME_TXT);
                    File file = txtFile(clueDtoList, exportName);
                    exportToCsv(response, file, exportName);

                    ScheduledExecutorService service = ThreadPoolUtil.getScheduledThreadPoolInstance();
                    service.schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.deleteQuietly(file)) {
                                log.info("file :{} is deleted.", file.getName());
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    return ReturnModel.getInstance().ok();

                }
            }
        }

    }

    /**
     * 导入
     *
     * @param file
     * @param userId
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings(value = "all")
    public Object importBatchNum(MultipartFile file, Integer userId, Integer type) {

        List<ClueEntity> cList = Lists.newArrayList();
        List<CallClueEntity> callList = Lists.newArrayList();
        List<SpecialNumberEntity> spList = Lists.newArrayList();
        String account = "";
        Map<String, Object> data = portalInterfaceService.getUserInfoByUserId(userId);
        if (null != data && !data.isEmpty()) {
            account = data.getOrDefault("account", "").toString();
        }
        try {
            ExcelUtil<PhoneExcelModel> excelUtil = new ExcelUtil<>(PhoneExcelModel.class);
            List<PhoneExcelModel> excelModelList = excelUtil.importExcel(file.getInputStream(), type);
            if (CollectionUtils.isEmpty(excelModelList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.isEmpty"));
            }
            //文档去重
            Set<PhoneExcelModel> set = new TreeSet<>(Comparator.comparing(PhoneExcelModel::getTelephoneNum));
            set.addAll(excelModelList);
            for (PhoneExcelModel phoneExcelModel : set) {
                if (StringUtils.isEmpty(phoneExcelModel.getCountryCode()) || StringUtils.isEmpty(phoneExcelModel.getTelephoneNum()) || (phoneExcelModel.getTelephoneNum() + phoneExcelModel.getCountryCode()).length() > 30 || (phoneExcelModel.getTelephoneNum() + phoneExcelModel.getCountryCode()).length() < 4 || StringUtils.isEmpty(phoneExcelModel.getPhoneType())) {
                    continue;
                }
                if (phoneExcelModel.getPhoneType() != 1 && phoneExcelModel.getPhoneType() != 2) {
                    log.warn("phone type error, phone type:{},phoneExcelModel:{}", phoneExcelModel.getPhoneType(), JSON.toJSONString(phoneExcelModel));
                    continue;
                }
                if (!PATTERN.matcher(phoneExcelModel.getTelephoneNum()).matches()) {
                    log.warn("phone telephone error, telephone:{},phoneExcelModel:{}", phoneExcelModel.getTelephoneNum(), JSON.toJSONString(phoneExcelModel));
                    continue;
                }
                //号码类型判断
                if (phoneExcelModel.getPhoneType() == 1) {
                    QueryWrapper<CallClueEntity> wrapper = new QueryWrapper<>();
                    wrapper.eq("TELEPHONE_Num", phoneExcelModel.getTelephoneNum());
                    int i = callClueService.count(wrapper);
                    if (i > 0) {
                        continue;
                    }
                    //线索组装
                    ClueEntity ce = new ClueEntity();
                    Long nextVal = clueService.nextVal();
                    ce.setClueId(nextVal);
                    ce.setObjectId(-100L);
                    ce.setClueName(phoneExcelModel.getCountryCode() + phoneExcelModel.getTelephoneNum());
                    ce.setClueType(200);
                    ce.setCreateTime(DateUtil.getSeconds());
                    ce.setUpdateTime(DateUtil.getSeconds());
                    ce.setClueState(NumberStatusEnum.WAITFORCANCELCONTROL.getCode());
                    ce.setDataCount(0L);
                    ce.setClueInfo(phoneExcelModel.getRemark());
                    ce.setCreateUserId(Long.valueOf(userId));
                    cList.add(ce);

                    //电话组装
                    CallClueEntity callClueEntity = new CallClueEntity();
                    callClueEntity.setClueId(ce.getClueId());
                    callClueEntity.setId(phoneExcelModel.getCountryCode() + phoneExcelModel.getTelephoneNum());
                    callClueEntity.setIdType(4);
                    callClueEntity.setCbId(2L);
                    callClueEntity.setActionType(1L);
                    callClueEntity.setKeywordFlag(16);
                    callList.add(callClueEntity);

                } else {
                    QueryWrapper<SpecialNumberEntity> wrapper = new QueryWrapper<>();
                    wrapper.eq("TELEPHONE_NUM", phoneExcelModel.getTelephoneNum());
                    int i = specialNumberService.count(wrapper);
                    if (i > 0) {
                        continue;
                    }
                    //特殊号码组装
                    SpecialNumberEntity specialNumberEntity = new SpecialNumberEntity();
                    Long nextVal = specialNumberService.getNextVal();
                    specialNumberEntity.setId(nextVal);
                    specialNumberEntity.setPhoneNum(phoneExcelModel.getCountryCode() + phoneExcelModel.getTelephoneNum());
                    specialNumberEntity.setPhoneState(NumberStatusEnum.OFF.getCode());
                    specialNumberEntity.setRemark(phoneExcelModel.getRemark());
                    specialNumberEntity.setCreatorNm(account);
                    specialNumberEntity.setCreateTime(DateUtil.getSeconds());
                    specialNumberEntity.setUpdateTime(DateUtil.getSeconds());
                    specialNumberEntity.setTelephoneNum(phoneExcelModel.getTelephoneNum());
                    specialNumberEntity.setCountryCode(phoneExcelModel.getCountryCode());
                    spList.add(specialNumberEntity);
                }

            }
            //插入线索表
            if (CollectionUtils.isEmpty(cList) && CollectionUtils.isEmpty(spList)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage("import.white.content.isFalse"));
            }
            int i = 0;
            if (!CollectionUtils.isEmpty(cList)) {
                for (ClueEntity clueEntity : cList) {
                    boolean insert = clueService.save(clueEntity);
                    if (!insert) {
                        i++;
                    }
                }
                //插入电话表
                if (i == 0) {
                    for (CallClueEntity callClueEntity : callList) {
                        boolean insert = callClueService.save(callClueEntity);
                        if (!insert) {
                            i++;
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(spList)) {
                //插入特殊号码表
                if (i == 0) {
                    for (SpecialNumberEntity specialNumberEntity : spList) {
                        boolean insert = specialNumberService.save(specialNumberEntity);
                        if (!insert) {
                            i++;
                        }
                    }
                }
            }

            if (i == 0) {
                return ReturnModel.getInstance().ok();
            }
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.white.isException"));

        } catch (Exception e) {
            log.error(e.toString());
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.white.isException"));
        }
    }


    /**
     * 导出目标文件
     *
     * @param response 通过response对象输出流
     * @param file     目标文件
     */
    @SuppressWarnings(value = "all")
    private void exportToCsv(HttpServletResponse response, File file, String fName) throws IOException {
        ZipOutputStream zipos;
        DataOutputStream os = null;
        FileInputStream in = null;
        zipos = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()));
        //设置压缩方法
        zipos.setMethod(ZipOutputStream.DEFLATED);
        try {
            File file1;
            file1 = new File(file.getCanonicalPath());
            zipos.putNextEntry(new ZipEntry(fName));
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fName, "UTF-8"));
            response.setContentType("application/octet-stream");
            os = new DataOutputStream(zipos);
            in = new FileInputStream(file1);
            IOUtils.copy(in, os);
        } catch (IOException e) {
            log.warn("file is not exists");
            log.error("exportToCsv error: {}. error msg {}.", e, e.getMessage());
        } finally {
            if (in != null) {
                in.close();
            }
            if (os != null) {
                os.close();
            }
            zipos.close();
        }
    }


    /**
     * CSV导出设置
     *
     * @param list
     * @param filePath
     * @param fileName
     */
    private File csvFile(List<ClueDto> list, String filePath, String fileName) {
        /**
         * 国际化表头
         */
        String TYPE = I18nUtils.getMessage("phoneTypeStr");
        String TIME = I18nUtils.getMessage("updateTime2");
        String STATE = I18nUtils.getMessage("phoneStateStr");
        String NUM = I18nUtils.getMessage("fullNum");
        LinkedHashMap map = new LinkedHashMap<>();
        map.put("fullNum", NUM);
        map.put("phoneTypeStr", TYPE);
        map.put("phoneStateStr", STATE);
        map.put("updateTime", TIME);

        List<NumberExportModel> modelList = new ArrayList<>();
        for (ClueDto clueDto : list) {
            NumberExportModel c = new NumberExportModel();
            if (clueDto.getPhoneType() == 1) {
                clueDto.setPhoneTypeStr(I18nUtils.getMessage("phonenumber.name.ordinary"));
            } else {
                clueDto.setPhoneTypeStr(I18nUtils.getMessage("phonenumber.name.special"));

            }
            if (clueDto.getPhoneState() == NumberStatusEnum.OFF.getCode()) {
                clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
            } else if (clueDto.getPhoneState() == NumberStatusEnum.ON.getCode()) {
                clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
            } else {
                clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
            }

            c.setFullNum(clueDto.getFullNum() + "\t");
            c.setPhoneTypeStr(clueDto.getPhoneTypeStr());
            c.setPhoneStateStr(clueDto.getPhoneStateStr());
            String time = "\t" + DateUtil.formatTimeSecond(clueDto.getUpdateTime()) + "\t";
            c.setUpdateTime(time);
            modelList.add(c);

        }
        return CsvUtils.createCsvFile(modelList, map, filePath, fileName);

    }


    /**
     * Excel导出设置
     *
     * @param list
     * @param filePath
     * @param fileName
     * @return
     * @throws Exception
     */
    private File excelFile(List<ClueDto> list, String filePath, String fileName) throws Exception {
        /**
         * 国际化表头
         */
        String TYPE = I18nUtils.getMessage("phoneTypeStr");
        String TIME = I18nUtils.getMessage("updateTime2");
        String STATE = I18nUtils.getMessage("phoneStateStr");
        String NUM = I18nUtils.getMessage("fullNum");
        String[] colum = {NUM, TYPE, STATE, TIME};
        String[] arr = {"fullNum", "phoneTypeStr", "phoneStateStr", "updateTime"};

        List<NumberExportModel> modelList = new ArrayList<>();

        for (ClueDto clueDto : list) {
            NumberExportModel c = new NumberExportModel();
            if (clueDto.getPhoneType() == 1) {
                clueDto.setPhoneTypeStr(I18nUtils.getMessage("phonenumber.name.ordinary"));
            } else {
                clueDto.setPhoneTypeStr(I18nUtils.getMessage("phonenumber.name.special"));

            }
            if (clueDto.getPhoneState() == NumberStatusEnum.OFF.getCode()) {
                clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
            } else if (clueDto.getPhoneState() == NumberStatusEnum.ON.getCode()) {
                clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
            } else {
                clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
            }

            c.setFullNum(clueDto.getFullNum() + "\t");
            c.setPhoneTypeStr(clueDto.getPhoneTypeStr());
            c.setPhoneStateStr(clueDto.getPhoneStateStr());
            c.setUpdateTime(DateUtil.formatTimeSecond(clueDto.getUpdateTime()));
            modelList.add(c);
        }
        File excel = ExcelCreateUtils.createExcel(modelList, arr, colum, fileName, filePath);
        return excel;
    }


    /**
     * 生成文件名
     *
     * @param name
     * @return
     */
    public static String ExportName(String name) {
        Date date = new Date();
        SimpleDateFormat yyyyMMddHHmmss = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = yyyyMMddHHmmss.format(date);
        String fileName = format + name;
        return fileName;
    }

    /**
     * TXT文本导出设置
     *
     * @param list
     * @param fileName
     * @return
     */
    private File txtFile(List<ClueDto> list, String fileName) {
        /**
         * 国际化表头
         */
        String TYPE = I18nUtils.getMessage("phoneTypeStr");
        String TIME = I18nUtils.getMessage("updateTime2");
        String STATE = I18nUtils.getMessage("phoneStateStr");
        String NUM = I18nUtils.getMessage("fullNum");

        File outFile = new File(fileName);
        Writer writer = null;
        try {
            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile, true), StandardCharsets.UTF_8), 10240);
            writer.write(NUM + "\t" + "\t" +
                    TYPE + "\t" + "\t" +
                    STATE + "\t" + "\t" +
                    TIME + "\r\n");

            NumberExportModel c = new NumberExportModel();
            for (ClueDto clueDto : list) {

                if (clueDto.getPhoneType() == 1) {
                    clueDto.setPhoneTypeStr(I18nUtils.getMessage("phonenumber.name.ordinary"));
                } else {
                    clueDto.setPhoneTypeStr(I18nUtils.getMessage("phonenumber.name.special"));

                }
                if (clueDto.getPhoneState() == NumberStatusEnum.OFF.getCode()) {
                    clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.stopcontrol"));
                } else if (clueDto.getPhoneState() == NumberStatusEnum.ON.getCode()) {
                    clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.incontrol"));
                } else {
                    clueDto.setPhoneStateStr(I18nUtils.getMessage("phonenumber.status.inProcess"));
                }
                String s = DateUtil.formatTimeSecond(clueDto.getUpdateTime());
                c.setUpdateTime(s);
                writer.write(clueDto.getFullNum() + "\t" + "\t" +
                        clueDto.getPhoneTypeStr() + "\t" + "\t" +
                        clueDto.getPhoneStateStr() + "\t" + "\t" +
                        c.getUpdateTime() + "\r\n");
            }
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("writer close error");
                }
            }

        }
        return outFile;
    }


    public String orderFieldName(String orderField) {

        if (null != orderField) {
            if (orderField.equals(PHONE_STATE)) {
                return PHONE_STATE;
            }
            if (orderField.equals(PHONE_TYPE)) {
                return PHONE_TYPE;
            }
            if (orderField.equals(CREATE_TIME)) {
                return CREATE_TIME;
            }
            if (orderField.equals(FULL_NUM)) {
                return FULL_NUM;
            }

        }
        return CREATE_TIME;

    }

}


