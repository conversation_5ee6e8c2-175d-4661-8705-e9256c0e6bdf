package com.semptian.business.numbersection;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.AssemblyCondition;
import com.semptian.utils.ExportUtils;
import com.semptian.dto.numbersection.NumberSectionDto;
import com.semptian.entity.NumberSectionEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import com.semptian.param.NumberSectionModel;
import com.semptian.service.NumberSectionService;
import com.semptian.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NumberSectionBiz {

    @Autowired
    private NumberSectionService numberSectionService;

    private static final String FILE_NAME = "NUMBER_SECTION_";

    public Object save(NumberSectionModel model) {

        if (numberSectionService.checkMsisdnExist(model.getId(), model.getMsisdn())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("mobile.same.number"));
        }

        NumberSectionEntity entity = new NumberSectionEntity();
        BeanUtil.copyProperties(model, entity);
        entity.setUpdateTime(DateUtil.getSeconds());
        entity.setStatus(VALID);
        if (model.getId() == null) {
            //新增
            entity.setId(numberSectionService.getNextVal());
            entity.setCreateTime(DateUtil.getSeconds());
            numberSectionService.save(entity);
        } else {
            //修改
            numberSectionService.updateById(entity);
        }
        return ReturnModel.getInstance().ok();
    }

    public Object detail(Long id) {
        return numberSectionService.getById(id);
    }

    public Object delete(DelModel model) {
        return numberSectionService.removeByIds(model.getIds());
    }

    public Object pageQuery(
            String cityName,
            Integer cityNameFlag,
            String netType,
            Integer netTypeFlag,
            String msisdn,
            Integer msisdnFlag,
            String postCode,
            Integer postCodeFlag,
            Integer onPage,
            Integer size,
            String orderField,
            Integer orderType
    ) {
        Map<String, Object> resultMap = Maps.newHashMap();
        QueryWrapper<NumberSectionEntity> entityWrapper = new QueryWrapper<>();

        Page<NumberSectionEntity> page = new Page<>(onPage, size);

        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtilNom.humpToLine(orderField));
        if (orderType == 1) {
            orderItem.setAsc(true);
        } else {
            orderItem.setAsc(false);
        }
        page.addOrder(orderItem);

        if (StringUtils.isNotBlank(cityName)) {
            AssemblyCondition.pageAssemblyCondition(cityNameFlag, "CITY_NAME", cityName, entityWrapper);
        }
        if (StringUtils.isNotBlank(netType)) {
            AssemblyCondition.pageAssemblyCondition(netTypeFlag, "NET_TYPE", netType, entityWrapper);
        }
        if (StringUtils.isNotBlank(msisdn)) {
            AssemblyCondition.pageAssemblyCondition(msisdnFlag, "MSISDN", msisdn, entityWrapper);
        }
        if (StringUtils.isNotBlank(postCode)) {
            AssemblyCondition.pageAssemblyCondition(postCodeFlag, "POST_CODE", postCode, entityWrapper);
        }

        Page<NumberSectionEntity> pageList = numberSectionService.page(page, entityWrapper);
        int total = numberSectionService.count(entityWrapper);
        resultMap.put("total", total);
        resultMap.put("resultList", pageList.getRecords());
        return resultMap;
    }

    public void export(ExportModel model, HttpServletResponse response) {

        QueryWrapper<NumberSectionEntity> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(model.getIds())) {
            List<Long> ids = Stream.of(model.getIds()).map(Long::parseLong).collect(Collectors.toList());
            wrapper.in("ID", ids);
        }
        List<NumberSectionEntity> list = numberSectionService.getBaseMapper().selectList(wrapper);
        List<NumberSectionDto> dtoList = Lists.newArrayList();
        list.forEach(item -> {
                    NumberSectionDto dto = new NumberSectionDto();
                    BeanUtil.copyProperties(item, dto);
                    String isp = null;
                    if (item.getIspCode() != null) {
                        if (ONE == item.getIspCode()) {
                            isp = I18nUtils.getMessage("isp.type1");
                        } else if (NINETY_NINE == item.getIspCode()) {
                            isp = I18nUtils.getMessage("isp.type2");
                        }
                    }
                    dto.setIsp(isp);
                    dtoList.add(dto);
                }
        );
        ExportUtils.export(model, dtoList, response, FILE_NAME, assemblyField(), assemblyTitle());
    }

    private String[] assemblyTitle() {
        return new String[]{
                i18("numberSection.entity.msisdn"),
                i18("numberSection.entity.cityCode"),
                i18("numberSection.entity.cityName"),
                i18("numberSection.entity.netType"),
                i18("numberSection.entity.postCode"),
                i18("numberSection.entity.areaCode"),
                i18("numberSection.entity.isp"),
        };
    }

    private String[] assemblyField() {
        return new String[]{
                "msisdn",
                "cityCode",
                "cityName",
                "netType",
                "postCode",
                "areaCode",
                "isp",
        };
    }

    public String i18(String key) {
        return I18nUtils.getMessage(key);
    }

}
