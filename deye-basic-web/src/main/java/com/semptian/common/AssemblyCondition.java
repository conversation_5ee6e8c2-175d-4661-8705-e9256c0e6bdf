package com.semptian.common;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;

import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 */
public class AssemblyCondition {

    public static <T> void pageAssemblyCondition(int queryType, String keyword, String value,QueryWrapper<T> wrapper) {
        if (queryType == PREFIX_MATCH) {
            wrapper.likeLeft(StringUtils.isNotBlank(value), "lower(" + keyword + ")", StringUtils.lowerCase(value));
        } else if (queryType == SUFFIX_MATCH) {
            wrapper.likeRight(StringUtils.isNotBlank(value), "lower(" + keyword + ")", StringUtils.lowerCase(value));
        } else if (queryType == FUZZY_MATCH) {
            wrapper.like(StringUtils.isNotBlank(value), "lower(" + keyword + ")", StringUtils.lowerCase(value));
        } else {
            wrapper.eq(StringUtils.isNotBlank(value), "lower(" + keyword + ")", StringUtils.lowerCase(value));
        }

    }
}
