package com.semptian.common.aop;

import cn.hutool.core.collection.CollectionUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.annotation.AuthCheckAnno;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.UserPositionEnum;
import com.semptian.feign.portal.PortalFeignClient;
import com.semptian.i18n.I18nUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date: 2020/9/28 15:29
 * Description:
 */
@Component
@Aspect
public class AuthCheckAspect {

    @Autowired
    private PortalFeignClient portalFeignClient;

    @Around("@annotation(authCheckAnno)")
    public Object doAround(ProceedingJoinPoint point,AuthCheckAnno authCheckAnno) {
        boolean checkPermission = authCheckAnno.isCheckPermission();
        boolean checkUserId = authCheckAnno.isCheckUserId();

        Object[] args = point.getArgs();
        HttpHeaders httpHeaders = (HttpHeaders) args[0];

        if (checkUserId) {
            if (checkUserId(httpHeaders)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
            }
        }
        if (checkPermission) {
            if (checkPermission(httpHeaders)) {
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PERMISSION_NO_ACCESS.getCode()).setMsg(I18nUtils.getMessage("common.user.nopermission"));
            }
        }
        try {
            return point.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return ReturnModel.getInstance().error();
    }

    private boolean checkPermission(HttpHeaders httpHeaders) {
        if (checkUserId(httpHeaders)) {
            return true;
        }
        ReturnModel returnModel = portalFeignClient.queryPositionByUserId(httpHeaders.get("userId").get(0));
        if (returnModel.getCode() != 1 || returnModel.getData() == null) {
            return true;
        }
        List<Map<String, Object>> mapList = (List<Map<String, Object>>) returnModel.getData();
        return CollectionUtil.isEmpty(mapList) || (int) mapList.get(0).get("id") == UserPositionEnum.USER.getCode();
    }

    private boolean checkUserId(HttpHeaders httpHeaders) {
        List<String> headers = httpHeaders.get("userId");
        return CollectionUtil.isEmpty(headers);
    }

}
