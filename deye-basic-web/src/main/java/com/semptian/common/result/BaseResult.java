package com.semptian.common.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;

/**
 * 接口返回的基本对象
 *
 * <AUTHOR>
 * @date 2020-08-21 09:40
 **/
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class BaseResult implements Serializable {

    private static final long serialVersionID = 1L;

    /** 返回状态码 */
    @NonNull
    private Integer code;

    /** 返回结果信息 */
    @NonNull
    private String message;

    /** 返回结果数据 */
    private Object data;

}

