package com.semptian.common.result;

import lombok.AllArgsConstructor;

/**
 * 返回的结果
 *
 * <AUTHOR>
 * @date 2020-08-21 09:42
 **/
@AllArgsConstructor
public enum CommonReturnCode implements ReturnCode {

    FAILED(0, "响应失败"),
    SUCCESS(1, "响应成功");

    private Integer code;
    private String message;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

}
