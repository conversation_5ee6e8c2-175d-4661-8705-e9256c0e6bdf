package com.semptian.common.result;

/**
 * 接口返回对象
 *
 * <AUTHOR>
 * @date 2020-08-21 09:41
 **/
public class Result extends BaseResult {

    public Result(ReturnCode returnCode) {
        super(returnCode.getCode(), returnCode.getMessage());
    }

    public Result(ReturnCode returnCode, Object data) {
        super(returnCode.getCode(), returnCode.getMessage(), data);
    }

    public Result(Integer code, String message) {
        super(code, message);
    }

}

