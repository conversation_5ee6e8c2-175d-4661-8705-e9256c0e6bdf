package com.semptian.config.system;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * Swagger 配置
 *
 * @Author: sk
 * @date: 2020/12/15
 * @params:
 */

@EnableSwagger2
@Configuration
public class SwaggerConfig {

    @Bean
    public Docket createRestApi() {
        ParameterBuilder userIdPar = new ParameterBuilder();
        ParameterBuilder appIdPar = new ParameterBuilder();
        ParameterBuilder langPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        userIdPar.name("userId").description("用户id").modelRef(new ModelRef("string")).parameterType("header").required(false).build();
        appIdPar.name("appId").description("登录授权id").modelRef(new ModelRef("string")).defaultValue("102").parameterType("header").required(false).build();
        langPar.name("lang").description("语言种类").modelRef(new ModelRef("string")).parameterType("header").required(false).build();
        pars.add(userIdPar.build());
        pars.add(appIdPar.build());
        pars.add(langPar.build());

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.semptian.controller"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(pars);
    }

    private ApiInfo apiInfo() {

        return new ApiInfoBuilder()
                .title("基础库接口规范")
                .description("本界面为\"案件系统V1.0\"开发接口规范 如有疑问 请联系*******************")
                .termsOfServiceUrl("")
                .version("V1.0")
                .build();
    }

}
