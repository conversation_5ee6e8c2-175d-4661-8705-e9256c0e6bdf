package com.semptian.controller;

import cn.hutool.core.io.FileUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.ClueIpFilterTableEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.UpdateIpModel;
import com.semptian.service.ClueIpFilterTableService;
import com.semptian.utils.CsvUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * @author: ZC
 * @date: 2020/12/18 10:06
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/ip_white")
@Api(tags = "ClueIpFilterTableController", value = "ip白名单")
@Slf4j
public class ClueIpFilterTableController {

    private static final String USER_ID_KEY = "userId";

    @Autowired
    private ResourceLoader resourceLoader;

    @Resource
    private ClueIpFilterTableService clueIpFilterTableService;

    @PostMapping("/save.json")
    @ApiOperation(value = "新增IP白名单", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object saveIpWhite(@RequestBody ClueIpFilterTableEntity clueIpFilterTableEntity, HttpServletRequest request) {

        String header = request.getHeader(USER_ID_KEY);
        log.info("current user:{}", header);
        if (!StringUtils.isEmpty(header)) {
            Integer userId = Integer.valueOf(header);
            return clueIpFilterTableService.insertIpWhite(clueIpFilterTableEntity, userId);
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
    }

    @DeleteMapping("/delete.json")
    @ApiOperation(value = "删除IP白名单", httpMethod = "DELETE")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object deleteIpWhite(String ids) {
        if (!StringUtils.isEmpty(ids)) {
            return clueIpFilterTableService.deleteIpWhite(ids);
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
    }

    @PutMapping("/modify.json")
    @ApiOperation(value = "编辑IP白名单", httpMethod = "PUT")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clueId", value = "IP地址", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "ipAddr", value = "IP地址", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ipMask", value = "MAC地址", required = true, dataType = "String", paramType = "query"),
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object checkIpWhite(@RequestParam(value = "clueId", required = true, defaultValue = "") Integer clueId,
                               @RequestParam(value = "ipAddr", required = true, defaultValue = "") String ipAddr,
                               @RequestParam(value = "ipMask", required = true, defaultValue = "") String ipMask,
                               @RequestBody ClueIpFilterTableEntity clueIpFilterTableEntity,
                               HttpServletRequest request) {
        String header = request.getHeader(USER_ID_KEY);
        log.info("current user:{}", header);
        if (!StringUtils.isEmpty(header)) {
            Integer userId = Integer.valueOf(header);
            clueIpFilterTableEntity.setUserId(userId);
            return clueIpFilterTableService.updateIpWhiteMsg(clueIpFilterTableEntity);
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
    }

    @PutMapping("/update_status.json")
    @ApiOperation(value = "修改IP白名单状态", httpMethod = "PUT")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object updateIpWhiteState(@RequestBody UpdateIpModel updateIpModel) {
        if (!StringUtils.isEmpty(updateIpModel)) {
            return clueIpFilterTableService.updateIpWhiteState(updateIpModel);
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));

    }

    @GetMapping("/query.json")
    @ApiOperation(value = "查询IP白名单", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ipAddr", value = "IP地址", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "起始时间", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "当前页，默认为1", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页数量，默认为10", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "orderType", value = "排序默认为0", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "orderField", value = "C2.UPDATE_TIME", dataType = "String", paramType = "query")
    })
    @OperateLog
    public Object selectAllIpWhite(
            @RequestParam(value = "ipAddr", required = false, defaultValue = "") String ipAddr,
            @RequestParam(name = "startTime", defaultValue = "", required = false) Long startTime,
            @RequestParam(name = "endTime", defaultValue = "", required = false) Long endTime,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType,
            @RequestParam(value = "orderField", required = false, defaultValue = "c2.UPDATETIME") String orderField) {
        try {
            return clueIpFilterTableService.selectIpWhiteMsg(ipAddr, startTime, endTime, onPage, size, orderField, orderType);
        } catch (Exception e) {
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("parameters.of.the.abnormal"));
        }

    }

    @GetMapping("/ip_exist.json")
    @ApiOperation(value = "校验IP和MAC", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object regexIpAndMask(String ipAddr, String clueId) {
        return clueIpFilterTableService.ipWhiteCheck(ipAddr, clueId);
    }

    @GetMapping("/mask_exist.json")
    @ApiOperation(value = "校验MAC", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object regexMask(String ipMask) {
        return clueIpFilterTableService.maskCheck(ipMask);
    }


    @GetMapping("/detail.json")
    @ApiOperation(value = "查看IP白名单详情", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object detailIpAllMsg(Integer clueId) {
        if (!StringUtils.isEmpty(clueId)) {
            return clueIpFilterTableService.selectIpById(clueId);
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
    }

    @GetMapping("/export.json")
    @ApiOperation(value = "导出IP白名单", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object exportIpWhite(String ids, Integer type, HttpServletResponse response) {
        if (!StringUtils.isEmpty(ids)) {
            return clueIpFilterTableService.ipMsgExport(ids, type, response);
        }
        return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
    }

    @PostMapping("/batch_import.json")
    @ApiOperation(value = "IP白名单批量导入", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object batchImportTest(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request) {
        String curUserId = request.getHeader(USER_ID_KEY);
        log.info("current user: {}", curUserId);
        String lang = request.getParameter("lang");
        Integer integer = 0;
        if (StringUtils.isEmpty(lang)) {
            integer = 0;
        }else {
            if (lang.equals("zh_CN")) {
                integer = 1;
            } else if (lang.equals("fr_DZ")) {
                integer = 2;
            } else if (lang.equals("en_US")) {
                integer = 3;
            }
        }
        if (!StringUtils.isEmpty(curUserId)) {
            Integer userId = Integer.valueOf(curUserId);
            return clueIpFilterTableService.importBatch(file, userId,integer);
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
    }




    @ApiOperation(value = "IP白名单导入模板下载", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })

    @RequestMapping(value = "/template_download.json", method = RequestMethod.GET)
    @OperateLog
    public Object downloadTemplateFile(HttpServletResponse response, HttpServletRequest request) {

        InputStream inputStream = null;

        try {
            String lang = request.getParameter("lang");
            String path = "";
            if (StringUtils.isEmpty(lang)) {
                path = "classpath:file/IP白名单导入模板.xlsx";
            }else {
                if (lang.equals("zh_CN")) {
                    path = "classpath:file/IP白名单导入模板.xlsx";
                } else if (lang.equals("fr_DZ")) {
                    //path = "classpath:file/Modèle d'importation de plomb.xls";
                    path = "classpath:file/IPWhiteModel-fr.xlsx";
                } else if (lang.equals("en_US")) {
                    path = "classpath:file/IPWhiteModel.xlsx";
                }
            }

            inputStream = resourceLoader.getResource(path).getInputStream();
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }


    }

}
