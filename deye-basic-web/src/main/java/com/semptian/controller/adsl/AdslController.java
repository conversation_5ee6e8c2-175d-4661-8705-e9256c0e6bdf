package com.semptian.controller.adsl;


import cn.hutool.core.collection.CollectionUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.business.adsl.AdslBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.AdslModel;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import static com.semptian.constant.CommonConstant.FOUR;


/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/adsl")
@Api(tags = "ADSL注册信息")
@Slf4j
public class AdslController {

    @Autowired
    private AdslBiz adslBiz;

    @ApiOperation(value = "ADSL保存", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/save.json")
    public Object save(@RequestBody @ApiParam(name = "adslModel", value = "实体json对象", required = true) AdslModel adslModel) {
        if (StringUtils.isBlank(adslModel.getOnlineAccount())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.onlineAccount"));
        }
        if (StringUtils.isBlank(adslModel.getIdentityCardNumber())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.identityCardNumber"));
        }
        if (StringUtils.isBlank(adslModel.getAccountHolderName())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.accountHolderName"));
        }
        if (StringUtils.isBlank(adslModel.getAssemblyPhone())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.assemblyPhone"));
        }
        if (StringUtils.isBlank(adslModel.getCityCode())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.cityCode"));
        }
        return adslBiz.save(adslModel);
    }

    @ApiOperation(value = "ADSL详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detail(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.id"));
        }
        return adslBiz.detail(id);
    }

    @ApiOperation(value = "ADSL删除", httpMethod = "DELETE", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @DeleteMapping("/delete.json")
    public Object delete(@RequestBody DelModel model) {
        if (CollectionUtil.isEmpty(model.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.id"));
        }
        return adslBiz.delete(model);
    }

    @ApiOperation(value = "ADSL分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "onlineAccount", required = false) String onlineAccount,
            @RequestParam(value = "onlineAccountFlag", required = false, defaultValue = "3") Integer onlineAccountFlag,
            @RequestParam(value = "identityCardNumber", required = false) String identityCardNumber,
            @RequestParam(value = "identityCardNumberFlag", required = false, defaultValue = "3") Integer identityCardNumberFlag,
            @RequestParam(value = "accountHolderName", required = false) String accountHolderName,
            @RequestParam(value = "accountHolderNameFlag", required = false, defaultValue = "3") Integer accountHolderNameFlag,
            @RequestParam(value = "assemblyPhone", required = false) String assemblyPhone,
            @RequestParam(value = "assemblyPhoneFlag", required = false, defaultValue = "3") Integer assemblyPhoneFlag,
            @RequestParam(value = "installationPosition", required = false) String installationPosition,
            @RequestParam(value = "installationPositionFlag", required = false, defaultValue = "3") Integer installationPositionFlag,
            @RequestParam(value = "accountHolderPhone", required = false) String accountHolderPhone,
            @RequestParam(value = "accountHolderPhoneFlag", required = false, defaultValue = "3") Integer accountHolderPhoneFlag,
            @RequestParam(value = "longitude", required = false) String longitude,
            @RequestParam(value = "latitude", required = false) String latitude,
            @RequestParam(value = "address", required = false) String address,
            @RequestParam(value = "addressFlag", required = false, defaultValue = "3") Integer addressFlag,
            @RequestParam(value = "ispCode", required = false) Integer ispCode,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType

    ) {
        return adslBiz.pageQuery(
                onlineAccount,
                onlineAccountFlag,
                identityCardNumber,
                identityCardNumberFlag,
                accountHolderName,
                accountHolderNameFlag,
                assemblyPhone,
                assemblyPhoneFlag,
                installationPosition,
                installationPositionFlag,
                accountHolderPhone,
                accountHolderPhoneFlag,
                longitude,
                latitude,
                address,
                addressFlag,
                ispCode,
                onPage,
                size,
                orderField,
                orderType
        );
    }

    @ApiOperation(value = "ADSL数据导出", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/export.json")
    public void export(
            @RequestBody ExportModel model,
            HttpServletResponse response
    ) {
        if(model.getExportType() == null){
            model.setExportType(FOUR);
        }
        adslBiz.export(model,response);
    }
}
