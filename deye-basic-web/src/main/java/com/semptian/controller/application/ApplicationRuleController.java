package com.semptian.controller.application;

import cn.hutool.core.io.FileUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.AppRuleModel;
import com.semptian.param.AutoUpdateModel;
import com.semptian.service.AppHotService;
import com.semptian.service.ApplicationRuleService;
import com.semptian.service.BaseRuleConfigService;
import com.semptian.utils.CsvUtils;
import com.semptian.utils.IpUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

import static com.semptian.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @date 2021-09-10 16:05
 **/
@RestController
@CrossOrigin
@Slf4j
@RequestMapping("/app_rule")
public class ApplicationRuleController {

    @Resource
    private ApplicationRuleService applicationRuleService;

    @Resource
    private AppHotService appHotService;

    @Resource
    private BaseRuleConfigService baseRuleConfigService;

    @Resource
    private ResourceLoader resourceLoader;

    /**
     * 查询应用知识库列表
     *
     * @return Object
     */
    @ApiOperation(value = "查询应用知识库列表", notes = "查询应用知识库列表")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query.json")
    @OperateLog
    public Object query(
                        @RequestParam(name = "keyword",required = false, defaultValue = "") String keyword,
                        @RequestParam(name = "ruleName", required = false, defaultValue = "") String ruleName,
                        @RequestParam(name = "status", required = false,defaultValue = "-1") Integer status,
                        @RequestParam(name = "queryRange", required = false) List<Integer> queryRange,
                        @RequestParam(name = "orderType", required = false,defaultValue = "1") Integer orderType,
                        @RequestParam(name = "orderField", required = false,defaultValue = "name") String orderField,
                        @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
                        @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage,
                        @RequestParam(name = "lang", required = false, defaultValue = "zh_CN") String lang){


        return  applicationRuleService.queryAppRuleList(keyword,ruleName,status,size,onPage,orderField,orderType,queryRange,lang);
    }

    /**
     * 查询指定应用是否为热门
     *
     * @return Object
     */
    @ApiOperation(value = "查询指定应用是否为热门", notes = "查询指定应用是否为热门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appName", required = true, value = "应用名称"),
            @ApiImplicitParam(name = "matchRule", value = "匹配规则, 0-完整匹配,1-被包含匹配,2-包含匹配, 如Youtube Video Upload为热门, 1-传入Youtube也属于热门, 2-传入Youtube Video Upload Geek也属于热门"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/is_hot.json")
    @OperateLog
    public Object query(@RequestParam(name = "appName") String appName,
                        @RequestParam(name = "matchRule", required = false, defaultValue = "0,1") List<Integer> matchRule) {

        return appHotService.isHot(appName, matchRule);
    }

    /**
     * 根据id查询对应的IP信息
     *
     * @return Object
     */
    @ApiOperation(value = "根据id查询对应的IP信息", notes = "根据id查询对应的IP信息")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_ip_list.json")
    @OperateLog
    public Object queryIpList(@RequestParam(name = "id") String id,
                              @RequestParam(name = "size", required = false, defaultValue = "1000") Integer size,
                              @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage) {

        return applicationRuleService.queryIpList(id, size, onPage);
    }


    /**
     * 新增应用知识库
     *
     * @return Object
     */
    @ApiOperation(value = "新增应用知识库", notes = "新增应用知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/add_app_rule.json")
    @OperateLog
    public Object addDomainRule( HttpServletRequest request,
                                 @RequestBody AppRuleModel model){

        String userId = getUserId(request);
        if(userId==null){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        if (StringUtils.isEmpty(model.getName())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("name.cannot.null"));
        }
        if (model.getName().length() > 128) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("app.max.length"));
        }

        //校验IP是否合法
        if(StringUtils.isNotBlank(model.getIp())){
            String[] split = model.getIp().split(",");
            if(!IpUtil.checkIPSAndPort(split)){
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("IP_CHECK"));
            }
        }else{
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.cannot.null"));
        }

        return  applicationRuleService.addAppRule(model.getName(),model.getDescription(),model.getIp(),model.getAppType(),userId);
    }

    private String getUserId( HttpServletRequest request) {
        String userId = request.getHeader("userId");
        if (StringUtils.isBlank(userId)) {
            userId = request.getParameter("userId");
        }
        return userId;
    }

    /**
     * 修改应用知识库
     *
     * @return Object
     */
    @ApiOperation(value = "修改应用知识库", notes = "修改应用知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update_app_rule.json")
    @OperateLog
    public Object updateDomainRule(@RequestBody AppRuleModel model,  HttpServletRequest request){
        String userId = getUserId(request);

        if (StringUtils.isEmpty(model.getName())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("name.cannot.null"));
        }
        if (model.getName().length() > 128) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("app.max.length"));
        }
        if (model.getId()==null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("id can not be null");
        }
        //校验IP是否合法
        if(StringUtils.isNotBlank(model.getIp())){
            String[] split = model.getIp().split(",");
            if(!IpUtil.checkIPSAndPort(split)){
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("IP_CHECK"));
            }
        }else{
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.cannot.null"));
        }

        return applicationRuleService.updateAppRule(model.getId(), model.getName(), model.getDescription(), model.getIp(), model.getAppType(), userId);
    }

    /**
     * 删除应用知识库
     *
     * @return Object
     */
    @ApiOperation(value = "删除应用知识库", notes = "删除应用知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @DeleteMapping("/delete_app_rule.json")
    @OperateLog
    public Object deleteDomainRule(@RequestParam(name = "ids") String ids){


        return  applicationRuleService.deleteAppRule(ids);
    }

    /**
     * 导入应用知识库
     *
     * @return Object
     */
    @ApiOperation(value = "导入应用知识库", notes = "导入应用知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/import_app_rule.json")
    @OperateLog
    public Object importAppRules(@ApiParam(name = "file", value = "导入的文件对象") MultipartFile file, HttpServletRequest request, HttpServletResponse response){
        //获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename) || (!originalFilename.endsWith(XLS) && !originalFilename.endsWith(XLSX))) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("file.type.error"));
        }
        String lang = request.getParameter("lang");
        if (StringUtils.isBlank(lang)) {
            lang = LANG_EN;
        }
        String userId = getUserId(request);
        if(userId==null){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        return applicationRuleService.importAppRules(file, lang, userId, response);
    }

    /**
     *  查询应用类型
     *
     * @return Object
     */
    @ApiOperation(value = "查询应用类型", notes = "查询应用类型")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/get_app_type_list.json")
    public Object getAppTypeList(){
        return applicationRuleService.getAppTypeList();
    }


    /**
     *  模板下载
     *
     * @return Object
     */
    @ApiOperation(value = "模板下载", notes = "模板下载")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/template_download.json")
    @OperateLog
    public Object downloadTemplateFile(HttpServletResponse response, HttpServletRequest request){
        String lang = request.getParameter("lang");

        String path = "";
        if (StringUtils.isEmpty(lang)) {
            path = "classpath:template/AppRule/ApplicationRule.xlsx";
        }else {
            switch (lang) {
                case "zh_CN":
                    path = "classpath:template/AppRule/ApplicationRule.xlsx";
                    break;
                case "fr_DZ":
                    path = "classpath:template/AppRule/ApplicationRule-fr.xlsx";
                    break;
                case "en_US":
                    path = "classpath:template/AppRule/ApplicationRule-en.xlsx";
                    break;
            }
        }

        try(InputStream inputStream = resourceLoader.getResource(path).getInputStream()) {
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     *  查询自动更新配置
     *
     * @return Object
     */
    @ApiOperation(value = "查询自动更新配置", notes = "查询自动更新配置")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_auto_update_status.json")
    public Object queryAutoUpdateStatus(){
        return ReturnModel.getInstance().ok();
    }

    /**
     * 修改应用知识库自动更新状态
     *
     * @return Object
     */
    @ApiOperation(value = "修改应用知识库自动更新状态", notes = "修改应用知识库自动更新状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status", value = "自动更新状态 0-关闭,1-开启")
    })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update_auto_update_status.json")
    @OperateLog
    public Object updateAutoUpdateStatus(@RequestBody AutoUpdateModel model) {

        if (model.getStatus() == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("status can not be null");
        }
        if (model.getStatus() != CommonConstant.TRUE && model.getStatus() != CommonConstant.FALSE) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("status value is illegal");
        }

        return baseRuleConfigService.updateAutoUpdateStatus(CommonConstant.APP_RULE_TYPE, model.getStatus());
    }

}
