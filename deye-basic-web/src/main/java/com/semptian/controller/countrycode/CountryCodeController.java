package com.semptian.controller.countrycode;

import cn.hutool.core.collection.CollectionUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.business.countrycode.CountryCodeBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.CountryCodeModel;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import static com.semptian.constant.CommonConstant.FOUR;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/country_code")
@Api(tags = "国家代码")
@Slf4j
public class CountryCodeController {

    @Autowired
    private CountryCodeBiz countryCodeBiz;

    @ApiOperation(value = "国家代码保存", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/save.json")
    public Object save(@RequestBody @ApiParam(name = "adslModel", value = "实体json对象", required = true) CountryCodeModel model) {
        if (StringUtils.isBlank(model.getCountryCode())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("countryCode.lack.code"));
        }
        return countryCodeBiz.save(model);
    }

    @ApiOperation(value = "国家代码详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detail(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("countryCode.lack.id"));
        }
        return countryCodeBiz.detail(id);
    }

    @ApiOperation(value = "国家代码删除", httpMethod = "DELETE", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @DeleteMapping("/delete.json")
    public Object delete(@RequestBody DelModel model) {
        if (CollectionUtil.isEmpty(model.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("countryCode.lack.id"));
        }
        return countryCodeBiz.delete(model);
    }

    @ApiOperation(value = "国家代码分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "countryCode", required = false) String countryCode,
            @RequestParam(value = "countryCodeFlag", required = false, defaultValue = "3") Integer countryCodeFlag,
            @RequestParam(value = "countryName", required = false) String countryName,
            @RequestParam(value = "countryNameFlag", required = false, defaultValue = "3") Integer countryNameFlag,
            @RequestParam(value = "countryEnName", required = false) String countryEnName,
            @RequestParam(value = "countryEnNameFlag", required = false, defaultValue = "3") Integer countryEnNameFlag,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType

    ) {
        return countryCodeBiz.pageQuery(
                countryCode,
                countryCodeFlag,
                countryName,
                countryNameFlag,
                countryEnName,
                countryEnNameFlag,
                onPage,
                size,
                orderField,
                orderType
        );
    }

    @ApiOperation(value = "国家代码导出", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/export.json")
    public void export(
            @RequestBody ExportModel model,
            HttpServletResponse response
    ) {
        if(model.getExportType() == null){
            model.setExportType(FOUR);
        }
        countryCodeBiz.export(model,response);
    }
}
