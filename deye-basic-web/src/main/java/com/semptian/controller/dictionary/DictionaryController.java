package com.semptian.controller.dictionary;

import com.semptian.base.service.ReturnModel;
import com.semptian.entity.DictionaryEntity;
import com.semptian.entity.I18nDataModel;
import com.semptian.entity.I18nKeywordModel;
import com.semptian.entity.PageQueryModel;
import com.semptian.service.DictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: SunQi
 * @create: 2021/01/04
 * desc: 国际化字典服务接口
 **/
@RestController
@CrossOrigin
@RequestMapping("/dictionary")
public class DictionaryController {

    @Autowired
    DictionaryService dictionaryService;

    /**
     * 新增或修改字典数据
     *
     * @param dictionaryEntity
     * @return
     */
    @RequestMapping(value = "/add_or_update.json", method = RequestMethod.POST)
    public ReturnModel addOrUpdate(@RequestBody DictionaryEntity dictionaryEntity) {
        return dictionaryService.addOrUpdateDictionary(dictionaryEntity);
    }

    /**
     * 根据lang、type、model_type、code查询国际化值,多个code用逗号隔开
     *
     * @param pageQueryModel
     * @return
     */
    @RequestMapping(value = "/get_i18n_value.json", method = RequestMethod.GET)
    public ReturnModel getValue(PageQueryModel pageQueryModel) {
        return dictionaryService.queryI18nValue(pageQueryModel);
    }

    /**
     * 根据关键字模糊查询国际化值
     *
     * @param pageQueryModel
     * @return
     */
    @RequestMapping(value = "/get_value_by_keyword.json", method = RequestMethod.GET)
    public ReturnModel getValueByKeyword(PageQueryModel pageQueryModel) {
        return dictionaryService.queryI18nValueByKeyword(pageQueryModel);
    }


    /**
     * 获取国际化后的返回值数据
     *
     * @return
     */
    @RequestMapping(value = "/get_i18n_data.json", method = RequestMethod.POST)
    public Object getI18nData(
            @RequestBody I18nDataModel model
    ) {
        return dictionaryService.getI18nData(model);
    }

    /**
     * 获取国际化后的keyword对应的值
     */
    @RequestMapping(value = "/get_i18n_value_by_keyword.json", method = RequestMethod.POST)
    public ReturnModel getI18nValueByKeyword(@RequestBody I18nKeywordModel keywordModel) {

        return dictionaryService.getI18nValueByKeyword(keywordModel);
    }
}
