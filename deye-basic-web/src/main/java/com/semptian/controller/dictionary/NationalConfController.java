package com.semptian.controller.dictionary;

import com.semptian.base.service.ReturnModel;
import com.semptian.service.NationalConfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: <PERSON><PERSON>i
 * @create: 2021/01/08
 * desc:
 **/
@RestController
@CrossOrigin
@RequestMapping("/national")
public class NationalConfController {
    @Autowired
    NationalConfService nationalConfService;

    @RequestMapping("/list_url.json")
    public ReturnModel getUrlList() {
        return nationalConfService.getUrlList();
    }

    @GetMapping("/get_keyword_by_url.json")
    public ReturnModel getKeywordByUrl(@RequestParam("url") String url) {

        return nationalConfService.getKeywordsByUrl(url);
    }
}
