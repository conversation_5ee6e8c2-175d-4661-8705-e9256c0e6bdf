package com.semptian.controller.domain;

import cn.hutool.core.io.FileUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.AutoUpdateModel;
import com.semptian.param.DomainQueryIpModel;
import com.semptian.param.DomainRuleModel;
import com.semptian.service.BaseRuleConfigService;
import com.semptian.service.DomainHotService;
import com.semptian.service.DomainRuleService;
import com.semptian.utils.CsvUtils;
import com.semptian.utils.IpUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

import static com.semptian.constant.CommonConstant.LANG_EN;
import static com.semptian.constant.CommonConstant.XLSX;

/**
 * <AUTHOR>
 * @date 2021-09-09 18:17
 **/
@RestController
@CrossOrigin
@Slf4j
@RequestMapping("/domain_rule")
public class DomainRuleController {

    @Resource
    private DomainRuleService domainRuleService;

    @Resource
    private BaseRuleConfigService baseRuleConfigService;

    @Resource
    private DomainHotService domainHotService;

    @Resource
    private ResourceLoader resourceLoader;

    @ApiOperation(value = "查询域名知识库列表", notes = "查询域名知识库列表")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "queryIp", value = "ip关键字", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "queryRange", value = "查询范围，1：自动更新数据，2：用户导入数据", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orderType", value = "排序类型，0：降序，1：升序", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "orderField", value = "排序字段，默认update_time", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "页面size，默认20", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "页码，默认1", dataType = "int", paramType = "query")
    })
    @GetMapping("/query_new.json")
    @OperateLog
    public Object query(@RequestParam(name = "keyword", required = false,defaultValue = "") String keyword,
                        @RequestParam(name = "queryIp", required = false, defaultValue = "") String queryIp,
                        @RequestParam(name = "queryRange", required = false) Integer queryRange,
                        @RequestParam(name = "orderType", required = false,defaultValue = "1") Integer orderType,
                        @RequestParam(name = "orderField", required = false,defaultValue = "name") String orderField,
                        @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
                        @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage) {

        return domainRuleService.newQueryDomainList(keyword, queryIp, size, onPage, orderField, orderType,queryRange);
    }

    /**
     * 查询指定域名是否为热门
     *
     * @return Object
     */
    @ApiOperation(value = "查询指定域名是否为热门", notes = "查询指定域名是否为热门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "domain", required = true, value = "域名名称"),
            @ApiImplicitParam(name = "matchRule", value = "匹配规则, 0-完整匹配,1-被包含匹配,2-包含匹配, 如itunes.apple.com为热门, 1-传入apple.com也属于热门, 2-传入123.itunes.apple.com也属于热门"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/is_hot.json")
    @OperateLog
    public Object query(@RequestParam(name = "domain") String domain,
                        @RequestParam(name = "matchRule", required = false, defaultValue = "0,1") List<Integer> matchRule) {

        return domainHotService.isHot(domain, matchRule);
    }

    /**
     * 根据id查询对应的IP信息
     *
     * @return Object
     */
    @ApiOperation(value = "根据id查询对应的IP信息", notes = "根据id查询对应的IP信息")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_ip_list.json")
    @OperateLog
    public Object queryIpList(@RequestParam(name = "id") String id,
                              @RequestParam(name = "size", required = false, defaultValue = "1000") Integer size,
                              @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage) {
        return domainRuleService.queryIpList(id, size, onPage);
    }



    /**
     * 新增域名知识库
     *
     * @return Object
     */
    @ApiOperation(value = "新增域名知识库", notes = "新增域名知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/add_domain_rule.json")
    @OperateLog
    public Object addDomainRule(@RequestBody DomainRuleModel model, HttpServletRequest request) {
        String userId = getUserId(request);
        if (userId == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        if (StringUtils.isEmpty(model.getName())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("domain.cannot.null"));
        }

        if (model.getName().length() > 255) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("domain.max.length"));
        }

        if(StringUtils.isNotBlank(model.getIp())){
            String[] split = model.getIp().split(",");
            boolean b = IpUtil.checkIP(split);
            if(!b){
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("IP_CHECK"));
            }
        }else{
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.cannot.null"));
        }

        return domainRuleService.addDomainRule(model.getName(), model.getIp(), model.getType(), userId);
    }

    private String getUserId(HttpServletRequest request) {
        String userId = request.getHeader("userId");
        if (StringUtils.isBlank(userId)) {
            userId = request.getParameter("userId");
        }
        return userId;
    }


    /**
     * 修改域名知识库状态
     *
     * @return Object
     */
    @ApiOperation(value = "修改域名知识库状态", notes = "修改域名知识库状态")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/change_status.json")
    @OperateLog
    public Object changeStatus(@RequestParam(name = "id") String id,
                                   @RequestParam(name = "status") Integer status) {

        if (id==null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("id can not be null");
        }
        return domainRuleService.changeStatus(id,status);
    }

    /**
     * 修改域名知识库状态
     *
     * @return Object
     */
    @ApiOperation(value = "修改域名知识库", notes = "修改域名知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update_domain_rule.json")
    @OperateLog
    public Object updateDomainRule(@RequestBody DomainRuleModel model, HttpServletRequest request) {
        String userId = getUserId(request);

        if (StringUtils.isEmpty(model.getName())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("domain.cannot.null"));
        }
        if (model.getName().length() > 255) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("domain.max.length"));
        }
        if (model.getId()==null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("id can not be null");
        }
        if(StringUtils.isNotBlank(model.getIp())){
            String[] split = model.getIp().split(",");
            boolean b = IpUtil.checkIP(split);
            if(!b){
                return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("IP_CHECK"));
            }
        }else{
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.cannot.null"));
        }

        return domainRuleService.updateDomainRule(model.getId(), model.getName(), model.getIp(), model.getType(), userId);
    }


    /**
     * 删除域名知识库
     *
     * @return Object
     */
    @ApiOperation(value = "删除域名知识库", notes = "删除域名知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @DeleteMapping("/delete_domain_rule.json")
    @OperateLog
    public Object deleteDomainRule(@RequestParam("ids") String ids) {
        return domainRuleService.deleteDomainRule(ids);
    }

    /**
     * 导入域名知识库
     *
     * @return Object
     */
    @ApiOperation(value = "导入域名知识库", notes = "导入域名知识库")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/import_domain_rules.json")
    @OperateLog
    public Object importDomainRules(@ApiParam(name = "file", value = "导入的文件对象") MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        //获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename) || !originalFilename.endsWith(XLSX)) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("file.type.error"));
        }
        String lang = request.getParameter("lang");
        if (StringUtils.isBlank(lang)) {
            lang = LANG_EN;
        }
        String userId = getUserId(request);
        if (userId == null) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        return domainRuleService.importDomainRules(file, lang, userId, response);
    }



    /**
     *  模板下载
     *
     * @return Object
     */
    @ApiOperation(value = "模板下载", notes = "模板下载")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/template_download.json")
    @OperateLog
    public Object downloadTemplateFile(HttpServletResponse response, HttpServletRequest request){
        String lang = request.getParameter("lang");
        String path = "";
        if (org.springframework.util.StringUtils.isEmpty(lang)) {
            path = "classpath:template/DomainRule/DomainRule.xlsx";
        }else {
            switch (lang) {
                case "zh_CN":
                    path = "classpath:template/DomainRule/DomainRule.xlsx";
                    break;
                case "fr_DZ":
                    path = "classpath:template/DomainRule/DomainRule-fr.xlsx";
                    break;
                case "en_US":
                    path = "classpath:template/DomainRule/DomainRule-en.xlsx";
                    break;
            }
        }

        try(InputStream inputStream = resourceLoader.getResource(path).getInputStream() ) {
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     *  查询自动更新配置
     *
     * @return Object
     */
    @ApiOperation(value = "查询自动更新配置", notes = "查询自动更新配置")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_auto_update_status.json")
    public Object queryAutoUpdateStatus(){
        return ReturnModel.getInstance().ok();
    }

    /**
     * 修改域名知识库自动更新状态
     *
     * @return Object
     */
    @ApiOperation(value = "修改域名知识库自动更新状态", notes = "修改域名知识库自动更新状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status", value = "自动更新状态 0-关闭,1-开启")
    })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update_auto_update_status.json")
    @OperateLog
    public Object updateAutoUpdateStatus(@RequestBody AutoUpdateModel model) {

        if (model.getStatus() == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("status can not be null");
        }
        if (model.getStatus() != CommonConstant.TRUE && model.getStatus() != CommonConstant.FALSE) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg("status value is illegal");
        }

        return baseRuleConfigService.updateAutoUpdateStatus(CommonConstant.DOMAIN_RULE_TYPE, model.getStatus());
    }

    /**
     * 根据domain名称集合查询对应的IP信息
     *
     * @return Object
     */
    @ApiOperation(value = "根据domain名称查询对应的IP信息", notes = "根据id集合查询对应的IP信息")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/query_ip_by_names.json")
    public Object queryIpByNameList(@RequestBody DomainQueryIpModel model) {
        return domainRuleService.queryIpByNameList(model.getNameList());
    }
}
