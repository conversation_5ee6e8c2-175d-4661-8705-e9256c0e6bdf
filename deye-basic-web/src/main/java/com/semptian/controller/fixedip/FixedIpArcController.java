package com.semptian.controller.fixedip;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.entity.FixedIpEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.feign.archive.ArchiveFeignClient;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.ArcInfoModel;
import com.semptian.service.FixedIpService;
import com.semptian.utils.ArcIdUtil;
import com.semptian.utils.CurrentUserUtil;
import com.semptian.utils.IPv6ParseUtil;
import com.semptian.utils.IpUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.semptian.constant.CommonConstant.XLSX;
/**
 * @author: lmz
 * @description: 固定IP档案访问路由
 * @date: 2022/8/30 15:29
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/fixed_ip")
@Slf4j
@Api
public class FixedIpArcController {

    @Resource
    private FixedIpService fixedIpService;

    @Autowired
    private ArchiveFeignClient archiveFeignClient;

    /**
     * 已创建固定IP三个model类 全部FixedIpModel、个人FixedIpPersonalModel、政企FixedIpEnterpriseModel在service层
     *
     * @param fixedIpEntity
     * @param request
     * @return
     */

    @PostMapping("/save_ip.json")
    @ApiOperation(value = "固定IP知识库新增功能", httpMethod = "POST", response = ReturnModel.class)
    public ReturnModel insertIpDatabaseInfo(
            @RequestBody FixedIpEntity fixedIpEntity,
            HttpServletRequest request
    ) {
        String userId = CurrentUserUtil.getUserId(request);
        String lang = CurrentUserUtil.getLang(request);
        String ipName = fixedIpEntity.getIpName();
        String ipAddress = fixedIpEntity.getIpAddress();
        fixedIpEntity.setOriginalIpAddress(ipAddress);
        String individualName = fixedIpEntity.getIndividualName();
        String phoneNum = fixedIpEntity.getPhoneNum();
        if (StringUtils.isBlank(userId)) {
            log.warn("request /fixed_ip/save_ip.json userId empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("userId param is blank");
        }
        if (StringUtils.isBlank(lang)) {
            log.warn("request /fixed_ip/update_arc_ip.json lang empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("lang param is blank");
        }
        if (StringUtils.isBlank(ipName)) {
            log.warn("request /fixed_ip/save_ip.json ip name empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("ip name param is blank");
        }
        if (StringUtils.isBlank(ipAddress)) {
            log.warn("request /fixed_ip/save_ip.json ip address empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("ip address param is blank");
        }
        if (StringUtils.isBlank(individualName)) {
            log.warn("request /fixed_ip/save_ip.json individual name empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("individual name param is blank");
        }
        if (ObjectUtil.isNull(fixedIpEntity.getEntityType())){
            log.warn("request /fixed_ip/update_arc_ip.json ipAddress empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("entityType param is blank");
        }

        if (!IpUtil.isipv4(ipAddress)){
            if (ipAddress.matches(IPv6ParseUtil.IPv6Reg)){
                String shortIPv6 = IPv6ParseUtil.getShortIPv6(ipAddress).toLowerCase();
                fixedIpEntity.setIpAddress(shortIPv6);
            }else {
                log.error("ip address is error,and ip is {}",ipAddress);
                return ReturnModel.getInstance().setCode(500).setMsg("ipAddress param is error");
            }
        }

        if (StringUtils.isNotBlank(phoneNum)) {
            boolean matches = phoneNum.matches("^[0-9]*$");
            if (!matches) {
                log.warn("request /fixed_ip/save_ip.json phone num is not a number!");
                return ReturnModel.getInstance().setCode(500).setMsg("phone num param is not a number");
            }
        }

        return fixedIpService.saveFixedIp(fixedIpEntity,lang);
    }

    @GetMapping("/save_ip.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ipAddress", value = "待修复的ipAddress", dataType = "String", required = true, paramType = "query")
    })
    @ApiOperation(value = "固定IP知识库新增确认ip是否会关联历史数据", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel repairIpDatabaseInfo(
            @RequestParam(name = "ipAddress") String ipAddress,
            HttpServletRequest request
    ){
        return fixedIpService.repairIpDatabaseInfo(ipAddress);
    }

    @PostMapping("/delete_batch_ip.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ipAddress", value = "", dataType = "String", required = true, paramType = "query")
    })
    @ApiOperation(value = "固定IP删除功能", httpMethod = "POST", response = ReturnModel.class)
    public ReturnModel deleteIpDatabaseInfo(@RequestBody FixedIpEntity fixedIpEntity,
            HttpServletRequest request
    ) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /fixed_ip/delete_batch_ip.json userId empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("userId param is blank");
        }
        String ipAddress = fixedIpEntity.getIpAddress();
        if (StringUtils.isBlank(ipAddress)){
            log.warn("request /fixed_ip/delete_batch_ip.json ipAddress empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("ipAddress param is blank");
        }
        return fixedIpService.deleteBatchIp(ipAddress);
    }

    @PostMapping("/update_arc_ip.json")
    @ApiOperation(value = "固定IP更新功能", httpMethod = "POST", response = ReturnModel.class)
    public ReturnModel updateArcIpInfo(@RequestBody FixedIpEntity fixedIpEntity,
                                       HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        String lang = CurrentUserUtil.getLang(request);
        String phoneNum = fixedIpEntity.getPhoneNum();
        if (StringUtils.isBlank(userId)) {
            log.warn("request /fixed_ip/update_arc_ip.json userId empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("userId param is blank");
        }
        if (StringUtils.isBlank(lang)) {
            log.warn("request /fixed_ip/update_arc_ip.json lang empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("lang param is blank");
        }
        String ipAddress = fixedIpEntity.getIpAddress();
        if (StringUtils.isBlank(ipAddress)){
            log.warn("request /fixed_ip/update_arc_ip.json ipAddress empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("ip address param is blank");
        }
        if (ObjectUtil.isNull(fixedIpEntity.getEntityType())){
            log.warn("request /fixed_ip/update_arc_ip.json ipAddress empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("entityType param is blank");
        }
        if (StringUtils.isNotBlank(phoneNum)) {
            boolean matches = phoneNum.matches("^[0-9]*$");
            if (!matches) {
                log.warn("request /fixed_ip/update_arc_ip.json phone num is not a number!");
                return ReturnModel.getInstance().setCode(500).setMsg("phone num param is not a number");
            }
        }
        return fixedIpService.updateArcIp(fixedIpEntity,lang);
    }


    @GetMapping("/fixed_ip_list.json")
    @ApiOperation(value = "固定IP列表查询功能", httpMethod = "GET", response = ReturnModel.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "entityType", value = "实体类型，0：政企，1：个人，2: 全部 默认2", dataType = "Integer", required = true,defaultValue = "2", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "当前页，默认1", dataType = "Integer", required = true,defaultValue = "1", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "分页数，默认20", dataType = "Integer", required = true,defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "deleteStatus", value = "是否查询已删除IP，0：不查，1：查，默认是 0", dataType = "Integer", required = true,defaultValue = "0", paramType = "query"),
            @ApiImplicitParam(name = "sortField", value = "默认是IP入库时间，支持modifyTime排序", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "sortType", value = "默认是倒序，1：倒序，0：正序", dataType = "Integer", required = false, paramType = "query"),
            @ApiImplicitParam(name = "archiveStatus", value = "根据建档状态进行查询0：已建档，1：建档失败，2：建档中，3：已删除，4：删除中，5：删除失败；默认不传查所有", dataType = "Integer", required = false, paramType = "query")
    })
    public ReturnModel fixedIpListInfo(
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "entityType",defaultValue = "2") Integer entityType,
            @RequestParam(name = "onPage",defaultValue = "1") Integer onPage,
            @RequestParam(name = "size",defaultValue = "20") Integer size,
            @RequestParam(name = "deleteStatus",defaultValue = "0") Integer deleteStatus,
            @RequestParam(name = "sortField", required = false,defaultValue = "MODIFY_TIME") String sortField,
            @RequestParam(name = "sortType", required = false,defaultValue = "1") Integer sortType,
            @RequestParam(name = "archiveStatus", required = false) Integer archiveStatus,
            HttpServletRequest request
    ) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /fixed_ip/fixed_ip_list.json userId empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("userId param is blank");
        }
        return fixedIpService.fixedIpList(userId,onPage,size,deleteStatus,sortField,sortType,entityType,keyword,archiveStatus);
    }

    @PostMapping("/fixed_ip_model.json")
    @ApiOperation(value = "导入模板下载", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    public void fixedIpModelInfo(
            @RequestBody FixedIpEntity fixedIpEntity,
            HttpServletRequest request,
            HttpServletResponse response
    ){
        String lang = CurrentUserUtil.getLang(request);
        Integer entityType = fixedIpEntity.getEntityType();
        if (ObjectUtil.isNull(entityType)){
            log.warn("request /fixed_ip/fixed_ip_model.json entityType empty!");
            return;
        }
        if (StringUtils.isBlank(lang)){
            log.warn("request /fixed_ip/fixed_ip_model.json lang empty!");
            return;
        }
        fixedIpService.fixedIpModel(entityType,lang,response);
    }

    @PostMapping("/fixed_ips.json")
    @ApiOperation(value = "批量导入", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    public ReturnModel fixedIpModelInfo(
            @RequestParam(name = "entityType") Integer entityType,
            @RequestParam(name = "file") MultipartFile file,
            HttpServletRequest request,
            HttpServletResponse response
    ){
        String originalFilename = file.getOriginalFilename().toLowerCase();
        if (!originalFilename.endsWith(XLSX)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("file.type.error")).setData(0);
        }
        String lang = CurrentUserUtil.getLang(request);
        if (ObjectUtil.isNull(entityType)){
            log.warn("request /fixed_ip/fixed_ips.json entityType empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("entityType param is blank");
        }
        if (StringUtils.isBlank(lang)){
            log.warn("request /fixed_ip/fixed_ips.json lang empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("lang param is blank");
        }
        return fixedIpService.importFixedIps(entityType,lang,file,response);
    }

    @GetMapping("/get_arc_info_by_id.json")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = false, paramType = "query")
    })
    @ApiOperation(value = "get arc info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getArcInfo(
            @RequestParam(name = "arcId", required = false) String arcId,
            @RequestParam(name = "arcAccount", required = false) String arcAccount,
            @RequestParam(name = "arcType", required = false) Integer arcType,
            @RequestParam(name = "arcAccountType", required = false) String arcAccountType,
            HttpServletRequest request
    ) {
        String userId = CurrentUserUtil.getUserId(request);
        if (org.apache.commons.lang3.StringUtils.isBlank(userId)) {
            log.warn("request /fixed_ip/get_arc_info_by_id.json userId empty!");
            return ReturnModel.getInstance().setCode(500).setMsg("userId param is blank");
        }
        String lang = CurrentUserUtil.getLang(request);

        //通过档案ID查询档案信息
        QueryWrapper<FixedIpEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("original_ip_address", arcAccount).eq("delete_status", 0).last("LIMIT 1");
        FixedIpEntity fixedIpEntity = fixedIpService.getOne(queryWrapper);
        if (ObjectUtil.isNull(fixedIpEntity)) {
            return ReturnModel.getInstance().ok(new ArcInfoModel());
        }

        ReturnModel arcInfo = archiveFeignClient.getArcInfo(ArcIdUtil.getAuthAccountId(CommonConstant.FIX_IP_AUTH_ACCOUNT_TYPE, fixedIpEntity.getIpAddress()), arcAccount, arcType, arcAccountType, userId, lang);
        Object data = arcInfo.getData();
        ArcInfoModel arcInfoModel = JSONObject.parseObject(JSONObject.toJSONString(data), ArcInfoModel.class);
        return ReturnModel.getInstance().ok().setData(arcInfoModel);
    }

    @GetMapping("/query_fixed_ip_status.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ipList", value = "待获取的ipAddress，批量查询用逗号分隔", dataType = "String", required = true, paramType = "query")
    })
    @ApiOperation(value = "固定Ip知识库获取建档状态", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel queryFixedIpStatus(@RequestParam(name = "ipList", required = true) String ipList,
                                          HttpServletRequest request
    ) {
        String lang = CurrentUserUtil.getLang(request);
        if (StringUtils.isBlank(ipList)) {
            log.warn("request /fixed_ip/query_fixed_ip_status.json fixed ip empty!");
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_BLANK.getCode()).setMsg("fixed ip "+I18nUtils.getMessage(ErrorCodeEnum.PARAM_IS_BLANK.getMsg()));
        }
        if (StringUtils.isBlank(lang)){
            log.warn("request /fixed_ip/query_fixed_ip_status.json fixed ip empty!");
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_BLANK.getCode()).setMsg("lang " + I18nUtils.getMessage(ErrorCodeEnum.PARAM_IS_BLANK.getMsg()));
        }
        return fixedIpService.queryFixedIpStatus(ipList);
    }

    /**
     * 根据IP查询固定IP用户信息(2024.05.21 当前提供给统计分析系统使用)
     * @param ip ip信息
     * @param startDay 查询开始日期
     * @param endDay 查询结束日期
     * @return ip关联固定IP用户信息
     */
    @GetMapping("/query_fixed_ip_user.json")
    @ApiOperation(value = "根据IP查询固定IP用户信息", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> queryFixedIpUser(@RequestParam(name = "ip") String ip, @RequestParam(name = "startDay") String startDay, @RequestParam(name = "endDay") String endDay
    ) {
        if (StringUtils.isBlank(ip) || StringUtils.isBlank(startDay) || StringUtils.isBlank(endDay)) {
            log.warn("request /fixed_ip/query_fixed_ip_user.json param is empty!");
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return fixedIpService.queryFixedIpUser(ip, startDay, endDay);
    }

}
