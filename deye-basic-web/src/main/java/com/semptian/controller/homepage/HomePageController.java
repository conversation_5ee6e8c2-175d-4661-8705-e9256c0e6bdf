package com.semptian.controller.homepage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.business.homepage.HomePageBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.FileTypeEnum;
import com.semptian.enums.ModuleEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.ImportModel;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.semptian.constant.CommonConstant.LANG_EN;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/home_page")
@Api(tags = "首页 API")
@Slf4j
public class HomePageController {

    @Autowired
    private HomePageBiz homePageBiz;

    @Autowired
    private ResourceLoader resourceLoader;

    @PostMapping("/file_upload.json")
    public Object upload(@RequestParam(value = "file") MultipartFile file) {
        return homePageBiz.upload(file);
    }

    @DeleteMapping("/file_delete.json")
    public Object delete(@RequestBody String filePath) {
        JSONObject jsonObject = JSON.parseObject(filePath);
        Object obj = jsonObject.get("filePath");
        if (obj == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return homePageBiz.delFile(obj.toString());
    }

    @GetMapping("/template_download.json")
    public Object download(
            @RequestParam(value = "moduleType") Integer moduleType,
            @RequestParam(value = "fileType") Integer fileType,
            HttpServletResponse response
    ) {
        InputStream inputStream = null;
        ServletOutputStream servletOutputStream = null;

        String moduleName = ModuleEnum.codeOf(moduleType);
        String typeName = FileTypeEnum.codeOf(fileType);
        if (null == moduleName) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg("Invalid moduleType");
        }
        if (typeName == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg("Invalid textType");
        }
        String fileName = moduleName + "." + typeName;
        String path = "template/" + fileName;
        try {
            Resource resource = resourceLoader.getResource("classpath:" + path);
            response.setContentType("application/x-msdownload");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            inputStream = resource.getInputStream();
            servletOutputStream = response.getOutputStream();
            IOUtils.copy(inputStream, servletOutputStream);
            response.flushBuffer();
        } catch (Exception e) {
            log.error(e.toString());
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg("Fail to download template");
        } finally {
            try {
                if (servletOutputStream != null) {
                    servletOutputStream.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error(e.toString());
            }
        }
        return ReturnModel.getInstance().ok();
    }

    @GetMapping("/data_statistics.json")
    public Object dataStatistics() {
        return homePageBiz.dataStatistics();
    }

    @PostMapping("/file_import.json")
    public Object fileImport(@RequestBody ImportModel model, HttpServletRequest request) {
        ModuleEnum[] values = ModuleEnum.values();
        List<Integer> types = Lists.newArrayList();
        for (ModuleEnum value : values) {
            types.add(value.getCode());
        }
        if (!types.contains(model.getModuleType())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_IS_INVALID.getCode()).setMsg("Invalid param of moduleType!");
        }
        String lang = request.getParameter("lang");
        if (StringUtils.isBlank(lang)) {
            lang = LANG_EN;
        }
        return homePageBiz.fileImport(model, lang);
    }
}
