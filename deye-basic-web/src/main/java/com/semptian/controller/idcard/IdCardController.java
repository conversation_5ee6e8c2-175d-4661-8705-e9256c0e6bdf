package com.semptian.controller.idcard;

import com.semptian.base.service.ReturnModel;
import com.semptian.business.idcard.IdCardBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.model.ExportModel;
import com.semptian.param.IdCardModel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import static com.semptian.constant.CommonConstant.FOUR;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/id_card")
@Api(tags = "身份证信息")
@Slf4j
public class IdCardController {

    @Autowired
    private IdCardBiz idCardBiz;

    @ApiOperation(value = "身份证信息修改", httpMethod = "PUT", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update.json")
    public Object update(@RequestBody @ApiParam(name = "IdCardModel", value = "实体json对象", required = true) IdCardModel model) {
        if (model.getId() == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("idcard.lack.id"));
        }
        if (StringUtils.isBlank(model.getNetSiteId())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("idcard.lack.netSiteId"));
        }
        return idCardBiz.update(model);
    }

    @ApiOperation(value = "身份证信息详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detail(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("idcard.lack.id"));
        }
        return idCardBiz.detail(id);
    }

    @ApiOperation(value = "身份证信息分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "netSiteId", required = false) String netSiteId,
            @RequestParam(value = "netSiteIdFlag", required = false, defaultValue = "3") Integer netSiteIdFlag,
            @RequestParam(value = "netSiteName", required = false) String netSiteName,
            @RequestParam(value = "netSiteNameFlag", required = false, defaultValue = "3") Integer netSiteNameFlag,
            @RequestParam(value = "cardId", required = false) String cardId,
            @RequestParam(value = "cardFlag", required = false, defaultValue = "3") Integer cardFlag,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "userNameFlag", required = false, defaultValue = "3") Integer userNameFlag,
            @RequestParam(value = "roomId", required = false) String roomId,
            @RequestParam(value = "roomIdFlag", required = false, defaultValue = "3") Integer roomIdFlag,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType

    ) {
        return idCardBiz.pageQuery(
                netSiteId,
                netSiteIdFlag,
                netSiteName,
                netSiteNameFlag,
                cardId,
                cardFlag,
                userName,
                userNameFlag,
                roomId,
                roomIdFlag,
                onPage,
                size,
                orderField,
                orderType
        );
    }

    @ApiOperation(value = "身份证信息导出", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/export.json")
    public void export(
            @RequestBody ExportModel model,
            HttpServletResponse response
    ) {
        if(model.getExportType() == null){
            model.setExportType(FOUR);
        }
        idCardBiz.export(model,response);
    }
}
