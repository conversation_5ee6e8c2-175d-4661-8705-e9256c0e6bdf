package com.semptian.controller.invalidtel;

import cn.hutool.core.collection.CollectionUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.business.invalidtel.InvalidTelBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import com.semptian.param.InvalidTelModel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import static com.semptian.constant.CommonConstant.FOUR;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/number_blacklist")
@Api(tags = "号码黑名单")
@Slf4j
public class InvalidTelController {
    @Autowired
    private InvalidTelBiz invalidTelBiz;

    @ApiOperation(value = "号码黑名单保存", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/save.json")
    public Object save(@RequestBody @ApiParam(name = "InvalidTelModel", value = "实体json对象", required = true) InvalidTelModel model) {
        if (StringUtils.isBlank(model.getPhoneNumber())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("invalidPhone.lack.phone"));
        }

        return invalidTelBiz.save(model);
    }

    @ApiOperation(value = "号码黑名单详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detail(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("invalidPhone.lack.id"));
        }
        return invalidTelBiz.detail(id);
    }

    @ApiOperation(value = "号码黑名单删除", httpMethod = "DELETE", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @DeleteMapping("/delete.json")
    public Object delete(@RequestBody DelModel model) {
        if (CollectionUtil.isEmpty(model.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("invalidPhone.lack.id"));
        }
        return invalidTelBiz.delete(model);
    }

    @ApiOperation(value = "号码黑名单分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
            @RequestParam(value = "phoneNumberFlag", required = false, defaultValue = "3") Integer phoneNumberFlag,
            @RequestParam(value = "remark", required = false) String remark,
            @RequestParam(value = "remarkFlag", required = false, defaultValue = "3") Integer remarkFlag,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType

    ) {
        return invalidTelBiz.pageQuery(
                phoneNumber,
                phoneNumberFlag,
                remark,
                remarkFlag,
                onPage,
                size,
                orderField,
                orderType
        );
    }

    @ApiOperation(value = "号码黑名单导出", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/export.json")
    public void export(
            @RequestBody ExportModel model,
            HttpServletResponse response
    ) {
        if(model.getExportType() == null){
            model.setExportType(FOUR);
        }
        invalidTelBiz.export(model,response);
    }
}
