package com.semptian.controller.iplibrary;

import com.semptian.base.service.ReturnModel;
import com.semptian.business.iplibrary.IpLibraryBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.AdslModel;
import com.semptian.param.IpLibraryModel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/ip_library")
@Api(tags = "全球IP库")
@Slf4j
public class IpLibraryController {

    @Autowired
    private IpLibraryBiz ipLibraryBiz;

    @ApiOperation(value = "IP修改", httpMethod = "PUT", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update.json")
    public Object update(@RequestBody @ApiParam(name = "IpLibraryModel", value = "实体json对象", required = true) IpLibraryModel model) {
        if (model.getId() == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.lack.id"));
        }
        if (StringUtils.isBlank(model.getStartIp())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.lack.startIp"));
        }
        if (StringUtils.isBlank(model.getEndIp())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.lack.endIp"));
        }
        return ipLibraryBiz.update(model);
    }

    @ApiOperation(value = "IP详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detail(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("ip.lack.id"));
        }
        return ipLibraryBiz.detail(id);
    }

    @ApiOperation(value = "IP分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "companyName", required = false) String companyName,
            @RequestParam(value = "companyNameFlag", required = false, defaultValue = "3") Integer companyNameFlag,
            @RequestParam(value = "address", required = false) String address,
            @RequestParam(value = "addressFlag", required = false, defaultValue = "3") Integer addressFlag,
            @RequestParam(value = "ipAddr", required = false) String ipAddr,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType

    ) {
        return ipLibraryBiz.pageQuery(
                companyName,
                companyNameFlag,
                address,
                addressFlag,
                ipAddr,
                onPage,
                size,
                orderField,
                orderType
        );
    }
}
