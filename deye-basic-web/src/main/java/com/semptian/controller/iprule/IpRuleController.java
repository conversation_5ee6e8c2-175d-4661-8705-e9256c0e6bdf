package com.semptian.controller.iprule;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.service.AppIpRuleService;
import com.semptian.service.DomainIpRuleService;
import com.semptian.service.DwsBehaviorDetailService;
import com.semptian.utils.IpUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@RestController
@CrossOrigin
@Slf4j
@RequestMapping("/ip_rule")
public class IpRuleController {

    @Resource
    private AppIpRuleService appIpRuleService;

    @Resource
    private DomainIpRuleService domainIpRuleService;

    @Resource
    private DwsBehaviorDetailService dwsBehaviorDetailService;

    /**
     * 查询指定ip是否为热门
     *
     * @return Object
     */
    @ApiOperation(value = "查询指定ip是否为热门", notes = "查询指定ip是否为热门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", required = true, value = "IP"),
            @ApiImplicitParam(name = "topSize", value = "默认3 返回符合条件的域名/应用名称"),
            @ApiImplicitParam(name = "ruleRange", value = "默认1,2  1:域名知识库，2：应用知识库"),
            @ApiImplicitParam(name = "queryRange", value = "查询范围, 0-预置数据,1-自动更新数据,2-用户导入数据")
    })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/is_hot.json")
    @OperateLog
    public Object query(@RequestParam(name = "ip") String ip,
                        @RequestParam(name = "topSize", required = false, defaultValue = "3") Integer topSize,
                        @RequestParam(name = "ruleRange", required = false, defaultValue = "1,2") List<Integer> ruleRange,
                        @RequestParam(name = "queryRange", required = false, defaultValue = "0,1") List<Integer> queryRange) {

        List<String> hotAppList = Lists.newArrayList();
        List<String> hotDomainList = Lists.newArrayList();
        try {
            String abbrIPv6 = null;
            //如果是ipv6，将ip转换为简写
            if (IpUtil.isipv6(ip)) {
                abbrIPv6 = IpUtil.parseAbbrIPv6(ip);
            }
            for (Integer i : ruleRange) {
                // 域名知识库
                if (i == 1) {
                    if (queryRange.contains(CommonConstant.PRESET_RULE_SOURCE_TYPE) || queryRange.contains(CommonConstant.AUTO_UPDATE_RULE_SOURCE_TYPE)) {
                        hotDomainList = dwsBehaviorDetailService.selectDomainListByIp(ip, abbrIPv6);
                        //只保留黑名单中的域名
                        hotDomainList = domainIpRuleService.hotFilter(hotDomainList, topSize);
                    } else if (queryRange.contains(CommonConstant.USER_RULE_SOURCE_TYPE)) {
                        hotDomainList = domainIpRuleService.isHot(ip, abbrIPv6, queryRange, topSize);
                    }
                }
                // 应用知识库
                else if (i == 2) {
                    hotAppList = appIpRuleService.isHot(ip, abbrIPv6, queryRange, topSize);
                }
            }
        } catch (Exception e) {
            log.error("query hot error, case:{}", e);
        }

        return ReturnModel.getInstance().ok().setData(buildIsHotIpResult(ip, hotAppList, hotDomainList));
    }

    /**
     * 批量查询指定ip是否为热门
     *
     * @return Object
     */
    @ApiOperation(value = "批量查询指定ip是否为热门", notes = "批量查询指定ip是否为热门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ips", required = true, value = "IP"),
            @ApiImplicitParam(name = "topSize", value = "默认3 返回符合条件的域名/应用名称"),
            @ApiImplicitParam(name = "ruleRange", value = "默认1,2  1:域名知识库，2：应用知识库"),
            @ApiImplicitParam(name = "queryRange", value = "查询范围, 0-预置数据,1-自动更新数据,2-用户导入数据")
    })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/is_hot_ips.json")
    @OperateLog
    public Object isHotIps(@RequestParam(name = "ips") List<String> ips,
                           @RequestParam(name = "topSize", required = false, defaultValue = "3") Integer topSize,
                           @RequestParam(name = "ruleRange", required = false, defaultValue = "1,2") List<Integer> ruleRange,
                           @RequestParam(name = "queryRange", required = false, defaultValue = "0,1") List<Integer> queryRange) {

        List<Map> result = new ArrayList<>();
        List<String> hotAppList = Lists.newArrayList();
        List<String> hotDomainList = Lists.newArrayList();
        for (String ip : ips) {
            try {
                String abbrIPv6 = null;
                //如果是ipv6，将ip转换为简写
                if (IpUtil.isipv6(ip)) {
                    abbrIPv6 = IpUtil.parseAbbrIPv6(ip);
                }
                for (Integer i : ruleRange) {
                    // 域名知识库
                    if (i == 1) {
                        if (queryRange.contains(CommonConstant.PRESET_RULE_SOURCE_TYPE) || queryRange.contains(CommonConstant.AUTO_UPDATE_RULE_SOURCE_TYPE)) {
                            hotDomainList = dwsBehaviorDetailService.selectDomainListByIp(ip, abbrIPv6);
                            //只保留黑名单中的域名
                            hotDomainList = domainIpRuleService.hotFilter(hotDomainList, topSize);
                        }
                        if (queryRange.contains(CommonConstant.USER_RULE_SOURCE_TYPE)) {
                            hotDomainList.addAll(domainIpRuleService.isHot(ip, abbrIPv6, queryRange, topSize));
                        }
                    }
                    // 应用知识库
                    else if (i == 2) {
                        hotAppList = appIpRuleService.isHot(ip, abbrIPv6, queryRange, topSize);
                    }
                }
            } catch (Exception e) {
                log.error("query hot error, case:{}", e);
            }
            result.add(buildIsHotIpResult(ip, hotAppList, hotDomainList));
        }

        return ReturnModel.getInstance().ok().setData(result);
    }

    private Map<String, Object> buildIsHotIpResult(String ip, List<String> hotAppList, List<String> hotDomainList) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("ip", ip);
        result.put("isHot", hotAppList.isEmpty() && hotDomainList.isEmpty() ? CommonConstant.FALSE : CommonConstant.TRUE);
        result.put("hotApp", hotAppList);
        result.put("hotDomain", hotDomainList);
        return result;
    }

}
