package com.semptian.controller.mobile;

import com.semptian.base.service.ReturnModel;
import com.semptian.business.mobileregister.MobileRegisterBiz;
import com.semptian.entity.MobileRegisterEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.service.MobileRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * @author: ZC
 * @date: 2021/3/12 14:27
 */
@Slf4j
@CrossOrigin
@RequestMapping(value = "/phone_register")
@RestController
@Api(tags = "手机注册信息", value = "手机注册信息")
public class MobileRegisterController {

    @Autowired
    private MobileRegisterBiz mobileRegisterBiz;

    @ApiOperation(value = "手机注册信息编辑", httpMethod = "PUT", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PutMapping("/update.json")
    public Object saveMsg(@RequestBody MobileRegisterEntity registerEntity) {
        if (StringUtils.isEmpty(registerEntity)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return mobileRegisterBiz.updateMsg(registerEntity);
    }



    @ApiOperation(value = "手机注册信息详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detailMsg(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("adsl.lack.id"));
        }
        return mobileRegisterBiz.detailMsg(id);
    }


    @ApiOperation(value = "手机注册分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "msisdn", required = false) String msisdn,
            @RequestParam(value = "msisdnFlag", required = false, defaultValue = "3") Integer msisdnFlag,
            @RequestParam(value = "imsi", required = false) String imsi,
            @RequestParam(value = "imsiFlag", required = false, defaultValue = "3") Integer imsiFlag,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "nameFlag", required = false, defaultValue = "3") Integer nameFlag,
            @RequestParam(value = "nowAddress", required = false) String nowAddress,
            @RequestParam(value = "nowAddressFlag", required = false, defaultValue = "3") Integer nowAddressFlag,
            @RequestParam(value = "residenceType", required = false) Integer residenceType,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType
    ) {

        return mobileRegisterBiz.selectMsg(msisdn, msisdnFlag, imsi, imsiFlag, name, nameFlag, nowAddress, nowAddressFlag, residenceType, onPage, size, orderField, orderType);

    }

}
