package com.semptian.controller.number;



import cn.hutool.core.io.FileUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.business.number.PhoneNumberBiz;
import com.semptian.common.annotation.AuthCheckAnno;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.PhoneNumberDeleteModel;
import com.semptian.param.PhoneNumberModel;
import com.semptian.service.SpecialNumberService;
import com.semptian.utils.CsvUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Map;


/**
 * 电话号码白名单web接口服务
 *
 * @Author: sk
 * @date: 2020/12/17
 * @params:
 */

@RestController
@CrossOrigin
@RequestMapping("/number_control")
@Api(tags = "号码白名单API")
@Slf4j
public class PhoneNumberController {

    private static final String USER_ID_KEY = "userId";

    @Resource
    private PhoneNumberBiz phoneNumberBiz;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private SpecialNumberService specialNumberService;

    /**
     * 查询号码白名单列表接口
     *
     * @Author: sk
     * @date: 2020/12/17
     * @params: [keyword, onPage, size, orderField, orderType, httpHeaders]
     */
    @ApiOperation(value = "查询号码白名单列表接口", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "模糊查询关键字", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "当前页码", required = false, dataType = "int", defaultValue = "1", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", required = false, dataType = "int", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "orderField", value = "排序字段", required = false, dataType = "String", defaultValue = "createTime", paramType = "query"),
            @ApiImplicitParam(name = "orderType", value = "排序方式 0降序 1升序", required = false, dataType = "int", defaultValue = "0", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "phoneType", value = "控制类型", required = false, dataType = "int", defaultValue = "0", paramType = "query")
    })
    @GetMapping("/query.json")
    @OperateLog
    public Object queryPageList(
            @RequestParam(name = "keyword", required = false, defaultValue = "") String keyword,
            @RequestParam(name = "onPage", required = false, defaultValue = "1") int onPage,
            @RequestParam(name = "size", required = false, defaultValue = "20") int size,
            @RequestParam(name = "orderField",required = false, defaultValue = "createTime") String orderField,
            @RequestParam(name = "orderType", required = false, defaultValue = "0") int orderType,
            @RequestParam(name = "startTime", defaultValue = "" ,required = false) Long startTime,
            @RequestParam(name = "endTime", defaultValue = "" ,required = false) Long endTime,
            @RequestParam(name = "phoneType", required = false, defaultValue = "0") Integer phoneType,
            @RequestHeader HttpHeaders httpHeaders
    ) {
        if ( null != startTime && null != endTime ){
            startTime = startTime /1000L;
            endTime = endTime /1000L;
        }

        return phoneNumberBiz.queryPageList(keyword, onPage, size, orderField, orderType,startTime ,endTime ,phoneType);

    }

    /**
     * 号码保存
     *
     * @Author: sk
     * @date: 2020/12/17
     * @params: [httpHeaders, phoneNumberModel]
     */
    @ApiOperation(value = "号码保存", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")
    })
    @PostMapping("/save.json")
    @AuthCheckAnno(isCheckUserId = true)
    @OperateLog
    public Object saveNumber(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @ApiParam(name = "phoneNumberModel", value = "实体json对象", required = true) PhoneNumberModel phoneNumberModel

    ) {
        Long userId = Long.valueOf(httpHeaders.get("userId").get(0));
        if (StringUtils.isBlank(phoneNumberModel.getCountryCode())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("country.code.empty"));
        }
        if (StringUtils.isBlank(phoneNumberModel.getTelephoneNum())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.name.isnull"));
        }
        if ( null == phoneNumberModel.getPhoneType()) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.type.isnull"));
        }
        phoneNumberModel.setUserId(userId);
        return phoneNumberBiz.saveNumber(phoneNumberModel);

    }


    /**
     * 号码删除
     *
     * @Author: sk
     * @date: 2020/12/17
     * @params: [httpHeaders,clueId]
     */
    @ApiOperation(value = "号码删除接口", httpMethod = "DELETE", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")

    })
    @DeleteMapping("/delete.json")
    @OperateLog
    public Object delete(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @ApiParam(name = "phoneNumberDeleteModel", value = "实体json对象", required = true) PhoneNumberDeleteModel phoneNumberDeleteModel) {
        Map<String, String> map = phoneNumberDeleteModel.getObj();
        if (MapUtils.isEmpty(map)){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("map.collection.empty"));
        }
        return phoneNumberBiz.delete(map);

    }


    /**
     * 切换号码状态
     *
     * @return Object
     */
    @ApiOperation(value = "切换号码状态", httpMethod = "PUT", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")

    })
    @PutMapping("/update_status.json")
    @OperateLog
    public Object updateStatus(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @ApiParam(name = "phoneNumberModel", value = "实体json对象", required = true) PhoneNumberModel phoneNumberModel

    ) {
        return phoneNumberBiz.updateStatus(phoneNumberModel);

    }




    /**
     * 号码名称校验
     *
     * @return Object
     */
    @ApiOperation(value = "号码名称校验接口", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "telephoneNum", value = "号码名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phoneType", value = "号码类型", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "countryCode", value = "国家区号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")
    })
    @GetMapping("/number_exist.json")
    @OperateLog
    public Object checkNumberName(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestParam(name = "telephoneNum") String telephoneNum,
            @RequestParam(name = "phoneType") Integer phoneType,
            @RequestParam(name = "countryCode") String countryCode,
            @RequestParam(name = "id", required = false) Long id
    ) {
        if (StringUtils.isBlank(telephoneNum)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.name.isnull"));
        }
        if ( null == phoneType) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.type.isnull"));
        }
        if (StringUtils.isBlank(countryCode)) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("country.code.empty"));
        }

        return phoneNumberBiz.checkNumberName(telephoneNum, phoneType,countryCode,id);

    }


    /**
     * 查询号码详情接口
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询号码详情接口", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "phoneType", value = "类型", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")
    })
    @GetMapping("/detail.json")
    @OperateLog
    public Object getNumberById(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestParam(name = "id", required = true) Long id,
            @RequestParam(name = "phoneType", required = true) Integer phoneType
    ) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.name.isnull"));
        }
        if (phoneType == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.type.isnull"));
        }
        return phoneNumberBiz.queryDetail(phoneType,id);

    }

    /**
     * 修改
     *
     * @Author: sk
     * @date: 2020/12/17
     * @params: [httpHeaders, phoneNumberModel]
     */
    @ApiOperation(value = "号码修改", httpMethod = "PUT", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")
    })
    @PutMapping("/modify.json")
    @AuthCheckAnno(isCheckUserId = true)
    @OperateLog
    public Object modifyNumber(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @ApiParam(name = "phoneNumberModel", value = "实体json对象", required = true) PhoneNumberModel phoneNumberModel

    ) {

        if (phoneNumberModel.getId() == null ) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));

        }
        if (StringUtils.isBlank(phoneNumberModel.getCountryCode())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("country.code.empty"));
        }
        if (StringUtils.isBlank(phoneNumberModel.getTelephoneNum())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.name.isnull"));
        }
        if ( null == phoneNumberModel.getPhoneType()) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("phonenumber.type.isnull"));
        }
        Long userId = Long.valueOf(httpHeaders.get("userId").get(0));
        phoneNumberModel.setUserId(userId);
        return phoneNumberBiz.modifyNumber(phoneNumberModel);

    }

    @ApiOperation(value = "号码导出接口", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileType", value = "导出类型",  dataType = "int", defaultValue = "1", paramType = "query"),
            @ApiImplicitParam(name = "ids", value = "ids", dataType = "string",  paramType = "query"),
            @ApiImplicitParam(name = "sids", value = "sids", dataType = "string",  paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "long", paramType = "header")

    })
    @GetMapping("/export.json")
    @AuthCheckAnno(isCheckUserId = true)
    @OperateLog
    public Object numberExport(
            @RequestHeader HttpHeaders httpHeaders,
            @RequestParam(name = "fileType",  defaultValue = "1") Integer fileType,
            @RequestParam(name = "ids", defaultValue = "") String ids,
            @RequestParam(name = "sids", defaultValue = "") String sids,
            HttpServletResponse response

    ) {


        if (null == fileType) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }

        Long userId = Long.valueOf(httpHeaders.get("userId").get(0));
        try {
            return phoneNumberBiz.numberExport(fileType,ids, sids,userId,response);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return ReturnModel.getInstance().error();
    }




    @PostMapping("/batch_import.json")
    @ApiOperation(value = "白名单特殊号码批量导入", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object importBatchNum(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request){
        String curUserId = request.getHeader(USER_ID_KEY);
        log.info("current user: {}", curUserId);
        String lang = request.getParameter("lang");
        Integer integer = 0;
        if (StringUtils.isEmpty(lang)) {
            integer = 0;
        }else {
            if (lang.equals("zh_CN")) {
                integer = 1;
            } else if (lang.equals("fr_DZ")) {
                integer = 2;
            } else if (lang.equals("en_US")) {
                integer = 3;
            }
        }
        if (!StringUtils.isEmpty(curUserId)) {
            Integer userId = Integer.valueOf(curUserId);
            return phoneNumberBiz.importBatchNum(file,userId,integer);
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
    }

    @ApiOperation(value = "白名单特殊号码导入模板下载", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })

    @RequestMapping(value = "/template_download.json", method = RequestMethod.GET)
    @OperateLog
    public Object downloadTemplateFile( HttpServletResponse response, HttpServletRequest request) {

        InputStream inputStream = null;

        try {
            String lang = request.getParameter("lang");
            String path = "";
            if (org.springframework.util.StringUtils.isEmpty(lang)) {
                path = "classpath:file/白名单特殊号码导入模板.xlsx";
            }else {
                if (lang.equals("zh_CN")) {
                    path = "classpath:file/白名单特殊号码导入模板.xlsx";
                } else if (lang.equals("fr_DZ")) {
                    path = "classpath:file/Modèle d'ImportationDeNuméro.xlsx";
                } else if (lang.equals("en_US")) {
                    path = "classpath:file/WhitelistSpecialNumberImportTemplate.xlsx";
                }
            }

            inputStream = resourceLoader.getResource(path).getInputStream();
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }

    @ApiOperation(value = "获取所有在控特殊号码", httpMethod = "GET")
    @GetMapping(value = "/all_on_special_nums.json")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public Object getOnSpecialNums() {
        return specialNumberService.getALLOn();
    }

}
