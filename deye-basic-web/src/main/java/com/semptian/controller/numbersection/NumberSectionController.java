package com.semptian.controller.numbersection;

import cn.hutool.core.collection.CollectionUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.business.numbersection.NumberSectionBiz;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.DelModel;
import com.semptian.model.ExportModel;
import com.semptian.param.NumberSectionModel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import static com.semptian.constant.CommonConstant.FOUR;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/number_section")
@Api(tags = "号码段")
@Slf4j
public class NumberSectionController {

    @Autowired
    private NumberSectionBiz numberSectionBiz;

    @ApiOperation(value = "号码段保存", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/save.json")
    public Object save(@RequestBody @ApiParam(name = "NumberSectionModel", value = "实体json对象", required = true) NumberSectionModel model) {
        if (StringUtils.isBlank(model.getMsisdn())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("numberSection.lack.msisdn"));
        }
        return numberSectionBiz.save(model);
    }

    @ApiOperation(value = "号码段详情查看", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/detail.json")
    public Object detail(@RequestParam(value = "id", required = true) Long id) {
        if (id == null) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("numberSection.lack.id"));
        }
        return numberSectionBiz.detail(id);
    }

    @ApiOperation(value = "号码段删除", httpMethod = "DELETE", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @DeleteMapping("/delete.json")
    public Object delete(@RequestBody DelModel model) {
        if (CollectionUtil.isEmpty(model.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("numberSection.lack.id"));
        }
        return numberSectionBiz.delete(model);
    }

    @ApiOperation(value = "号码段分页查询", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/page_query.json")
    public Object pageQuery(
            @RequestParam(value = "cityName", required = false) String cityName,
            @RequestParam(value = "cityNameFlag", required = false, defaultValue = "3") Integer cityNameFlag,
            @RequestParam(value = "netType", required = false) String netType,
            @RequestParam(value = "netTypeFlag", required = false, defaultValue = "3") Integer netTypeFlag,
            @RequestParam(value = "msisdn", required = false) String msisdn,
            @RequestParam(value = "msisdnFlag", required = false, defaultValue = "3") Integer msisdnFlag,
            @RequestParam(value = "postCode", required = false) String postCode,
            @RequestParam(value = "postCodeFlag", required = false, defaultValue = "3") Integer postCodeFlag,
            @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "orderField", required = false, defaultValue = "updateTime") String orderField,
            @RequestParam(value = "orderType", required = false, defaultValue = "0") Integer orderType

    ) {
        return numberSectionBiz.pageQuery(
                cityName,
                cityNameFlag,
                netType,
                netTypeFlag,
                msisdn,
                msisdnFlag,
                postCode,
                postCodeFlag,
                onPage,
                size,
                orderField,
                orderType
        );
    }

    @ApiOperation(value = "号码段数据导出", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/export.json")
    public void export(
            @RequestBody ExportModel model,
            HttpServletResponse response
    ) {
        if(model.getExportType() == null){
            model.setExportType(FOUR);
        }
        numberSectionBiz.export(model,response);
    }
}
