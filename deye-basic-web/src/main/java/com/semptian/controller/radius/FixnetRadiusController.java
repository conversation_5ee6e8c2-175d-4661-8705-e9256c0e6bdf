package com.semptian.controller.radius;

import com.semptian.base.service.ReturnModel;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.service.FixnetRadiusService;
import com.semptian.service.MobileRadiusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2024/12/11 10:50
 */
@Slf4j
@CrossOrigin
@RequestMapping(value = "/fixnet_radius")
@RestController
@Api(tags = "fixnet_radius", value = "fixnet_radius")
public class FixnetRadiusController {

    @Autowired
    private FixnetRadiusService fixnetRadiusService;

    @OperateLog
    @ApiOperation(value = "查询固网radius号码信息列表", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_radius_list.json")
    public Object queryRadiusList(
            @RequestParam(name = "account", required = false, defaultValue = "") String account,
            @RequestParam(name = "onPage", required = false, defaultValue = "1") int onPage,
            @RequestParam(name = "size", required = false, defaultValue = "20") int size
    ) {
        if(StringUtils.isNotEmpty(account) && account.length()>255){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("keyword is too long");
        }
        return fixnetRadiusService.queryRadiusList(account,onPage,size);
    }

    @OperateLog
    @ApiOperation(value = "查看某个账号的ip列表", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_ip_list.json")
    public Object queryIPList(
            @RequestParam(name = "account", required = true, defaultValue = "") String account,
            @RequestParam(name = "startDay", required = true, defaultValue = "") String startDay,
            @RequestParam(name = "endDay", required = true, defaultValue = "") String endDay,
            @RequestParam(name = "dateOption", required = false, defaultValue = "") String dateOption,
            @RequestParam(name = "onPage", required = false, defaultValue = "1") int onPage,
            @RequestParam(name = "size", required = false, defaultValue = "20") int size
    ) {
        return fixnetRadiusService.authRecord(account,startDay,endDay,dateOption,onPage,size);
    }


}
