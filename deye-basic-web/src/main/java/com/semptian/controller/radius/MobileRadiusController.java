package com.semptian.controller.radius;

import com.semptian.base.service.ReturnModel;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.service.MobileRadiusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2024/12/11 10:50
 */
@Slf4j
@CrossOrigin
@RequestMapping(value = "/mobile_radius")
@RestController
@Api(tags = "mobile_radius", value = "mobile_radius")
public class MobileRadiusController {

    @Autowired
    private MobileRadiusService mobileRadiusService;

    @OperateLog
    @ApiOperation(value = "查询移动网radius号码信息列表", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/query_radius_list.json")
    public Object queryRadiusList(
            @RequestParam(name = "phoneNumber", required = false, defaultValue = "") String phoneNumber,
            @RequestParam(name = "onPage", required = false, defaultValue = "1") int onPage,
            @RequestParam(name = "size", required = false, defaultValue = "20") int size
    ) {
        if(StringUtils.isNotEmpty(phoneNumber) && phoneNumber.length()>255){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_EXCEPTION.getCode()).setMsg("keyword is too long");
        }
        return mobileRadiusService.queryRadiusList(phoneNumber,onPage,size);
    }

}
