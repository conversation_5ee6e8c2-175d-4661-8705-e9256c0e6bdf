package com.semptian.controller.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.WhiteListEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.WhiteListStatusEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.service.ClueIpFilterTableService;
import com.semptian.service.WhiteListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: ZC
 * @date: 2021/2/25 19:47
 * 定时任务调度入口
 */
@CrossOrigin
@RestController
@RequestMapping("/schedule_task")
@Slf4j
@Api(tags = "IP定时任务调度入口")
public class ScheduleTaskController {

    @Autowired
    private ClueIpFilterTableService clueIpFilterTableService;

    @Autowired
    private WhiteListService whiteListService;

    @ApiOperation(value = "监控过期IP进行停控", notes = "监控过期IP进行停控")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/ip_expire_monitor.json")
    public Object IpExpireMonitor() {
        try {
            clueIpFilterTableService.ipExpireMonitor();
        } catch (Exception e) {
            log.error(e.toString());
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
        }
        return ReturnModel.getInstance().ok();

    }

    /**
     * 白名单失败重试，每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void whiteListRetry() {
        // 查询所有在控中/停控中的线索
        List<WhiteListEntity> whiteLists = whiteListService.list(new LambdaQueryWrapper<WhiteListEntity>()
                .in(WhiteListEntity::getStatus, WhiteListStatusEnum.TO_BE_START.getCode(), WhiteListStatusEnum.TO_BE_STOP.getCode())
        );

        // 将数据按在控中、停控中分组下发
        whiteLists.stream().collect(Collectors.groupingBy(data -> data.getStatus())).forEach((status, datas) -> {
            whiteListService.issuedWhiteListToYJ(datas, status);
        });
    }
}
