package com.semptian.controller.spamcall;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.DeleteModel;
import com.semptian.param.SpamCallNumberInfoModel;
import com.semptian.param.SpamCallNumberQueryParamModel;
import com.semptian.service.SpamCallNumberInfoService;
import com.semptian.utils.CsvUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

import static com.semptian.constant.CommonConstant.USER_ID_KEY;
import static com.semptian.constant.ImportConstant.*;

/**
 * <p>
 * 骚扰号码信息库表 前端控制器
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@RestController
@RequestMapping("/spam_call_number")
public class SpamCallNumberInfoController {

    @Resource
    private SpamCallNumberInfoService spamCallNumberService;

    @Resource
    private ResourceLoader resourceLoader;

    @ApiOperation(value = "骚扰号码添加", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/add.json")
    public Object add(@RequestBody SpamCallNumberInfoModel spamCallNumberInfoModel) {
        if (ObjectUtil.isNull(spamCallNumberInfoModel) || StrUtil.isBlank(spamCallNumberInfoModel.getPhoneNumber())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }

        return spamCallNumberService.add(spamCallNumberInfoModel);
    }

    @ApiOperation(value = "骚扰号码修改", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/update.json")
    public Object update(@RequestBody SpamCallNumberInfoModel spamCallNumberInfoModel) {
        if (ObjectUtil.isNull(spamCallNumberInfoModel) || StrUtil.isBlank(spamCallNumberInfoModel.getPhoneNumber())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }

        return spamCallNumberService.update(spamCallNumberInfoModel);
    }

    @ApiOperation(value = "骚扰号码删除", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/delete.json")
    public Object delete(@RequestBody DeleteModel deleteModel) {
        if (CollUtil.isEmpty(deleteModel.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return spamCallNumberService.delete(deleteModel);
    }

    @ApiOperation(value = "骚扰号码分页查询列表", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/list.json")
    public Object list(@RequestBody SpamCallNumberQueryParamModel spamCallNumberQueryParamModel) {
        return spamCallNumberService.list(spamCallNumberQueryParamModel);
    }

    @ApiOperation(value = "号码国际区号列表", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/phone_code_list.json")
    public Object phoneCodeList() {
        return spamCallNumberService.phoneCodeList();
    }

    @PostMapping("/batch_import.json")
    @ApiOperation(value = "骚扰号码批量导入", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object batchImport(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        String curUserId = request.getHeader(USER_ID_KEY);
        log.info("current user: {}", curUserId);
        String lang = request.getParameter("lang");
        int integer;
        if (StringUtils.isEmpty(lang)) {
            integer = 2;
        }else {
            if (CommonConstant.LANG_CN.equals(lang)) {
                integer = 1;
            } else if (CommonConstant.LANG_EN.equals(lang)) {
                integer = 3;
            }else {
                integer = 2;
            }
        }
        if (!StringUtils.isEmpty(curUserId)) {
            Integer userId = Integer.valueOf(curUserId);
            return spamCallNumberService.importBatch(file, userId,integer, response);
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
    }

    @RequestMapping(value = "/template_download.json", method = RequestMethod.GET)
    @OperateLog
    public Object downloadTemplateFile(HttpServletResponse response, HttpServletRequest request) {
        String lang = request.getParameter("lang");
        String path = FILE_CLASS_PATH + SPAM_CALL_NUMBER_FILE_NAME_FR_DZ;

        if (StrUtil.isNotEmpty(lang)) {
            if (CommonConstant.LANG_CN.equals(lang)) {
                path = FILE_CLASS_PATH + SPAM_CALL_NUMBER_FILE_NAME_ZH_CN;
            }else if (CommonConstant.LANG_EN.equals(lang)) {
                path = FILE_CLASS_PATH + SPAM_CALL_NUMBER_FILE_NAME_EN_US;
            }
        }

        try {
            InputStream inputStream = resourceLoader.getResource(path).getInputStream();
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }
}
