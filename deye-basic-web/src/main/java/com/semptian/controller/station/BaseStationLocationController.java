package com.semptian.controller.station;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.BaseStationExportModel;
import com.semptian.param.BaseStationLocationModel;
import com.semptian.param.BaseStationQueryParamModel;
import com.semptian.param.DeleteModel;
import com.semptian.service.BaseStationLocationService;
import com.semptian.utils.CsvUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.semptian.constant.CommonConstant.USER_ID_KEY;
import static com.semptian.constant.CommonConstant.XLSX;
import static com.semptian.constant.ImportConstant.BASE_STATION_FILE_NAME_EN_US;
import static com.semptian.constant.ImportConstant.FILE_CLASS_PATH;

/**
 * <p>
 * 基站位置信息库表 前端控制器
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@RestController
@RequestMapping("/base_station")
public class BaseStationLocationController {

    @Resource
    private BaseStationLocationService baseStationLocationService;

    @Resource
    private ResourceLoader resourceLoader;

    private static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(1);

    @ApiOperation(value = "根据左下和右上坐标点经纬度查询基站信息", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/queryStationByLocation.json")
    public Object pageQuery(
            @RequestParam(value = "downLeftLat") Float downLeftLat,
            @RequestParam(value = "downLeftLon") Float downLeftLon,
            @RequestParam(value = "topRightLat") Float topRightLat,
            @RequestParam(value = "topRightLon") Float topRightLon,
            @RequestParam(value = "size") Integer size
    ) {
        if(ObjectUtil.isNull(downLeftLat) || ObjectUtil.isNull(downLeftLon) || ObjectUtil.isNull(topRightLat)
                || ObjectUtil.isNull(topRightLon) || ObjectUtil.isNull(size) ){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return baseStationLocationService.queryStationByLocation(downLeftLat, downLeftLon, topRightLat, topRightLon, size);
    }

    @ApiOperation(value = "根据关键词和总数查询基站信息", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/recommendStationByNumber.json")
    public Object pageQuery(
            @RequestParam(value = "keyword") String keyword,
            @RequestParam(value = "size") Integer size
    ) {
        if(keyword == null || size == null){
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return baseStationLocationService.recommendStationByNumber(keyword, size);
    }

    @ApiOperation(value = "基站位置添加", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/add.json")
    public Object add(@RequestBody BaseStationLocationModel baseStationModel) {
        if (ObjectUtil.isNull(baseStationModel) || StrUtil.isBlank(baseStationModel.getStationNo())
                || ObjectUtil.isNull(baseStationModel.getLatitude()) || ObjectUtil.isNull(baseStationModel.getLongitude())
                || StrUtil.isBlank(baseStationModel.getStationAddress()) ) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }

        return baseStationLocationService.add(baseStationModel);
    }

    @ApiOperation(value = "基站位置修改", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/update.json")
    public Object update(@RequestBody BaseStationLocationModel baseStationModel) {
        if (ObjectUtil.isNull(baseStationModel) || StrUtil.isBlank(baseStationModel.getStationNo())
                || ObjectUtil.isNull(baseStationModel.getLatitude()) || ObjectUtil.isNull(baseStationModel.getLongitude())
                || StrUtil.isBlank(baseStationModel.getStationAddress()) || ObjectUtil.isNull(baseStationModel.getId())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }

        return baseStationLocationService.update(baseStationModel);
    }

    @ApiOperation(value = "基站位置删除", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/delete.json")
    public Object delete(@RequestBody DeleteModel deleteModel) {
        if (CollUtil.isEmpty(deleteModel.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return baseStationLocationService.delete(deleteModel);
    }

    @ApiOperation(value = "基站位置库分页查询列表", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/list.json")
    public Object list(@RequestBody BaseStationQueryParamModel baseStationQueryParamModel) {
        return baseStationLocationService.list(baseStationQueryParamModel);
    }

    @ApiOperation(value = "基站位置库导出", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/export.json")
    public Object export(@RequestBody BaseStationExportModel model, HttpServletResponse response) {
        return baseStationLocationService.export(model, response);
    }

    @PostMapping("/batch_import.json")
    @ApiOperation(value = "基站库批量导入", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object batchImport(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request) {
        String curUserId = request.getHeader(USER_ID_KEY);
        log.info("current user: {}", curUserId);

        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename) || !originalFilename.endsWith(XLSX)) {
            return ReturnModel.getInstance().error(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("file.type.error"));
        }

        if (!StringUtils.isEmpty(curUserId)) {
            Integer userId = Integer.valueOf(curUserId);

            EXECUTOR.submit(() -> {
                baseStationLocationService.importBatch(file, userId, 3);
            });

            baseStationLocationService.sendStartMessage(userId);
            return ReturnModel.getInstance().ok();
        }

        return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("import.fail"));
    }

    @RequestMapping(value = "/template_download.json", method = RequestMethod.GET)
    @OperateLog
    public void downloadTemplateFile(HttpServletResponse response) {
        String path = FILE_CLASS_PATH + BASE_STATION_FILE_NAME_EN_US;
        try (InputStream inputStream = resourceLoader.getResource(path).getInputStream()) {
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }

}
