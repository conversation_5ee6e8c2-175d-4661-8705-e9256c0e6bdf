package com.semptian.controller.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.DeleteModel;
import com.semptian.param.UserCategoryModel;
import com.semptian.service.UserCategoryService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 上网用户分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@RestController
@RequestMapping("/user_category")
public class UserCategoryController {

    @Resource
    private UserCategoryService userCategoryService;

    @ApiOperation(value = "上网用户分类添加", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/add.json")
    public Object add(@RequestBody UserCategoryModel userCategoryModel) {
        if (ObjectUtil.isNull(userCategoryModel) || StrUtil.isBlank(userCategoryModel.getName()) || ObjectUtil.isNull(userCategoryModel.getPid())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userCategoryService.add(userCategoryModel);
    }

    @ApiOperation(value = "上网用户分类修改", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/update.json")
    public Object update(@RequestBody UserCategoryModel userCategoryModel) {
        if (ObjectUtil.isNull(userCategoryModel) || StrUtil.isBlank(userCategoryModel.getName())
                || ObjectUtil.isNull(userCategoryModel.getId()) || ObjectUtil.isNull(userCategoryModel.getPid())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userCategoryService.update(userCategoryModel);
    }

    @ApiOperation(value = "上网用户分类删除", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/delete.json")
    public Object delete(@RequestBody DeleteModel deleteModel) {
        if (CollUtil.isEmpty(deleteModel.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userCategoryService.delete(deleteModel);
    }

    @ApiOperation(value = "上网用户分类树形列表", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/list/tree.json")
    public Object list(@RequestParam(value = "name", required = false) String name) {
        return userCategoryService.tree(name);
    }
}