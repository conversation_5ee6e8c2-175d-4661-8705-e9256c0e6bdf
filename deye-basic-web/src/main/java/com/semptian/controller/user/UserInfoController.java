package com.semptian.controller.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.*;
import com.semptian.service.UserInfoService;
import com.semptian.utils.CsvUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

import static com.semptian.constant.ImportConstant.*;

/**
 * <p>
 * 上网用户信息库表 前端控制器
 * </p>
 *
 * <AUTHOR> Hu
 * @since 2024-03-20
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserInfoController {

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private ResourceLoader resourceLoader;

    @ApiOperation(value = "上网用户添加", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/add.json")
    public Object add(@RequestBody UserInfoModel userInfoModel) {
        if (ObjectUtil.isNull(userInfoModel) || StrUtil.isBlank(userInfoModel.getUserName())
                || ObjectUtil.isNull(userInfoModel.getUserType()) || ObjectUtil.isNull(userInfoModel.getUserCategoryId())
                || ObjectUtil.isNull(userInfoModel.getStatus())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userInfoService.add(userInfoModel);
    }

    @ApiOperation(value = "上网用户修改", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/update.json")
    public Object update(@RequestBody UserInfoModel userInfoModel) {
        if (ObjectUtil.isNull(userInfoModel) || ObjectUtil.isNull(userInfoModel.getId())
                || ObjectUtil.isNull(userInfoModel.getUserType()) || ObjectUtil.isNull(userInfoModel.getUserCategoryId())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userInfoService.update(userInfoModel);
    }

    @ApiOperation(value = "上网用户删除", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/delete.json")
    public Object delete(@RequestBody DeleteModel deleteModel) {
        if (CollUtil.isEmpty(deleteModel.getIds())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userInfoService.delete(deleteModel);
    }

    @ApiOperation(value = "上网用户状态启用/停用", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/status.json")
    public Object status(@RequestBody BatchUpdateStatusModel batchUpdateStatusModel) {
        if (CollUtil.isEmpty(batchUpdateStatusModel.getIds()) || ObjectUtil.isNull(batchUpdateStatusModel.getStatus())) {
            return ReturnModel.getInstance().setCode(ErrorCodeEnum.PARAM_NOT_COMPLETE.getCode()).setMsg(I18nUtils.getMessage("parameter.is.empty"));
        }
        return userInfoService.status(batchUpdateStatusModel);
    }

    @ApiOperation(value = "上网用户分页查询列表", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/list.json")
    public Object list(@RequestBody UserInfoQueryParamModel userInfoQueryModel) {
        return userInfoService.list(userInfoQueryModel);
    }

    @ApiOperation(value = "账号是否为重要目标", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/is_important_target.json")
    public Object isImportantTarget(@RequestParam(name = "arcType") Integer arcType, @RequestParam(name = "arcAccount") String arcAccount) {
        return ReturnModel.getInstance().ok(userInfoService.isImportantTarget(arcType, arcAccount));
    }

    @ApiOperation(value = "根据重要目标账号和重要目标类型获取重要目标分类", httpMethod = "POST", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/get_important_target_category_by_batch.json")
    public Object getImportantTargetCategoryByBatch(@RequestBody List<ImportantTargetModel> importantTargetList) {
        return ReturnModel.getInstance().ok(userInfoService.getImportantTargetCategoryByBatch(importantTargetList));
    }

    @ApiOperation(value = "关键字模糊查询下拉上网用户信息", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/get_user_info_by_keyword.json")
    public Object getUserInfoByKeyword(@RequestParam(name = "keyword") String keyword) {
        return ReturnModel.getInstance().ok(userInfoService.getUserInfoByKeyword(keyword));
    }

    @PostMapping("/batch_import.json")
    @ApiOperation(value = "上网用户批量导入", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object batchImport(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        String curUserId = request.getHeader("userId");
        log.info("current user: {}", curUserId);
        String lang = request.getParameter("lang");
        int integer;
        if (StringUtils.isEmpty(lang)) {
            integer = 2;
        }else {
            if (CommonConstant.LANG_CN.equals(lang)) {
                integer = 1;
            } else if (CommonConstant.LANG_EN.equals(lang)) {
                integer = 3;
            }else {
                integer = 2;
            }
        }
        if (!StringUtils.isEmpty(curUserId)) {
            Integer userId = Integer.valueOf(curUserId);
            return userInfoService.importBatch(file, userId,integer, response);
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
    }

    @RequestMapping(value = "/template_download.json", method = RequestMethod.GET)
    @OperateLog
    public void downloadTemplateFile(HttpServletResponse response, HttpServletRequest request) {
        String lang = request.getParameter("lang");
        String path = FILE_CLASS_PATH + USER_INFO_FILE_NAME_FR;

        if (StrUtil.isNotEmpty(lang)) {
            if (CommonConstant.LANG_CN.equals(lang)) {
                path = FILE_CLASS_PATH + USER_INFO_FILE_NAME_CN;
            }else if (CommonConstant.LANG_EN.equals(lang)) {
                path = FILE_CLASS_PATH + USER_INFO_FILE_NAME_EN;
            }
        }

        try (InputStream inputStream = resourceLoader.getResource(path).getInputStream()) {
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }
}