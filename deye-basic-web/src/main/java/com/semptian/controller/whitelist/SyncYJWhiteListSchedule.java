package com.semptian.controller.whitelist;

import com.semptian.service.WhiteListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc 全量同步白名单到YJ
 * @date 2024/10/28
 */
@Component
@Slf4j
public class SyncYJWhiteListSchedule {

    @Autowired
    private WhiteListService whiteListService;

    @Scheduled(cron = "${ics.sync.whitelist.cron:0 0 1 * * ?}")
    public void syncAllWhiteList() {
        whiteListService.syncAllWhiteList();
        log.info("sync all white list to yj success");
    }
}
