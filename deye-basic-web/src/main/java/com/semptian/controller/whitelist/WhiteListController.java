package com.semptian.controller.whitelist;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.semptian.base.service.ReturnModel;
import com.semptian.constant.CommonConstant;
import com.semptian.entity.WhiteListEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.WhiteListStatusEnum;
import com.semptian.enums.WhiteListTypeEnum;
import com.semptian.i18n.I18nUtils;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.param.WhiteListModel;
import com.semptian.param.WhiteListQueryParam;
import com.semptian.param.WhiteListVo;
import com.semptian.service.WhiteListService;
import com.semptian.utils.CsvUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 白名单controller
 * @date 2024/9/14
 */
@RestController
@RequestMapping("/white_list")
@Api(tags = "WhiteListController", value = "白名单")
@Slf4j
public class WhiteListController {

    @Autowired
    private WhiteListService whiteListService;

    @Autowired
    private ResourceLoader resourceLoader;

    @PostMapping("/save.json")
    @ApiOperation(value = "新增白名单", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public ReturnModel save(@RequestBody WhiteListModel param, HttpServletRequest request) {

        String userId = request.getHeader(CommonConstant.USER_ID_KEY);
        if (StringUtils.isEmpty(userId)) {
            log.error("userId is null");
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }

        param.setCreateUser(userId);
        param.setModifyUser(userId);
        return whiteListService.addOrUpdate(param);
    }

    @PostMapping("/modify.json")
    @ApiOperation(value = "修改白名单", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public ReturnModel modify(@RequestBody WhiteListModel param, HttpServletRequest request) {

        String userId = request.getHeader(CommonConstant.USER_ID_KEY);
        if (StringUtils.isEmpty(userId)) {
            log.error("userId is null");
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        if (param.getId() == null){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.id.is.empty"));
        }
        // 查询白名单状态，只有停控才能修改
        WhiteListEntity entity = whiteListService.getById(param.getId());
        if (entity == null){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.rule.not.exists"));
        }

        if (!WhiteListStatusEnum.STOP_CONTROL.getCode().equals(entity.getStatus())){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.prohibit.modify"));
        }

        param.setModifyUser(userId);
        return whiteListService.addOrUpdate(param);
    }

    @GetMapping("/status.json")
    @ApiOperation(value = "在控、停控、删除白名单", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public ReturnModel updateStatus(@RequestParam("ids") List<Long> ids,
                              @RequestParam("status") Integer status,
                              HttpServletRequest request) {

        String userId = request.getHeader(CommonConstant.USER_ID_KEY);
        if (StringUtils.isEmpty(userId)) {
            log.error("userId is null");
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        if (ids == null){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.id.is.empty"));
        }
        if (status == null || !(WhiteListStatusEnum.DELETE.getCode().equals(status)
                || WhiteListStatusEnum.IN_CONTROL.getCode().equals(status)
                || WhiteListStatusEnum.STOP_CONTROL.getCode().equals(status))
        ){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.status.is.error"));
        }

        return whiteListService.updateStatus(ids, status);
    }

    @ApiOperation(value = "查询白名单", notes = "根据查询信息获取白名单列表")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @RequestMapping(value = "/query_list.json", method = RequestMethod.GET)
    @OperateLog
    public Object getClueList(WhiteListQueryParam queryParam,
            HttpServletRequest request
    ) {
        String userId = request.getHeader(CommonConstant.USER_ID_KEY);
        if (StringUtils.isEmpty(userId)) {
            log.error("userId is null");
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }

        queryParam.setCreateUser(userId);
        return whiteListService.queryList(queryParam);
    }

    @GetMapping("/detail.json")
    @ApiOperation(value = "查询白名单详情", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @OperateLog
    public ReturnModel updateStatus(@RequestParam("id") Long id,
                                    HttpServletRequest request) {

        String userId = request.getHeader(CommonConstant.USER_ID_KEY);
        if (StringUtils.isEmpty(userId)) {
            log.error("userId is null");
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("common.userId.isnull"));
        }
        if (id == null){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.SERVICE_EXCEPTION.getCode()).setMsg(I18nUtils.getMessage("white.list.id.is.empty"));
        }

        WhiteListEntity entity = whiteListService.getById(id);
        if (entity == null){
            return ReturnModel.getInstance().error().setCode(ErrorCodeEnum.RESULT_DATA_NONE.getCode()).setMsg(I18nUtils.getMessage("white.list.rule.not.exists"));
        }

        return ReturnModel.getInstance().ok(new WhiteListVo(entity));
    }

    @ApiOperation(value = "查询所有在控白名单（数据治理使用）", notes = "获取所有在控白名单列表")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @RequestMapping(value = "/query_all_control.json", method = RequestMethod.GET)
    @OperateLog
    public Object queryAllControl(@RequestParam("sourceIp")String sourceIp,
                              @RequestParam("sourceType")String sourceType,
                              @RequestParam(name = "ruleType", required = false) List<Integer> ruleType
    ) {
        log.info("[{}][{}] get all control white list", sourceIp, sourceType);
        List<Map<String, Object>> datas = whiteListService.listMaps(new LambdaQueryWrapper<WhiteListEntity>()
                .select(WhiteListEntity::getId, WhiteListEntity::getFormatRule, WhiteListEntity::getEffectiveScope, WhiteListEntity::getType)
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.IN_CONTROL.getCode())
                .in(CollectionUtil.isNotEmpty(ruleType), WhiteListEntity::getType, ruleType)
                .orderByDesc(WhiteListEntity::getCreateTime)
        );

        // 将datas按照type分组
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        for (Map<String, Object> data : datas) {
            String type = data.get("type").toString();
            String typeName = WhiteListTypeEnum.getByCode(Integer.parseInt(type));

            List<Map<String, Object>> list = resultMap.get(typeName);
            if (list == null) {
                list = new ArrayList<>();
                resultMap.put(typeName, list);
            }
            list.add(data);
        }

        return ReturnModel.getInstance().ok(resultMap);
    }

    @ApiOperation(value = "白名单导入模板下载", httpMethod = "GET")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @RequestMapping(value = "/template_download.json", method = RequestMethod.GET)
    @OperateLog
    public void downloadTemplateFile(@RequestParam(name = "type") Integer type,
                                     HttpServletResponse response,
                                     HttpServletRequest request) {
        InputStream inputStream;

        try {
            String lang = request.getParameter("lang");
            String dict;
            if (WhiteListTypeEnum.IP.getCode().equals(type)){
                dict = "classpath:file/whitelist/ip/";
            }else if (WhiteListTypeEnum.RADIUS.getCode().equals(type)){
                dict = "classpath:file/whitelist/radius/";
            }else if (WhiteListTypeEnum.MOBILE_PHONE.getCode().equals(type)){
                dict = "classpath:file/whitelist/phone/";
            }else {
                throw new RuntimeException("type is error");
            }

            String fileName = "ImportTemplate_CN.xlsx";
            if (lang.equals("fr_DZ")) {
                fileName = "ImportTemplate_FR.xlsx";
            } else if (lang.equals("en_US")) {
                fileName = "ImportTemplate_EN.xlsx";
            }

            String path = dict + fileName;
            inputStream = resourceLoader.getResource(path).getInputStream();
            CsvUtils.exportHistoryFile(response, inputStream, FileUtil.getName(path));
        } catch (Exception e) {
            log.error(e.toString());
            throw new RuntimeException(e.getMessage());
        }
    }

    @PostMapping("/batch_import.json")
    @ApiOperation(value = "白名单特殊号码批量导入", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParam(name = "userId", value = "用户标识", required = true, dataType = "String", paramType = "header")
    @OperateLog
    public Object importBatchNum(@RequestParam(name = "file") MultipartFile file,
                                 @RequestParam(name = "type") Integer whiteListType,
                                 HttpServletRequest request){
        String curUserId = request.getHeader(CommonConstant.USER_ID_KEY);
        log.info("current user: {}", curUserId);
        String lang = request.getParameter("lang");
        Integer langType = 0;
        if (org.apache.commons.lang3.StringUtils.isEmpty(lang)) {
            langType = 0;
        }else {
            if (lang.equals("zh_CN")) {
                langType = 1;
            } else if (lang.equals("fr_DZ")) {
                langType = 2;
            } else if (lang.equals("en_US")) {
                langType = 3;
            }
        }
        if (!org.apache.commons.lang3.StringUtils.isEmpty(curUserId)) {
            return whiteListService.importBatch(file, curUserId, whiteListType, langType);
        }
        return ReturnModel.getInstance().setCode(ErrorCodeEnum.EXCEL_IMPORT_FALSE.getCode()).setMsg(I18nUtils.getMessage("case.business.isException"));
    }

    @ApiOperation(value = "白名单导出", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/export.json")
    public void export( @RequestParam(name = "fileType",  defaultValue = "1") Integer fileType,
                        @RequestParam(name = "whiteListTypes") List<Integer> whiteListTypes,
                          @RequestParam(name = "ids", defaultValue = "") String ids,
                          HttpServletResponse response) {
        whiteListService.export(fileType, whiteListTypes, ids, response);
    }

    @ApiOperation(value = "手动同步白名单到YJ", httpMethod = "GET", response = ReturnModel.class)
    @ApiResponses({
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/sync_to_yj.json")
    public ReturnModel syncWhiteListToYJ( ) {
        try {
            whiteListService.syncAllWhiteList();
            return ReturnModel.getInstance().ok();
        }catch (Exception e){
            log.error("sync white list to yj error", e);
            return ReturnModel.getInstance().error();
        }
    }
}
