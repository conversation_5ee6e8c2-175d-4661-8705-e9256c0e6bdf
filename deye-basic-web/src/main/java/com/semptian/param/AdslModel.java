package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ADSL", description = "ADSL")
public class AdslModel {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private Long id;

    /**
     * 上网账号
     */
    @ApiModelProperty(value = "上网账号", name = "onlineAccount", required = true)
    private String onlineAccount;

    /**
     * 账号持有人身份证号码
     */
    @ApiModelProperty(value = "账号持有人身份证号码", name = "identityCardNumber", required = true)
    private String identityCardNumber;

    /**
     * 账号持有人姓名
     */
    @ApiModelProperty(value = "账号持有人姓名", name = "accountHolderName", required = true)
    private String accountHolderName;

    /**
     * 设备组装电话
     */
    @ApiModelProperty(value = "设备组装电话", name = "assemblyPhone", required = true)
    private String assemblyPhone;

    /**
     * 城市代码
     */
    @ApiModelProperty(value = "城市代码", name = "cityCode", required = true)
    private String cityCode;

    /**
     * 安装位置
     */
    @ApiModelProperty(value = "安装位置", name = "installationPosition", required = false)
    private String installationPosition;

    /**
     * 账号持有人电话
     */
    @ApiModelProperty(value = "账号持有人电话", name = "accountHolderPhone", required = false)
    private String accountHolderPhone;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度", name = "longitude", required = false)
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度", name = "latitude", required = false)
    private String latitude;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", name = "address", required = false)
    private String address;

    /**
     * 运营商
     * 1.Telecom
     * 2.其他
     */
    @ApiModelProperty(value = "运营商", name = "ispCode", required = false)
    private Integer ispCode;


    /**
     * 存取模式
     * 1.专用网络真实IP地址
     * 2.专线
     * 3.ADSL号码输入
     * 4.ISDN
     * 5.普通号码
     * 6.电缆调制解调器编号
     * 7.电线
     * 8.无线上网
     * 9.其他连接方式
     */
    @ApiModelProperty(value = "存取模式", name = "accessMode", required = false)
    private Integer accessMode;
}
