package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-15 16:47
 **/
@Data
@ApiModel(value = "应用规则", description = "应用规则")
public class AppRuleModel {
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private String id;

    @ApiModelProperty(value = "名称", name = "name", required = true)
    private String name;

    @ApiModelProperty(value = "状态", name = "status", required = false)
    private Integer status;

    @ApiModelProperty(value = "描述", name = "description", required = false)
    private String description;

    @ApiModelProperty(value = "规则", name = "rule", required = false)
    private String rule;

    @ApiModelProperty(value = "ip", name = "rule", required = true)
    private String ip;

    @ApiModelProperty(value ="应用类型", name = "type", required = false)
    private String appType;

    @ApiModelProperty(value ="来源 0-预置, 1-自动更新, 2-用户导入", name = "type", required = false)
    private Integer type;
}
