package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "国家代码", description = "国家代码")
public class CountryCodeModel {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private Long id;

    /**
     * 国家码
     */
    @ApiModelProperty(value = "国家码", name = "countryCode", required = true)
    private String countryCode;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称", name = "countryName", required = false)
    private String countryName;

    /**
     * 国家英文名称
     */
    @ApiModelProperty(value = "国家英文名称", name = "countryEnName", required = false)
    private String countryEnName;
}
