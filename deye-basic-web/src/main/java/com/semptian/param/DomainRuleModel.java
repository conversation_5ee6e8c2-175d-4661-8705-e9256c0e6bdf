package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-15 16:07
 **/
@Data
@ApiModel(value = "域名规则", description = "域名规则")
public class DomainRuleModel {
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private String id;

    @ApiModelProperty(value = "名称", name = "name", required = true)
    private String name;

    @ApiModelProperty(value = "状态", name = "status", required = false)
    private Integer status;

    @ApiModelProperty(value = "描述", name = "description", required = false)
    private String description;

    @ApiModelProperty(value = "ip", name = "ip", required = true)
    private String ip;

    @ApiModelProperty(value = "规则", name = "rule", required = false)
    private String rule;

    @ApiModelProperty(value ="域名级别", name = "type", required = false)
    private Integer type;

}
