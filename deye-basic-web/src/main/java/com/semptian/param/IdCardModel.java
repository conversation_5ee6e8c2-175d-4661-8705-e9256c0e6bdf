package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "身份证信息Model", description = "身份证信息Model")
public class IdCardModel {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private Long id;

    /**
     * 互联网服务位置代码
     */
    @ApiModelProperty(value = "互联网服务位置代码", name = "netSiteId", required = true)
    private String netSiteId;

    /**
     * 互联网服务标题
     */
    @ApiModelProperty(value = "互联网服务标题", name = "netSiteName", required = false)
    private String netSiteName;

    /**
     * 上网人员姓名
     */
    @ApiModelProperty(value = "上网人员姓名", name = "userName", required = false)
    private String userName;

    /**
     * 房间编号
     */
    @ApiModelProperty(value = "房间编号", name = "roomId", required = false)
    private String roomId;

}
