package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "号码黑名单", description = "号码黑名单")
public class InvalidTelModel {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private Long id;

    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码", name = "phoneNumber", required = true)
    private String phoneNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark", required = false)
    private String remark;
}
