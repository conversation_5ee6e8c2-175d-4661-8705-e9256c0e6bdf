package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "IP库Model", description = "IP库Model")
public class IpLibraryModel {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id", required = true)
    private Long id;

    /**
     * 启动IP
     */
    @ApiModelProperty(value = "启动IP", name = "startIp", required = true)
    private String startIp;

    /**
     * 结束IP
     */
    @ApiModelProperty(value = "结束IP", name = "endIp", required = true)
    private String endIp;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称", name = "companyName", required = false)
    private String companyName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", name = "address", required = false)
    private String address;
}
