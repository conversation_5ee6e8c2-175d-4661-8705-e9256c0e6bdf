package com.semptian.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "号码段", description = "号码段")
public class NumberSectionModel {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id", required = false)
    private Long id;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", name = "msisdn", required = true)
    private String msisdn;

    /**
     * 城市代码
     */
    @ApiModelProperty(value = "城市代码", name = "cityCode", required = false)
    private String cityCode;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称", name = "cityName", required = false)
    private String cityName;

    /**
     * 网络类型
     */
    @ApiModelProperty(value = "网络类型", name = "netType", required = false)
    private String netType;

    /**
     * 邮编号
     */
    @ApiModelProperty(value = "邮编号", name = "postCode", required = false)
    private String postCode;

    /**
     * 区号
     */
    @ApiModelProperty(value = "区号", name = "areaCode", required = false)
    private String areaCode;

}
