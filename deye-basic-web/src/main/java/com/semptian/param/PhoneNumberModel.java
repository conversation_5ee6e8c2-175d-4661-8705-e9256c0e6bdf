/*
package com.semptian.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

*/
/**
 * 号码参数接收对象
 *
 * @Author: sk
 * @Date: 2020/12/17 12:20
 *//*


@Data
@ApiModel(value = "号码对象", description = "号码对象")
public class PhoneNumberModel {

    */
/**
     * 国家区号
     *//*

    @ApiModelProperty(value = "国家区号", name = "countryCode",required = true)
    private String countryCode;

    */
/**
     * 电话号码
     *//*

    @ApiModelProperty(value = "电话号码", name = "telephoneNum",required = true)
    private String telephoneNum;

    */
/**
     * 号码类型 1:电话号码 2:特殊号码
     *//*

    @ApiModelProperty(value = "号码类型", name = "phoneType",required = true)
    private Integer phoneType;


    */
/**
     * 线索号
     *//*

    @ApiModelProperty(value = "线索号", name = "clueId",required = false)
    private Long clueId;

    */
/**
     * 对象号 -100
     *//*

    @ApiModelProperty(value = "对象号", name = "objectId",required = false)
    private Integer objectId;

    */
/**
     * 手机号
     *//*

    @ApiModelProperty(value = "手机号", name = "clueName", required = false)
    private String clueName;

    */
/**
     * 线索类型
     *//*

    @ApiModelProperty(value = "线索类型",name = "clueType",required = false)
    private Integer clueType;

    */
/**
     * 创建时间
     *//*

    @ApiModelProperty(value = "创建时间",name = "createTime",required = false)
    private Long createTime;


    */
/**
     * 线索状态
     *//*

    @ApiModelProperty(value = "线索状态",name = "clueState",required = false)
    private Integer clueState;
    */
/**
     * 数据总数
     *//*

    @ApiModelProperty(value = "数据总数",name = "dataCount",required = false)
    private Integer dataCount;

    */
/**
     * 线索备注
     *//*

    @ApiModelProperty(value = "线索备注",name = "clueInfo",required = false)
    private String clueInfo;

    */
/**
     * 线索级别
     *//*

    @ApiModelProperty(value = "线索级别",name = "clueLevel",required = false)
    private Integer clueLevel;

    */
/**
     * 处理方式
     *//*

    @ApiModelProperty(value = "处理方式",name = "procMethod",required = false)
    private Integer procMethod;


    */
/**
     * 所属模块
     *//*

    @ApiModelProperty(value = "所属模块",name = "clueFrom",required = false)
    private Integer clueFrom;

    */
/**
     * 用户账号
     *//*

    @ApiModelProperty(value = "所属模块",name = "clueAddName",required = false)
    private String clueAddName;

    */
/**
     * 用户id
     *//*

    @JsonIgnore
    private Long userId;

}
*/
