#2022-10-18 B16P7PATCH 转测
1、替换jar包后重启
#更新功能点：
    1、修改固定ip地址库创建已存在的ip-提示未国际化
    2、修改重复添加相同固定IP产生的提示语问题
#重点测试
    1、固定IP新增
    2、新增提示语测试    
        
# 回归bug单
    DEYESII-10012

#2022-10-18 B16P7 转测
1、替换jar包后重启
#更新功能点：
    1、修改固定IP更新含有单引号导致长度超过限制
    2、固定IP批量插入一次超过2000条国际化，提示 每次最多导入2000条    
#重点测试
    1、固定IP更新    
        
# 回归bug单
    DEYESII-9996

#2022-10-17 B16P5 转测
1、替换jar包后重启
#更新功能点：
    1、修改固定IP进行插入、修改、查询时字段含有单引号导致的查询报错
    2、为批量插入时出生日期、成立时间添加最大设定时间，即当天的23:59:59，且不允许时间戳进行插入，
    只识别yyyy/MM/dd、dd/MM/yyyy
    3、修改固定Ip跳转查询为null问题，ip名称为::a
#重点测试
    1、固定IP批量插入，时间是否符合标准
    2、固定IP新增、编辑、查询时，查询或添加字段中含有引号
    3、固定Ip特殊Ip跳转问题
        
# 回归bug单
    DEYESII-9996、DEYESII-9943

# 2022-10-10 B16P04 转测
1、执行/sql/B16.oracle/FIXED_IP_ARCHIVE_UPDATE_B03.sql
2、替换jar包后重启
#更新功能点：
    1、修改固定IP资源库导入的日期不正确
    2、修改固定IP资源库政企模式下的“所属行业”字段去掉
    3、修改固定IP资源库每次导入限制2000条，需要在导入界面添加提示语，在导入时对数据行数做校验
    4、修改固定IP列表删除时无法实时判断是否有关注档案、是否已建档，会导致用户执行删除操作时无法给出正确的提示
    5、修改IP删除、档案恢复提示语修正
    6、修改固定IP Nombre d'employé、Numéro officiel值校验不正确
    7、新增固定IP实时判断IP档案状态接口，用户IP删除时提示用户IP信息    
## 重点测试
    1、固定IP批量导入测试
    2、固定IP删除、以及批量删除    
    3、固定IP新增（先删除，再新增，查看是否有关联历史信息提示）    
    
# 回归bug单
    DEYESII-9943、DEYESII-9942、DEYESII-9940、DEYESII-9933、DEYESII-9929、DEYESII-9919