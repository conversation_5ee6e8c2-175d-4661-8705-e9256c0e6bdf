#新增配置:

#域名自定义最大数量
domain.custom.max.num = 101
#应用自定义最大数量
app.custom.max.num = 101

#修改配置:
#clickhouse连接地址(ip和端口需要修改) warehouse
spring.datasource.dynamic.datasource.click_house.url = jdbc:clickhouse://**************:8124/dws?socket_timeout=3600000&send_timeout=3600000&receive_timeout=3600000
# 知识库自动更新的时间间隔, 会从昨天开始倒推到该值的天数
rule.data.autoUpdate.days = 3
# 知识库数据是否只包含443端口的数据
rule.data.onlyHttps = false
# 知识库更新-热门三级域名取值数量
rule.data.hot.tertiaryDomainNum = 750000
# 知识库更新-热门二级域名取值数量
rule.data.hot.secondaryDomainNum = 250000
# 知识库更新-应用规则库总数(预置+自动更新)
rule.data.hot.appMaxNum = 5000
# 知识库更新-域名规则库总数(预置+自动更新)
rule.data.hot.domainMaxNum = 4000000
