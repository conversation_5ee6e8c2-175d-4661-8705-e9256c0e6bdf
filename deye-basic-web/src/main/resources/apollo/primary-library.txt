server.port = 8098
server.tomcat.uri-encoding = utf-8
server.context-path = /basic_library
mybatis-plus.configuration.jdbc-type-for-null = null
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.id-type = 1
spring.thymeleaf.mode = LEGACYHTML5
spring.jackson.time-zone = GMT+8
spring.jackson.date-format = yyyy-MM-dd HH:mm:ss
spring.resources.static-locations = classpath:/img/,classpath:/META-INF/resources/,classpath:/file/

#数据源配置
spring.autoconfigure.exclude = com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.datasource.dynamic.primary = oracle
spring.datasource.dynamic.strict = false
spring.datasource.dynamic.datasource.mysql.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.mysql.url = *********************************************************************************************************************************************
spring.datasource.dynamic.datasource.mysql.username = de_dev
spring.datasource.dynamic.datasource.mysql.password = 123456
spring.datasource.dynamic.datasource.mysql.driver-class-name = com.mysql.jdbc.Driver

spring.datasource.dynamic.datasource.oracle.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.oracle.url = ********************************************
spring.datasource.dynamic.datasource.oracle.username = fhnsdb
spring.datasource.dynamic.datasource.oracle.password = fhnsdb
spring.datasource.dynamic.datasource.oracle.driver-class-name = oracle.jdbc.driver.OracleDriver

#clickhouse连接地址(需要修改) warehouse
spring.datasource.dynamic.datasource.click_house.url = ****************************************
#clickhouse连接用户名（需要修改）
spring.datasource.dynamic.datasource.click_house.username = default
#clickhouse连接密码（需要修改）
spring.datasource.dynamic.datasource.click_house.password = 123456
#####DruidDataSource配置#####################
spring.datasource.dynamic.datasource.click_house.druid.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.click_house.driver-class-name = ru.yandex.clickhouse.ClickHouseDriver
spring.datasource.dynamic.datasource.click_house.druid.initialSize = 20
spring.datasource.dynamic.datasource.click_house.druid.minIdle = 20
spring.datasource.dynamic.datasource.click_house.druid.maxActive = 100
# 配置获取连接等待超时的时间
spring.datasource.dynamic.datasource.click_house.druid.maxWait = 6000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.dynamic.datasource.click_house.druid.timeBetweenEvictionRunsMillis = 60000
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.dynamic.datasource.click_house.druid.poolPreparedStatements = true
spring.datasource.dynamic.datasource.click_house.druid.maxPoolPreparedStatementPerConnectionSize = 20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.dynamic.datasource.click_house.druid.filters = stat,slf4j
# spring.datasource.dynamic.datasource.click_house.druid.filter.stat.slow-sql-millis = 2000
# spring.datasource.dynamic.datasource.click_house.druid.filter.stat.log-slow-sql = true
# spring.datasource.dynamic.datasource.click_house.druid.filter.stat.merge-sql = true
# spring.datasource.dynamic.datasource.click_house.druid.filter.slf4j.dataSourceLogEnabled = true
# spring.datasource.dynamic.datasource.click_house.druid.filter.slf4j.statementExecutableSqlLogEnable = true
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.dynamic.datasource.click_house.druid.connectionProperties = druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#合并多个DruidDataSource的监控数据
spring.datasource.dynamic.datasource.click_house.druid.useGlobalDataSourceStat = true

spring.datasource.druid.initial-size = 5
spring.datasource.druid.max-active = 30
spring.datasource.druid.min-idle = 5
spring.datasource.druid.max-wait = 60000
spring.datasource.druid.pool-prepared-statements = true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size = 20
spring.datasource.druid.time-between-eviction-runs-millis = 60000
spring.datasource.druid.min-evictable-idle-time-millis = 30000
spring.datasource.druid.max-evictable-idle-time-millis = 60000
spring.datasource.druid.validation-query = SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle = true
spring.datasource.druid.test-on-borrow = false
spring.datasource.druid.test-on-return = false
spring.datasource.druid.stat-view-servlet.enabled = true
spring.datasource.druid.stat-view-servlet.url-pattern = /druid/*
spring.datasource.druid.stat-view-servlet.login-username = admin
spring.datasource.druid.stat-view-servlet.login-password = admin
spring.datasource.druid.filter.stat.log-slow-sql = true
spring.datasource.druid.filter.stat.slow-sql-millis = 1000
spring.datasource.druid.filter.stat.merge-sql = true
spring.datasource.druid.filter.wall.config.multi-statement-allow = true

spring.application.name = deye-basic-library
feign.hystrix.enabled = false
spring.messages.basename = i18n.messages
#国际化默认语言
i18n.default.lang = fr_DZ
system.appId = 76
server.tomcat.basedir = /semptian/tmp
operate-log-url = http://192.168.80.188:9300/log/add_log.json

upload.path = /basic/upload/

# 知识库自动更新的时间间隔, 会从昨天开始倒推到该值的天数
rule.data.autoUpdate.days = 7
# 知识库数据是否只包含443端口的数据
rule.data.onlyHttps = true
# 知识库更新-热门应用取值数量
rule.data.hot.appNum = 200
# 知识库更新-热门三级域名取值数量
rule.data.hot.tertiaryDomainNum = 1000
# 知识库更新-热门二级域名取值数量
rule.data.hot.secondaryDomainNum = 1000
# 知识库更新-应用对应的热门IP数量
rule.data.hot.appIpNum = 100
# 知识库更新-域名对应的热门IP数量
rule.data.hot.domainIpNum = 100
# 知识库更新-应用规则库总数(预置+自动更新)
rule.data.hot.appMaxNum = 5000
# 知识库更新-域名规则库总数(预置+自动更新)
rule.data.hot.domainMaxNum = 5000
# 知识库更新-三级域名提取规则
rule.data.domain.tertiary.extract = (([\w-]*\.?){2}\.(com|net|io|google|org|cn|app|mobi|tv|xyz|co|biz|info|top|pro|vip|video|tech|goog|bid|news|games|site|in|house|apple|art|cc|club|live|cloud|name|buzz|online|dz)(\.(cn|fr|ms|ru|me|dz|jp|asia|us|uk))?)$
# 知识库更新-二级域名提取规则
rule.data.domain.secondary.extract = (([\w-]*\.?){1}\.(com|net|io|google|org|cn|app|mobi|tv|xyz|co|biz|info|top|pro|vip|video|tech|goog|bid|news|games|site|in|house|apple|art|cc|club|live|cloud|name|buzz|online|dz)(\.(cn|fr|ms|ru|me|dz|jp|asia|us|uk))?)$
# 知识库更新-域名提取来源
rule.data.domain.extract.source = sub_domain
# 知识库更新-应用数据来源,多个用,隔开(201-URL,202-MAIL,203-CHAT,204-BBS_WEIBO,205-OTHER_LOG)
rule.data.app.source = 201,203,205
# 知识库更新-域名数据来源,多个用,隔开(201-URL,202-MAIL,203-CHAT,204-BBS_WEIBO,205-OTHER_LOG)
rule.data.domain.source = 201,205

# ORACLE批次插入条数大小
oracle.insert.batch.size = 100
