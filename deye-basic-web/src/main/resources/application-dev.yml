spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: oracle.jdbc.driver.OracleDriver
        druid:
            url: ********************************************
            username: fhnsdb
            password: fhnsdb
            initial-size: 8
            max-active: 10
            min-idle: 1
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                         multi-statement-allow: true