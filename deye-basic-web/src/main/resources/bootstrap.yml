spring:
  profiles:
    active: dev
  application:
    name: deye-basic-library-xh
  redis:
    cache-prefix: v62_test_basic_library_
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      #Nacos服务注册中心地址
      discovery:
        server-addr: 192.168.80.63:8848
      #Nacos作为配置中心地址
      config:
        server-addr: 192.168.80.63:8848
        namespace: semptian
        file-extension: properties
        group: DEFAULT_GROUP
        prefix: deye-basic-library

server:
  port: 8098
  servlet:
    context-path: /basic_library

#YJ ICS环境配置
ics:
  service:
    url: http://192.168.80.111:8070
  login:
    username: admin
    password: 123456
