#\u8FD9\u91CC\u586B\u5199\u4E2D\u6587\u7FFB\u8BD1
common.userId.isnull = User Id is empty
phonenumber.status.notexist = Phone status does not exist
phonenumber.name.exists = Phone name already exists
phonenumber.status.incontrol = In Control
phonenumber.status.stopcontrol = Stop control
phonenumber.status.inProcess = Processing
phonenumber.name.ordinary = Whitelist
phonenumber.name.special = Special number
phonenumber.type.isnull = Phone type is empty
phonenumber.name.isnull = Phone name is empty
country.code.empty = Country area code is empty
map.collection.empty = Map Collection is empty
parameters.of.the.abnormal = Parameter Invalid
export.failure = Export Failed
mac.illegal.or.null = MAC is invalid or empty
mask.illegal.or.null = IP Mask is invalid or empty
ip.illegal.or.null = IP is invalid or empty
ip.address.repeat = IP Duplication
ip.add.white.failure = IP Whitelist addition failed
ip.delete.white.failure = IP Whitelist deletion failed
failed.to.modify.state = Status Modification Failed
ip.edit.failure = IP Edit failed
status.notDelete = Control Status Cannot Be Deleted
case.business.isException = Business anomaly
export.rows.exceeds.maximum.limit= Export Quantity Exceeds Maximum Limit

import.fail = Import failed

import.white.isException = Import Exception Occurred
import.white.isEmpty = Import Content is Empty
import.white.content.isFalse = All import contents are illegal

import.exceed.max.rows= Import Quantity Exceeds Maximum Limit

time.out.ip.notInControl = There Are Valid Expired IPs That Cannot Be Set In Control

repeat.state.notDo = Cannot set duplicate status

updateTime = Update time
updateTime2 = Creation Time
clueStateStr = Status
IP.Mask = Mask
IP.Addr = IP Address
phoneStateStr = Status
phoneTypeStr = Phone type
fullNum = Phone Number


isp.type1 = Telecom
isp.type2 = other
baseStation.type1 = Basic base station
baseStation.type2 = Internal distribution
baseStation.type3 = Subway base station
baseStation.type4 = Other category base stations

adsl.lack.id = Id is empty
adsl.lack.onlineAccount = Internet account is empty
adsl.lack.identityCardNumber = Account Holder ID Number Empty
adsl.lack.accountHolderName = Account Holder Name Empty
adsl.lack.assemblyPhone = Device Assembly Phone is Empty
adsl.lack.cityCode = City Code is Empty
adsl.repeat.onlineAccount = Internet account already exists
ip.lack.id = Id is empty
ip.lack.startIp = Start IP is empty
ip.lack.endIp = End IP is empty
idcard.lack.id = Id is empty
idcard.lack.netSiteId = Internet service location code is empty
numberSection.lack.id = Id is empty
numberSection.lack.msisdn = Mobile Number is Empty
countryCode.lack.id = Id is empty
countryCode.lack.code = Country code is empty
countryCode.repeat.code = Country code already exists
invalidPhone.lack.phone = Phone Number is Empty
invalidPhone.lack.id = Id is empty
invalidPhone.repeat.phone = Phone Number Already Exists

adsl.entity.onlineAccount = Internet account
adsl.entity.accountHolderName = Account Holder Name
adsl.entity.identityCardNumber = Account Holder ID Number
adsl.entity.accountHolderPhone = Account Holder Phone
adsl.entity.contactName = Contact Person Name
adsl.entity.contactIdNumber = Contact Person ID Number
adsl.entity.contactNumber = Contact Person Phone
adsl.entity.assemblyPhone = Device installation phone
adsl.entity.installationPosition = Device installation location
adsl.entity.cityCode = City Code
adsl.entity.address = Address
adsl.entity.longitude = Longitude
adsl.entity.latitude = Latitude

idcard.entity.netSiteId = Internet service location code
idcard.entity.netSiteName = Internet service title
idcard.entity.cityCode = City Code
idcard.entity.cardId = Network card number
idcard.entity.userName = Internet user name
idcard.entity.certificateCode = Document ID Number
idcard.entity.certificateType = Document Type
idcard.entity.certificationUnit = Name of Issuing Unit
idcard.entity.nationality = Nationality
idcard.entity.companyName = Unit Name
idcard.entity.mobile = Contact Phone
idcard.entity.roomId = room number

numberSection.entity.msisdn = Phone Number
numberSection.entity.cityCode = City Code
numberSection.entity.cityName = City Name
numberSection.entity.netType = Network type
numberSection.entity.postCode = Postal Code
numberSection.entity.areaCode = Area code
numberSection.entity.isp = Operator

countryCode.entity.countryCode = Country code
countryCode.entity.countryName = Country name
countryCode.entity.countryEnName = Country English name

station.entity.baseStationId = Base station number
station.entity.isp = Operator
station.entity.cityCode = City Code
station.entity.longitude = Longitude
station.entity.latitude = Latitude
station.entity.baseStationType = Base station type
station.entity.baseStationName = Base station name
station.entity.baseStationAddress = Base station address
station.entity.geographicalIndication = Geographical Indication
station.entity.remark = Remark

ipLibrary.entity.startIp = Start IP
ipLibrary.entity.endIp = End IP
ipLibrary.entity.ipId = ipId

mobileRegister.entity.msisdn = Phone Number

invalidNumber.entity.phoneNumber = Mobile Number

station.baseStationId.same = Current Base Station Number Already Exists

parameter.is.empty = Parameter is Empty

mobile.same.number = This Phone Number Already Exists

file.parse.exception = File parsing error

inProcess.data.cannot.delete = Processing Data Cannot be Deleted

importOutOfSize = Import Supports A Maximum Of 100 Items
preset.data.cannot.delete = Automatically Updated Data Cannot Be Deleted
nameOrRuleExists = Name or IP Already Exists
nameExists = Name Already Exists
ruleExists = Rule already exists
name.cannot.null = Name Cannot Be Empty
appType.cannot.null = Application Category cannot be empty
ip.cannot.null = IP Cannot be empty
domain.cannot.null = Domain Name cannot be empty
domainType.cannot.null = Domain Name Category cannot be empty
file.type.error = Unsupported file format

customize = Custom
prefabrication = Preset
autoUpdate = Automatic Update
IP_CHECK = IP Format does not meet the requirements

ip.max.length= IP Content length should be within 2000 characters
domain.max.length= Domain Name content length should be within 255 characters
app.max.length= Name Content Length Should Be Within 128 Characters
domainType.illegal= Domain Name Category must be 2 or 3
description.max.length= Description Content Length Should Be Within 64 Characters
appType.max.length= Application Category content length should be within 30 characters
appType.not.exist = Application Category does not exist


domain.custom.max.num= Custom Domain Knowledge Base Quantity Cannot Exceed
app.custom.max.num= Custom Application Knowledge Base Quantity Cannot Exceed

fixed.ip.careStatus= Fixed IP archive are already followed
fixed.ip.archivesStatus= Fixed IP is archived
fixed.ip.batchTwo= Maximum 2000 Imports Per Time
fixed.ip.insertFail= Fixed IP addition failed, the IP already exists
fixed.ip.insertBlock= Add failed, fixed IP address count exceeds the maximum limit of 10,000
fixed.ip.entityError= Entity Type Abnormal, Must Be Individual Or Corporate
fixed.ip.updateError= Fixed IP update failed, the IP has been deleted

user.category.exists= User Category already exists
user.category.exists.user= When there is internet user information, cannot delete
user.name.exists= Internet user already exists
user.ip.belong.fixed.ip= This IP is a Fixed IP User
user.name.exists.another.user= This IP Already Exists in Other Users
user.remark.exists= Note name cannot be duplicated under the same category
user.type.1= Fixed network RADIUS
user.type.2= Mobile network RADIUS
user.type.3= Fixed IP address
user.type.4= Custom
user.type.is.empty= Internet user type cannot be empty
user.type.is.error= Internet user type error
user.name.is.empty= Internet user account cannot be empty
user.name.format.error= Internet user account format error
user.category.is.empty= User Category cannot be empty
user.category.not.exists= User Category does not exist
user.category.name.is.duplicate= User Category Name duplicated, please fill in the complete category name and separate with "/"
user.ip.not.in.fixed.ip= Current IP Not In Fixed IP Library


base.station.network.type= 2G Base station,3G Base station,4G Base station,5G Base station,Hybrid Base station
base.station.grade= Micro cell base station,small cell base station,distributed antenna station,micro station,macro station
base.station.unknown= unknown
base.station.export.title.stationNo= Base station number
base.station.export.title.stationAddress= Base station address
base.station.export.title.longitude= Longitude
base.station.export.title.latitude= Latitude
base.station.export.title.coverageRadius= Coverage RADIUS (m)
base.station.export.title.networkTypeName= Network type
base.station.export.title.gradeName= Level
base.station.export.title.networkOperatorName= Carrier
base.station.export.title.description= Description
base.station.export.title.modifyTimeName= Update time

baseStation.stationNo.is.empty= Base station number cannot be empty
baseStation.stationNo.is.exist= Base station number already exists
baseStation.stationNo.is.too.long= Base station number length cannot exceed 64
baseStation.latitude.is.empty= Base station latitude cannot be empty
baseStation.latitude.format.error= Base station latitude format error
baseStation.longitude.is.empty= Base station longitude cannot be empty
baseStation.longitude.format.error= Base station longitude format error
baseStation.latitude.longitude.is.exist= Base station longitude and latitude already exists
baseStation.coverageRadius.format.error= Coverage RADIUS Format Error
baseStation.networkType.is.empty= Network type cannot be empty
baseStation.networkType.error= Network type format error
baseStation.grade.is.empty= Grade cannot be empty
baseStation.grade.error= Grade format error
baseStation.networkOperator.is.empty= Operator cannot be empty
baseStation.networkOperator.error= Operator format error

phone.number.is.exist= Phone already exists
phone.number.type.1= Service Provider/Company Public Phone
phone.number.type.2= Scam Call
phone.number.type.3= Advertising sales call
phone.number.type.99= other
phone.number.is.empty= Phone cannot be empty
phone.number.format.error= Phone format error
phone.number.country.code.error= Phone country code error
phone.number.type.is.empty= Phone type cannot be empty
phone.number.type.error= Phone type error

# \u4EE5\u4E0B\u4E3A\u767D\u540D\u5355\u65B0\u589E
white.list.id.is.empty=ID cannot be empty
white.list.rule.is.empty=The content cannot be empty
white.list.type.is.error=White list type error
white.list.effective.scope.is.empty=The effective range cannot be empty
white.list.effective.scope.is.error=Effective scope error
white.list.country.code.is.empty=The country code cannot be empty
white.list.ip.is.error=IP format error
white.list.rule.already.exists=The whitelist already exists
white.list.status.is.error=Status error
white.list.rule.not.exists=The whitelist does not exist
white.list.status.type.0=delete
white.list.status.type.1=Starting control
white.list.status.type.16=Start control
white.list.status.type.4=Stopping control
white.list.status.type.8=Stop control
white.list.prohibit.modify=The whitelist is in a non stop control state and cannot be modified
white.list.export.title.countryCode=Country code
white.list.export.title.rule=Content
white.list.export.title.typeStr=Type
white.list.export.title.effectiveScope=Effective scope
white.list.export.title.remark=Remarks
white.list.rule.num.limit=The whitelist exceeds the upper limit, with a maximum value of 1000
white.list.phone.length.is.error=The length of the number should be 4-30
white.list.phone.is.error=The number must be a pure number
white.list.status.is.not.stopControl=This data is not in a stopped control state
white.list.status.is.not.control=This data is not in a controlled state
excel.format.error=Excel format error
import.excel.data.error=Line %d data error, cause: %s
import.excel.data.repeat=Duplicate whitelist on line %d, content: %s
import.excel.data.exist=The whitelist already exists on line %d, content: %s
white.list.radius.length.is.error=The length of a Radius account cannot exceed 64
