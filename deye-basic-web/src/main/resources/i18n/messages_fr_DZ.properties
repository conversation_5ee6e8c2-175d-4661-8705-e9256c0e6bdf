#\u6CD5\u8BED\u7FFB\u8BD1

common.userId.isnull =L'ID utilisateur est vide
phonenumber.status.notexist =L'\u00E9tat du num\u00E9ro n'existe pas
phonenumber.name.exists =Le nom du num\u00E9ro existe d\u00E9j\u00E0
phonenumber.status.incontrol =Contr\u00F4le
phonenumber.status.stopcontrol =Arr\u00EAtez le contr\u00F4le
phonenumber.status.inProcess =En cours de traitement
phonenumber.name.ordinary =Liste blanche
phonenumber.name.special =Num\u00E9ro sp\u00E9cial
phonenumber.type.isnull =Le type de num\u00E9ro est vide
phonenumber.name.isnull =Le nom du num\u00E9ro est vide
country.code.empty =Le code du pays est vide
map.collection.empty =la collection de cartes est vide
parameters.of.the.abnormal =Argument invalide
export.failure =L'exportation a \u00E9chou\u00E9
mac.illegal.or.null =MAC est ill\u00E9gal ou vide
ip.illegal.or.null =L'IP est ill\u00E9gale ou vide
ip.address.repeat =Duplication IP
ip.add.white.failure =\u00C9chec de l'ajout de la liste blanche IP
ip.delete.white.failure =\u00C9chec de la suppression de la liste blanche IP
failed.to.modify.state =La modification du statut a \u00E9chou\u00E9
ip.edit.failure =La modification IP a \u00E9chou\u00E9

status.notDelete =Ne peut pas \u00EAtre supprim\u00E9 sous contr\u00F4le

export.rows.exceeds.maximum.limit=Le nombre de lignes export\u00E9s d\u00E9passe la limite maximale

case.business.isException =Exception commerciale

import.fail = \u00C9chec de l'importation

import.white.isException =Exception \u00E0 l'importation

import.white.isEmpty = Le contenu import\u00E9 est vide

import.white.content.isFalse =Tout le contenu import\u00E9 est ill\u00E9gal

import.exceed.max.rows=Le nombre de lignes import\u00E9es d\u00E9passe la limite maximale

time.out.ip.notInControl =Il y a des adresses IP dont la dur\u00E9e de validit\u00E9 a expir\u00E9 ne peuvent pas \u00EAtre d\u00E9finies dans le contr\u00F4le

repeat.state.notDo =Impossible de d\u00E9finir le statut de duplication

updateTime =Mise \u00E0 jour
updateTime2 =Temps de cr\u00E9ation
clueStateStr =statut
IP.Mask =Masquer
IP.Addr =Adresse IP
phoneStateStr =Statut
phoneTypeStr =Type de num\u00E9ro
fullNum =Num\u00E9ro de t\u00E9l\u00E9phone

station.entity.baseStationId =Num\u00E9ro de la station de base
station.entity.isp =Op\u00E9rateur
station.entity.cityCode =Code de la ville
station.entity.longitude =longitude
station.entity.latitude =latitude
station.entity.baseStationType =Type de station de base
station.entity.baseStationName =Nom de la station de base
station.entity.baseStationAddress =Adresse de la station de base
station.entity.geographicalIndication =Indication g\u00E9ographique
station.entity.remark =Remarques

station.baseStationId.same =Le num\u00E9ro actuel de la station de base existe d\u00E9j\u00E0

parameter.is.empty =Le param\u00E8tre est vide

isp.type1 = Telecom
isp.type2 = D'autres
baseStation.type1 = Station de base de base
baseStation.type2 = Distribution interne
baseStation.type3 = Station de base du m\u00E9tro
baseStation.type4 = Autres types de stations de base

adsl.lack.id = l'identifiant est vide
adsl.lack.onlineAccount = Le compte Internet est vide
adsl.lack.identityCardNumber = Le num\u00E9ro d'identification du titulaire du compte est vide
adsl.lack.accountHolderName = Le nom du titulaire du compte est vide
adsl.lack.assemblyPhone = Le t\u00E9l\u00E9phone de l'assemblage de l'appareil est vide
adsl.lack.cityCode = Le code de la ville est vide
adsl.repeat.onlineAccount = Le compte Internet existe d\u00E9j\u00E0
ip.lack.id = l'identifiant est vide
ip.lack.startIp = L'IP de d\u00E9part est vide
ip.lack.endIp =  L'IP de fin est vide
idcard.lack.id = l'identifiant est vide
idcard.lack.netSiteId = Le code de localisation du service Internet est vide
numberSection.lack.msisdn = Le num\u00E9ro de t\u00E9l\u00E9phone portable est vide
numberSection.lack.id = l'identifiant est vide
countryCode.lack.id = l'identifiant est vide
countryCode.lack.code = Le code du pays est vide
countryCode.repeat.code = Le code de pays existe d\u00E9j\u00E0
invalidPhone.lack.phone = Le num\u00E9ro de t\u00E9l\u00E9phone est vide
invalidPhone.lack.id = l'identifiant est vide
invalidPhone.repeat.phone = Le num\u00E9ro de t\u00E9l\u00E9phone existe d\u00E9j\u00E0

adsl.entity.onlineAccount = Compte en ligne
adsl.entity.accountHolderName = Nom du titulaire du compte
adsl.entity.identityCardNumber = Num\u00E9ro d'identification du titulaire du compte
adsl.entity.accountHolderPhone = T\u00E9l\u00E9phone du titulaire du compte
adsl.entity.contactName = Nom du contact
adsl.entity.contactIdNumber = Num\u00E9ro d'identification du contact
adsl.entity.contactNumber = num\u00E9ro de contact
adsl.entity.assemblyPhone = T\u00E9l\u00E9phone de montage d'\u00E9quipement
adsl.entity.installationPosition = Emplacement d'installation de l'\u00E9quipement
adsl.entity.cityCode = Code de la ville
adsl.entity.address = adresse
adsl.entity.longitude = longitude
adsl.entity.latitude = latitude

idcard.entity.netSiteId = Code de localisation du service Internet
idcard.entity.netSiteName = Titre du service Internet
idcard.entity.cityCode = Code de la ville
idcard.entity.cardId = Num\u00E9ro de carte r\u00E9seau
idcard.entity.userName = Nom du personnel Internet
idcard.entity.certificateCode = Num\u00E9ro d'identification
idcard.entity.certificateType = type de certificat
idcard.entity.certificationUnit = Nom de l'unit\u00E9 \u00E9mettrice
idcard.entity.nationality = Pays de citoyennet\u00E9
idcard.entity.companyName = Nom de la compagnie
idcard.entity.mobile = num\u00E9ro de contact
idcard.entity.roomId = num\u00E9ro de chambre

numberSection.entity.msisdn = num\u00E9ro de t\u00E9l\u00E9phone
numberSection.entity.cityCode = Code de la ville
numberSection.entity.cityName = nom de la ville
numberSection.entity.netType = Type de r\u00E9seau
numberSection.entity.postCode = num\u00E9ro de poste
numberSection.entity.areaCode = Indicatif r\u00E9gional
numberSection.entity.isp = Op\u00E9rateur

countryCode.entity.countryCode = Code postal
countryCode.entity.countryName = Nom du pays
countryCode.entity.countryEnName = Nom anglais du pays

ipLibrary.entity.startIp = L'IP de d\u00E9part
ipLibrary.entity.endIp = L'IP de fin
ipLibrary.entity.ipId = ipId

mobileRegister.entity.msisdn = num\u00E9ro de t\u00E9l\u00E9phone

invalidNumber.entity.phoneNumber = num\u00E9ro de t\u00E9l\u00E9phone

mobile.same.number =Le num\u00E9ro de t\u00E9l\u00E9phone existe d\u00E9j\u00E0

inProcess.data.cannot.delete = Les donn\u00E9es de traitement ne peuvent pas \u00EAtre supprim\u00E9es

file.parse.exception = Erreurs de r\u00E9solution de fichiers
importOutOfSize = Les importations prennent en charge jusqu\u2019\u00E0 100 \u00E9l\u00E9ments de donn\u00E9es
preset.data.cannot.delete = Les donn\u00E9es de mise automatique ne peuvent pas \u00EAtre supprim\u00E9es
nameOrRuleExists = Le nom ou IP existe d\u00E9j\u00E0
nameExists = Le nom existe d\u00E9j\u00E0
ruleExists = La r\u00E8gle existe d\u00E9j\u00E0
name.cannot.null = Le nom ne peut pas \u00EAtre vide
appType.cannot.null = La cat\u00E9gorie d'application ne peut pas \u00EAtre vide
ip.cannot.null = L'IP ne peut pas \u00EAtre vide
domain.cannot.null = Le nom de domaine ne peut pas \u00EAtre vide
domainType.cannot.null = La cat\u00E9gorie de nom de domaine ne peut pas \u00EAtre vide
file.type.error = Format de fichier non support\u00E9

customize = Personnaliser
prefabrication = Pr\u00E9configur\u00E9
autoUpdate = Auto-actualisation
IP_CHECK = Format IP erron\u00E9

ip.max.length=La longueur du contenu IP doit \u00EAtre inf\u00E9rieure \u00E0 2000 caract\u00E8res
domain.max.length=La longueur du contenu nom de domaine doit \u00EAtre inf\u00E9rieure \u00E0 255 caract\u00E8res
app.max.length=La longueur du contenu nom doit \u00EAtre inf\u00E9rieure \u00E0 128 caract\u00E8res
domainType.illegal=La cat\u00E9gorie de nom de domaine doit \u00EAtre 2 ou 3
description.max.length=La longueur du contenu description doit \u00EAtre inf\u00E9rieure \u00E0 64 caract\u00E8res
appType.max.length=La longueur du contenu cat\u00E9gorie d'application doit \u00EAtre inf\u00E9rieure \u00E0 100 caract\u00E8res
appType.not.exist = La cat\u00E9gorie d'application n'existe pas

domain.custom.max.num=Le nombre de bases de domaine personnalis\u00E9es ne peut pas d\u00E9passer
app.custom.max.num=Le nombre de bases d''appliaction personnalis\u00E9es ne peut pas d\u00E9passer

fixed.ip.careStatus=IP fixe d\u00E9j\u00E0 abonn\u00E9
fixed.ip.archivesStatus=IP fixe fichier cr\u00E9\u00E9
fixed.ip.batchTwo=L'importation maximale est de 2000 \u00E0 la fois.
fixed.ip.insertFail=L'ajoute de l'adresse IP fixe est \u00E9chou\u00E9, elle existe d\u00E9j\u00E0
fixed.ip.insertBlock=\u00C9chec de l'ajout, le nombre d'adresses IP fixes d\u00E9passe la limite maximale de 10\u00A0000
fixed.ip.entityError=Le type d'entit\u00E9 est anormal, il doit s'agir d'un individu ou d'une entreprise gouvernementale
fixed.ip.updateError=L'actualisation de l'adress IP fixe est \u00E9chou\u00E9, elle a \u00E9t\u00E9 supprim\u00E9e 

user.category.exists=La classification des utilisateurs d'Internet existe d\u00E9j\u00E0
user.category.exists.user=La suppression n'est pas autoris\u00E9e lorsqu'il existe des informations sur les utilisateurs d'Internet
user.name.exists=Le compte utilisateur Internet existe d\u00E9j\u00E0
user.ip.belong.fixed.ip=Cette adresse IP appartient \u00E0 un utilisateur d'IP fixe
user.name.exists.another.user=Cette adresse IP existe d\u00E9j\u00E0 dans un autre utilisateur
user.remark.exists=Le nom de remarque ne peut pas \u00EAtre dupliqu\u00E9 dans la m\u00EAme classification
user.type.1=RAYON du r\u00E9seau fixe
user.type.2=RAYON du r\u00E9seau mobile
user.type.3=Adresse IP fixe
user.type.4=Utilisateur personnalis\u00E9
user.type.is.empty=Le type d'utilisateur ne peut pas \u00EAtre vide
user.type.is.error=Le type d'utilisateur est incorrect
user.name.is.empty=Le nom d'utilisateur ne peut pas \u00EAtre vide
user.name.format.error=Le nom d'utilisateur est incorrect
user.category.is.empty=La classification des utilisateurs ne peut pas \u00EAtre vide
user.category.not.exists=La classification des utilisateurs n'existe pas
user.category.name.is.duplicate=Le nom de la cat\u00E9gorie utilisateur est dupliqu\u00E9, veuillez remplir le nom complet de la cat\u00E9gorie et s\u00E9parer par "/"
user.ip.not.in.fixed.ip=L'adresse IP actuelle n'est pas dans la base de donn\u00E9es des adresses IP fixes

base.station.network.type=Borne d''acc\u00E8s 2G,Borne d''acc\u00E8s 3G,Borne d''acc\u00E8s 4G,Borne d''acc\u00E8s 5G,Station de base hybride
base.station.grade=station de base microcellulaire,station de base cellulaire,Station de base distribu\u00E9e,micro-site,station macro
base.station.unknown=Inconnu
base.station.export.title.stationNo=Num\u00E9ro de la base
base.station.export.title.stationAddress=Adresse de la borne d'acc\u00E8s
base.station.export.title.longitude=Longitude
base.station.export.title.latitude=Latitude
base.station.export.title.coverageRadius=Rayon de couverture(m)
base.station.export.title.networkTypeName=Type de r\u00E9seau
base.station.export.title.gradeName=grade
base.station.export.title.networkOperatorName=Propri\u00E9t\u00E9 de l'op\u00E9rateur
base.station.export.title.description=Description
base.station.export.title.modifyTimeName=Mise \u00E0 jour

baseStation.stationNo.is.empty=Le num\u00E9ro de station ne peut pas \u00EAtre vide
baseStation.stationNo.is.exist=Le num\u00E9ro de station existe d\u00E9j\u00E0
baseStation.stationNo.is.too.long=Le num\u00E9ro de station ne peut pas d\u00E9passer 64 caract\u00E8res
baseStation.latitude.is.empty=La latitude ne peut pas \u00EAtre vide
baseStation.latitude.format.error=Format de latitude incorrect
baseStation.longitude.is.empty=La longitude ne peut pas \u00EAtre vide
baseStation.longitude.format.error=Format de longitude incorrect
baseStation.latitude.longitude.is.exist=La latitude et la longitude de la station de base existent d\u00E9j\u00E0
baseStation.coverageRadius.format.error=Format de rayon de couverture incorrect
baseStation.networkType.is.empty=Type de r\u00E9seau ne peut pas \u00EAtre vide
baseStation.networkType.error=Type de r\u00E9seau incorrect
baseStation.grade.is.empty=Niveau ne peut pas \u00EAtre vide
baseStation.grade.error=Niveau incorrect
baseStation.networkOperator.is.empty=Op\u00E9rateur ne peut pas \u00EAtre vide
baseStation.networkOperator.error=Op\u00E9rateur de r\u00E9seau incorrect

phone.number.is.exist=Le num\u00E9ro existe d\u00E9j\u00E0
phone.number.type.1=T\u00E9l\u00E9phone public du fournisseur de services/de l''entreprise
phone.number.type.2=Appel frauduleux
phone.number.type.3=Appels de ventes publicitaires
phone.number.type.99=Autres
phone.number.is.empty=Le num\u00E9ro ne peut pas \u00EAtre vide
phone.number.format.error=Format de num\u00E9ro de t\u00E9l\u00E9phone incorrect
phone.number.country.code.error=Format de code pays incorrect
phone.number.type.is.empty=Le type de num\u00E9ro ne peut pas \u00EAtre vide
phone.number.type.error=Type de num\u00E9ro incorrect

# \u4EE5\u4E0B\u4E3A\u767D\u540D\u5355\u65B0\u589E
white.list.id.is.empty=ID ne peut pas \u00EAtre vide
white.list.rule.is.empty=Le contenu ne peut pas \u00EAtre vide
white.list.type.is.error=Erreur de type liste blanche
white.list.effective.scope.is.empty=La port\u00E9e effective ne peut pas \u00EAtre vide
white.list.effective.scope.is.error=Erreur de port\u00E9e effective
white.list.country.code.is.empty=Le code pays ne peut pas \u00EAtre vide
white.list.ip.is.error=Erreur de format IP
white.list.rule.already.exists=La liste blanche existe d\u00E9j\u00E0
white.list.status.is.error=Erreur de statut
white.list.rule.not.exists=La liste blanche n'existe pas
white.list.status.type.0=Supprimer
white.list.status.type.1=Dans le contr\u00F4le
white.list.status.type.16=En contr\u00F4le
white.list.status.type.4=Arr\u00EAt en contr\u00F4le
white.list.status.type.8=Arr\u00EAt contr\u00F4le
white.list.prohibit.modify=Statut non - stop sur liste blanche, modification interdite
white.list.export.title.countryCode=Code pays
white.list.export.title.rule=Contenu
white.list.export.title.typeStr=Type
white.list.export.title.effectiveScope=Port\u00E9e d'entr\u00E9e en vigueur
white.list.export.title.remark=Remarques
white.list.rule.num.limit=La liste blanche d\u00E9passe la limite sup\u00E9rieure, maximum 1000
white.list.phone.length.is.error=La longueur du num\u00E9ro devrait \u00EAtre 4 - 30
white.list.phone.is.error=Le num\u00E9ro doit \u00EAtre purement num\u00E9rique
white.list.status.is.not.stopControl=Cet \u00E9tat de donn\u00E9es non stop
white.list.status.is.not.control=Ces donn\u00E9es ne sont pas sous contr\u00F4le
excel.format.error=Erreur de format Excel
import.excel.data.error=Ligne %d erreur de donn\u00E9es, cause: %s
import.excel.data.repeat=La liste blanche de la ligne %d se r\u00E9p\u00E8te et se lit comme suit: %s
import.excel.data.exist=La liste blanche de la ligne %d existe d\u00E9j\u00E0 et se lit comme suit: %s
white.list.radius.length.is.error=Le num\u00E9ro de compte radius ne peut pas d\u00E9passer 64