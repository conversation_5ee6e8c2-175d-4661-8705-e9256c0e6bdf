#\u8FD9\u91CC\u586B\u5199\u4E2D\u6587\u7FFB\u8BD1
common.userId.isnull =\u7528\u6237Id\u4E3A\u7A7A
phonenumber.status.notexist =\u53F7\u7801\u72B6\u6001\u4E0D\u5B58\u5728
phonenumber.name.exists =\u53F7\u7801\u540D\u79F0\u5DF2\u5B58\u5728
phonenumber.status.incontrol =\u5728\u63A7
phonenumber.status.stopcontrol =\u505C\u63A7
phonenumber.status.inProcess =\u5904\u7406\u4E2D
phonenumber.name.ordinary =\u767D\u540D\u5355
phonenumber.name.special =\u7279\u6B8A\u53F7\u7801
phonenumber.type.isnull =\u53F7\u7801\u7C7B\u578B\u4E3A\u7A7A
phonenumber.name.isnull =\u53F7\u7801\u540D\u79F0\u4E3A\u7A7A
country.code.empty =\u56FD\u5BB6\u533A\u53F7\u4E3A\u7A7A
map.collection.empty =map\u96C6\u5408\u4E3A\u7A7A
parameters.of.the.abnormal =\u53C2\u6570\u65E0\u6548
export.failure =\u5BFC\u51FA\u5931\u8D25
mac.illegal.or.null =MAC\u4E0D\u5408\u6CD5\u6216\u4E3A\u7A7A
mask.illegal.or.null =IP\u63A9\u7801\u4E0D\u5408\u6CD5\u6216\u4E3A\u7A7A
ip.illegal.or.null =IP\u4E0D\u5408\u6CD5\u6216\u4E3A\u7A7A
ip.address.repeat =IP\u91CD\u590D
ip.add.white.failure =IP\u767D\u540D\u5355\u65B0\u589E\u5931\u8D25
ip.delete.white.failure =IP\u767D\u540D\u5355\u5220\u9664\u5931\u8D25
failed.to.modify.state =\u72B6\u6001\u4FEE\u6539\u5931\u8D25
ip.edit.failure =IP\u7F16\u8F91\u5931\u8D25
status.notDelete =\u5728\u63A7\u72B6\u6001\u4E0D\u80FD\u5220\u9664
case.business.isException =\u4E1A\u52A1\u5F02\u5E38
export.rows.exceeds.maximum.limit=\u5BFC\u51FA\u6761\u6570\u8D85\u8FC7\u6700\u5927\u9650\u5236

import.fail = \u5BFC\u5165\u5931\u8D25

import.white.isException =\u5BFC\u5165\u53D1\u751F\u5F02\u5E38
import.white.isEmpty = \u5BFC\u5165\u5185\u5BB9\u4E3A\u7A7A
import.white.content.isFalse =\u6240\u6709\u5BFC\u5165\u5185\u5BB9\u90FD\u4E0D\u5408\u6CD5

import.exceed.max.rows=\u5BFC\u5165\u6761\u6570\u8D85\u8FC7\u6700\u5927\u9650\u5236

time.out.ip.notInControl = \u5B58\u5728\u6709\u6548\u65F6\u95F4\u8FC7\u671F\u7684IP\u65E0\u6CD5\u8BBE\u7F6E\u5728\u63A7

repeat.state.notDo =\u65E0\u6CD5\u8BBE\u7F6E\u91CD\u590D\u72B6\u6001

updateTime =\u66F4\u65B0\u65F6\u95F4
updateTime2 =\u521B\u5EFA\u65F6\u95F4
clueStateStr =\u72B6\u6001
IP.Mask =\u63A9\u7801
IP.Addr =IP\u5730\u5740
phoneStateStr =\u72B6\u6001
phoneTypeStr =\u53F7\u7801\u7C7B\u578B
fullNum =\u7535\u8BDD\u53F7\u7801


isp.type1 = Telecom
isp.type2 = \u5176\u4ED6
baseStation.type1 = \u57FA\u672C\u57FA\u7AD9
baseStation.type2 = \u5185\u90E8\u5206\u5E03
baseStation.type3 = \u5730\u94C1\u57FA\u7AD9
baseStation.type4 = \u5176\u4ED6\u7C7B\u522B\u57FA\u7AD9

adsl.lack.id = id\u4E3A\u7A7A
adsl.lack.onlineAccount = \u4E0A\u7F51\u8D26\u53F7\u4E3A\u7A7A
adsl.lack.identityCardNumber = \u8D26\u53F7\u6301\u6709\u4EBA\u8EAB\u4EFD\u8BC1\u53F7\u7801\u4E3A\u7A7A
adsl.lack.accountHolderName = \u8D26\u53F7\u6301\u6709\u4EBA\u59D3\u540D\u4E3A\u7A7A
adsl.lack.assemblyPhone = \u8BBE\u5907\u7EC4\u88C5\u7535\u8BDD\u4E3A\u7A7A
adsl.lack.cityCode = \u57CE\u5E02\u4EE3\u7801\u4E3A\u7A7A
adsl.repeat.onlineAccount = \u4E0A\u7F51\u8D26\u53F7\u5DF2\u5B58\u5728
ip.lack.id = id\u4E3A\u7A7A
ip.lack.startIp = \u542F\u52A8IP\u4E3A\u7A7A
ip.lack.endIp =  \u7ED3\u675FIP\u4E3A\u7A7A
idcard.lack.id = id\u4E3A\u7A7A
idcard.lack.netSiteId = \u4E92\u8054\u7F51\u670D\u52A1\u4F4D\u7F6E\u4EE3\u7801\u4E3A\u7A7A
numberSection.lack.id = id\u4E3A\u7A7A
numberSection.lack.msisdn = \u624B\u673A\u53F7\u7801\u4E3A\u7A7A
countryCode.lack.id = id\u4E3A\u7A7A
countryCode.lack.code = \u56FD\u5BB6\u7801\u4E3A\u7A7A
countryCode.repeat.code = \u56FD\u5BB6\u7801\u5DF2\u5B58\u5728
invalidPhone.lack.phone = \u7535\u8BDD\u53F7\u7801\u4E3A\u7A7A
invalidPhone.lack.id = id\u4E3A\u7A7A
invalidPhone.repeat.phone = \u7535\u8BDD\u53F7\u7801\u5DF2\u5B58\u5728

adsl.entity.onlineAccount = \u4E0A\u7F51\u8D26\u53F7
adsl.entity.accountHolderName = \u8D26\u53F7\u6301\u6709\u4EBA\u59D3\u540D
adsl.entity.identityCardNumber = \u8D26\u53F7\u6301\u6709\u4EBA\u8EAB\u4EFD\u8BC1\u53F7\u7801
adsl.entity.accountHolderPhone = \u8D26\u53F7\u6301\u6709\u4EBA\u7535\u8BDD
adsl.entity.contactName = \u8054\u7CFB\u4EBA\u59D3\u540D
adsl.entity.contactIdNumber = \u8054\u7CFB\u4EBA\u8EAB\u4EFD\u8BC1\u53F7
adsl.entity.contactNumber = \u8054\u7CFB\u4EBA\u7535\u8BDD
adsl.entity.assemblyPhone = \u8BBE\u5907\u5B89\u88C5\u7535\u8BDD
adsl.entity.installationPosition = \u8BBE\u5907\u5B89\u88C5\u4F4D\u7F6E
adsl.entity.cityCode = \u57CE\u5E02\u4EE3\u7801
adsl.entity.address = \u5730\u5740
adsl.entity.longitude = \u7ECF\u5EA6
adsl.entity.latitude = \u7EAC\u5EA6

idcard.entity.netSiteId = \u4E92\u8054\u7F51\u670D\u52A1\u4F4D\u7F6E\u4EE3\u7801
idcard.entity.netSiteName = \u4E92\u8054\u7F51\u670D\u52A1\u6807\u9898
idcard.entity.cityCode = \u57CE\u5E02\u4EE3\u7801
idcard.entity.cardId = \u7F51\u5361\u53F7
idcard.entity.userName = \u4E0A\u7F51\u4EBA\u5458\u59D3\u540D
idcard.entity.certificateCode = \u8BC1\u4EF6\u53F7\u7801
idcard.entity.certificateType = \u8BC1\u4EF6\u7C7B\u578B
idcard.entity.certificationUnit = \u53D1\u8BC1\u5355\u4F4D\u540D\u79F0
idcard.entity.nationality = \u56FD\u7C4D
idcard.entity.companyName = \u5355\u4F4D\u540D\u79F0
idcard.entity.mobile = \u8054\u7CFB\u7535\u8BDD
idcard.entity.roomId = \u623F\u95F4\u53F7

numberSection.entity.msisdn = \u7535\u8BDD\u53F7\u7801
numberSection.entity.cityCode = \u57CE\u5E02\u4EE3\u7801
numberSection.entity.cityName = \u57CE\u5E02\u540D\u79F0
numberSection.entity.netType = \u7F51\u7EDC\u7C7B\u578B
numberSection.entity.postCode = \u90AE\u7F16\u53F7
numberSection.entity.areaCode = \u533A\u53F7
numberSection.entity.isp = \u8FD0\u8425\u5546

countryCode.entity.countryCode = \u56FD\u5BB6\u7801
countryCode.entity.countryName = \u56FD\u5BB6\u540D\u79F0
countryCode.entity.countryEnName = \u56FD\u5BB6\u82F1\u6587\u540D\u79F0

station.entity.baseStationId =\u57FA\u7AD9\u7F16\u53F7
station.entity.isp =\u8FD0\u8425\u5546
station.entity.cityCode =\u57CE\u5E02\u4EE3\u7801
station.entity.longitude =\u7ECF\u5EA6
station.entity.latitude =\u7EAC\u5EA6
station.entity.baseStationType =\u57FA\u7AD9\u7C7B\u578B
station.entity.baseStationName =\u57FA\u7AD9\u540D\u79F0
station.entity.baseStationAddress =\u57FA\u7AD9\u5730\u5740
station.entity.geographicalIndication =\u5730\u7406\u6807\u5FD7
station.entity.remark =\u5907\u6CE8

ipLibrary.entity.startIp = \u542F\u52A8IP
ipLibrary.entity.endIp = \u7ED3\u675FIP
ipLibrary.entity.ipId = ipId

mobileRegister.entity.msisdn = \u7535\u8BDD\u53F7\u7801

invalidNumber.entity.phoneNumber = \u624B\u673A\u53F7\u7801

station.baseStationId.same =\u5F53\u524D\u57FA\u7AD9\u7F16\u53F7\u5DF2\u5B58\u5728

parameter.is.empty =\u53C2\u6570\u4E3A\u7A7A

mobile.same.number =\u8BE5\u624B\u673A\u53F7\u7801\u5DF2\u5B58\u5728

file.parse.exception = \u6587\u4EF6\u89E3\u6790\u9519\u8BEF

inProcess.data.cannot.delete = \u5904\u7406\u4E2D\u7684\u6570\u636E\u65E0\u6CD5\u5220\u9664

importOutOfSize = \u5BFC\u5165\u6700\u591A\u652F\u6301100\u6761\u6570\u636E
preset.data.cannot.delete = \u81EA\u52A8\u66F4\u65B0\u6570\u636E\u65E0\u6CD5\u5220\u9664
nameOrRuleExists = \u540D\u79F0\u6216IP\u5DF2\u5B58\u5728
nameExists = \u540D\u79F0\u5DF2\u5B58\u5728
ruleExists = \u89C4\u5219\u5DF2\u5B58\u5728
name.cannot.null = \u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
appType.cannot.null = \u5E94\u7528\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
ip.cannot.null = IP\u4E0D\u80FD\u4E3A\u7A7A
domain.cannot.null = \u57DF\u540D\u4E0D\u80FD\u4E3A\u7A7A
domainType.cannot.null = \u57DF\u540D\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
file.type.error = \u6587\u4EF6\u683C\u5F0F\u4E0D\u652F\u6301

customize = \u81EA\u5B9A\u4E49
prefabrication = \u9884\u7F6E
autoUpdate = \u81EA\u52A8\u66F4\u65B0
IP_CHECK = ip\u683C\u5F0F\u4E0D\u7B26\u5408\u8981\u6C42

ip.max.length=ip\u5185\u5BB9\u957F\u5EA6\u5E94\u4E3A2000\u4E2A\u5B57\u7B26\u4EE5\u5185
domain.max.length=\u57DF\u540D\u5185\u5BB9\u957F\u5EA6\u5E94\u4E3A255\u4E2A\u5B57\u7B26\u4EE5\u5185
app.max.length=\u540D\u79F0\u5185\u5BB9\u957F\u5EA6\u5E94\u4E3A128\u4E2A\u5B57\u7B26\u4EE5\u5185
domainType.illegal=\u57DF\u540D\u5206\u7C7B\u5FC5\u987B\u4E3A2\u6216\u80053
description.max.length=\u63CF\u8FF0\u5185\u5BB9\u957F\u5EA6\u5E94\u4E3A64\u4E2A\u5B57\u7B26\u4EE5\u5185
appType.max.length=\u5E94\u7528\u5206\u7C7B\u5185\u5BB9\u957F\u5EA6\u5E94\u4E3A30\u4E2A\u5B57\u7B26\u4EE5\u5185
appType.not.exist = \u5E94\u7528\u5206\u7C7B\u4E0D\u5B58\u5728


domain.custom.max.num=\u81EA\u5B9A\u4E49\u57DF\u540D\u77E5\u8BC6\u5E93\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7
app.custom.max.num=\u81EA\u5B9A\u4E49\u5E94\u7528\u77E5\u8BC6\u5E93\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7

fixed.ip.careStatus=\u56FA\u5B9AIP\u6863\u6848\u5DF2\u5173\u6CE8
fixed.ip.archivesStatus=\u56FA\u5B9AIP\u5DF2\u5EFA\u6863
fixed.ip.batchTwo=\u6BCF\u6B21\u6700\u591A\u5BFC\u51652000\u6761
fixed.ip.insertFail=\u56FA\u5B9AIP\u6DFB\u52A0\u5931\u8D25\uFF0C\u8BE5IP\u5DF2\u5B58\u5728
fixed.ip.insertBlock=\u6DFB\u52A0\u5931\u8D25\uFF0C\u56FA\u5B9AIP\u5730\u5740\u4E2A\u6570\u8D85\u8FC7\u6700\u5927\u9650\u523610000
fixed.ip.entityError=\u5B9E\u4F53\u7C7B\u578B\u5F02\u5E38\uFF0C\u5FC5\u987B\u662F\u4E2A\u4EBA\u6216\u8005\u653F\u4F01
fixed.ip.updateError=\u56FA\u5B9AIP\u66F4\u65B0\u5931\u8D25\uFF0C\u8BE5IP\u5DF2\u5220\u9664

user.category.exists=\u7528\u6237\u5206\u7C7B\u5DF2\u5B58\u5728
user.category.exists.user=\u6709\u4E0A\u7F51\u7528\u6237\u4FE1\u606F\u65F6\uFF0C\u4E0D\u53EF\u5220\u9664
user.name.exists=\u4E0A\u7F51\u7528\u6237\u5DF2\u5B58\u5728
user.ip.belong.fixed.ip=\u8BE5IP\u4E3A\u56FA\u5B9AIP\u7528\u6237
user.name.exists.another.user=\u8BE5IP\u5DF2\u5B58\u5728\u4E8E\u5176\u5B83\u7528\u6237\u4E2D
user.remark.exists=\u540C\u4E00\u5206\u7C7B\u4E0B\u5907\u6CE8\u540D\u79F0\u4E0D\u53EF\u91CD\u590D
user.type.1=\u56FA\u7F51RADIUS
user.type.2=\u79FB\u52A8\u7F51RADIUS
user.type.3=\u56FA\u5B9AIP\u5730\u5740
user.type.4=\u81EA\u5B9A\u4E49
user.type.is.empty=\u4E0A\u7F51\u7528\u6237\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
user.type.is.error=\u4E0A\u7F51\u7528\u6237\u7C7B\u578B\u9519\u8BEF
user.name.is.empty=\u4E0A\u7F51\u7528\u6237\u8D26\u53F7\u4E0D\u80FD\u4E3A\u7A7A
user.name.format.error=\u4E0A\u7F51\u7528\u6237\u8D26\u53F7\u683C\u5F0F\u9519\u8BEF
user.category.is.empty=\u7528\u6237\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
user.category.not.exists=\u7528\u6237\u5206\u7C7B\u4E0D\u5B58\u5728
user.category.name.is.duplicate=\u7528\u6237\u5206\u7C7B\u540D\u79F0\u91CD\u590D,\u8BF7\u586B\u5199\u5B8C\u6574\u5206\u7C7B\u540D\u79F0\u5E76\u7528"/"\u5206\u9694
user.ip.not.in.fixed.ip=\u5F53\u524DIP\u672A\u5728\u56FA\u5B9AIP\u5E93\u4E2D


base.station.network.type=2G\u57FA\u7AD9,3G\u57FA\u7AD9,4G\u57FA\u7AD9,5G\u57FA\u7AD9,\u6DF7\u5408\u57FA\u7AD9
base.station.grade=\u5FAE\u5C0F\u533A\u57FA\u7AD9,\u5C0F\u533A\u57FA\u7AD9,\u5206\u5E03\u5F0F\u5929\u7EBF\u7AD9,\u5FAE\u7AD9,\u5B8F\u7AD9
base.station.unknown=\u672A\u77E5
base.station.export.title.stationNo=\u57FA\u7AD9\u7F16\u53F7
base.station.export.title.stationAddress=\u57FA\u7AD9\u5730\u5740
base.station.export.title.longitude=\u7ECF\u5EA6
base.station.export.title.latitude=\u7EAC\u5EA6
base.station.export.title.coverageRadius=\u8986\u76D6\u534A\u5F84(m)
base.station.export.title.networkTypeName=\u7F51\u7EDC\u7C7B\u578B
base.station.export.title.gradeName=\u7B49\u7EA7
base.station.export.title.networkOperatorName=\u5F52\u5C5E\u8FD0\u8425\u5546
base.station.export.title.description=\u63CF\u8FF0
base.station.export.title.modifyTimeName=\u66F4\u65B0\u65F6\u95F4

baseStation.stationNo.is.empty=\u57FA\u7AD9\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
baseStation.stationNo.is.exist=\u57FA\u7AD9\u7F16\u53F7\u5DF2\u5B58\u5728
baseStation.stationNo.is.too.long=\u57FA\u7AD9\u7F16\u53F7\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC764
baseStation.latitude.is.empty=\u57FA\u7AD9\u7EAC\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
baseStation.latitude.format.error=\u57FA\u7AD9\u7EAC\u5EA6\u683C\u5F0F\u9519\u8BEF
baseStation.longitude.is.empty=\u57FA\u7AD9\u7ECF\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
baseStation.longitude.format.error=\u57FA\u7AD9\u7ECF\u5EA6\u683C\u5F0F\u9519\u8BEF
baseStation.latitude.longitude.is.exist=\u57FA\u7AD9\u7ECF\u7EAC\u5EA6\u5DF2\u5B58\u5728
baseStation.coverageRadius.format.error=\u8986\u76D6\u534A\u5F84\u683C\u5F0F\u9519\u8BEF
baseStation.networkType.is.empty=\u7F51\u7EDC\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
baseStation.networkType.error=\u7F51\u7EDC\u7C7B\u578B\u9519\u8BEF
baseStation.grade.is.empty=\u7B49\u7EA7\u4E0D\u80FD\u4E3A\u7A7A
baseStation.grade.error=\u7B49\u7EA7\u9519\u8BEF
baseStation.networkOperator.is.empty=\u5F52\u5C5E\u8FD0\u8425\u5546\u4E0D\u80FD\u4E3A\u7A7A
baseStation.networkOperator.error=\u5F52\u5C5E\u8FD0\u8425\u5546\u9519\u8BEF

phone.number.is.exist=\u53F7\u7801\u5DF2\u5B58\u5728
phone.number.type.1=\u670D\u52A1\u5546/\u516C\u53F8\u516C\u7528\u7535\u8BDD
phone.number.type.2=\u8BC8\u9A97\u7535\u8BDD
phone.number.type.3=\u5E7F\u544A\u63A8\u9500\u7535\u8BDD
phone.number.type.99=\u5176\u4ED6
phone.number.is.empty=\u53F7\u7801\u4E0D\u80FD\u4E3A\u7A7A
phone.number.format.error=\u53F7\u7801\u683C\u5F0F\u9519\u8BEF
phone.number.country.code.error=\u53F7\u7801\u56FD\u5BB6\u7801\u9519\u8BEF
phone.number.type.is.empty=\u53F7\u7801\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
phone.number.type.error=\u53F7\u7801\u7C7B\u578B\u9519\u8BEF

# \u4EE5\u4E0B\u4E3A\u767D\u540D\u5355\u65B0\u589E
white.list.id.is.empty=id\u4E0D\u80FD\u4E3A\u7A7A
white.list.rule.is.empty=\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A
white.list.type.is.error=\u767D\u540D\u5355\u7C7B\u578B\u9519\u8BEF
white.list.effective.scope.is.empty=\u751F\u6548\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A
white.list.effective.scope.is.error=\u751F\u6548\u8303\u56F4\u9519\u8BEF
white.list.country.code.is.empty=\u56FD\u5BB6\u7801\u4E0D\u80FD\u4E3A\u7A7A
white.list.ip.is.error=ip\u683C\u5F0F\u9519\u8BEF
white.list.rule.already.exists=\u767D\u540D\u5355\u5DF2\u5B58\u5728
white.list.status.is.error=\u72B6\u6001\u9519\u8BEF
white.list.rule.not.exists=\u767D\u540D\u5355\u4E0D\u5B58\u5728
white.list.status.type.0=\u5220\u9664
white.list.status.type.1=\u5728\u63A7\u4E2D
white.list.status.type.16=\u5728\u63A7
white.list.status.type.4=\u505C\u63A7\u4E2D
white.list.status.type.8=\u505C\u63A7
white.list.prohibit.modify=\u767D\u540D\u5355\u975E\u505C\u63A7\u72B6\u6001\uFF0C\u7981\u6B62\u4FEE\u6539
white.list.export.title.countryCode=\u56FD\u5BB6\u7801
white.list.export.title.rule=\u767D\u540D\u5355\u5185\u5BB9
white.list.export.title.typeStr=\u767D\u540D\u5355\u7C7B\u578B
white.list.export.title.effectiveScope=\u751F\u6548\u8303\u56F4
white.list.export.title.remark=\u5907\u6CE8
white.list.rule.num.limit=\u767D\u540D\u5355\u8D85\u8FC7\u4E0A\u9650\uFF0C\u6700\u5927\u503C1000
white.list.phone.length.is.error=\u53F7\u7801\u957F\u5EA6\u5E94\u4E3A4-30
white.list.phone.is.error=\u53F7\u7801\u5FC5\u987B\u4E3A\u7EAF\u6570\u5B57
white.list.status.is.not.stopControl=\u8BE5\u6570\u636E\u975E\u505C\u63A7\u72B6\u6001
white.list.status.is.not.control=\u8BE5\u6570\u636E\u975E\u5728\u63A7\u72B6\u6001
excel.format.error=excel\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF
import.excel.data.error=\u7B2C%d\u884C\u6570\u636E\u9519\u8BEF\uFF0C\u539F\u56E0:%s
import.excel.data.repeat=\u7B2C%d\u884C\u767D\u540D\u5355\u91CD\u590D\uFF0C\u5185\u5BB9:%s
import.excel.data.exist=\u7B2C%d\u884C\u767D\u540D\u5355\u5DF2\u5B58\u5728\uFF0C\u5185\u5BB9:%s
white.list.radius.length.is.error=RADIUS\u8D26\u53F7\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC764