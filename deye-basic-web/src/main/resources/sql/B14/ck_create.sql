

-- 2. create local table
CREATE TABLE dws.dws_base_domain_rule_ip on cluster warehouse_cluster (
`insert_day` Int32,
`domain` String,
`ip` String,
`ip_type` Int32,
`port` Int32,
`behavior_num` Int64,
`insert_time` Int64)
ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/dws/dws_base_domain_rule_ip', '{replica}')
PARTITION BY insert_day
ORDER BY (domain, behavior_num, ip)
SETTINGS index_granularity = 8192, storage_policy = 'policy_semptian';

-- 3. create distribute table
CREATE TABLE dws.dws_base_domain_rule_ip_all on cluster warehouse_cluster (
`insert_day` Int32,
`domain` String,
`ip` String,
`ip_type` Int32,
`port` Int32,
`behavior_num` Int64,
`insert_time` Int64)
ENGINE = Distributed('warehouse_cluster', 'dws', 'dws_base_domain_rule_ip', rand());



CREATE TABLE dws.dws_base_domain_rule on cluster warehouse_cluster (
`insert_day` Int32,
`domain` String,
`domain_level` Int32,
`behavior_num` Int64)
ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/dws/dws_base_domain_rule', '{replica}')
PARTITION BY insert_day
ORDER BY (domain_level, behavior_num)
SETTINGS index_granularity = 8192, storage_policy = 'policy_semptian';

-- 3. create distribute table
CREATE TABLE dws.dws_base_domain_rule_all on cluster warehouse_cluster (
`insert_day` Int32,
`domain` String,
`domain_level` Int32,
`behavior_num` Int64)
ENGINE = Distributed('warehouse_cluster', 'dws', 'dws_base_domain_rule', rand());