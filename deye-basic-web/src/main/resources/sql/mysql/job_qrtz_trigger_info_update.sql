-- 创建定时更新知识库规则数据(每周日执行)
INSERT INTO `deye_job_schedule`.job_qrtz_trigger_info (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `job_type`, `el_job_id`, `job_trigger_type`, `job_start_time`, `job_expired_time`, `executor_fail_retry_interval`, `source_app_name`) VALUES (1, '0 20 2 ? * 1', '[basic_library][update]rule_update', CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(), 'admin', '', 'FIRST', 'httpJobHandler', 'http://192.168.11.1:8098/basic_library/schedule_task/rule_data_update.json', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', CURRENT_TIMESTAMP(), '', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
