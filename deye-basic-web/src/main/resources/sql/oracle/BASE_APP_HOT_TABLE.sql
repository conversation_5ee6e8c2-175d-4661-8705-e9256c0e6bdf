/*
 Navicat Premium Data Transfer

 Source Server         : dev-128
 Source Server Type    : Oracle
 Source Server Version : 110200
 Source Host           : **************:1521
 Source Schema         : FHNSDB

 Target Server Type    : Oracle
 Target Server Version : 110200
 File Encoding         : 65001

 Date: 23/07/2022 11:15:52
*/


-- ----------------------------
-- Table structure for BASE_APP_HOT_TABLE
-- ---------------------------
DECLARE
  num number;
BEGIN
  SELECT count(1) into num FROM all_tables WHERE OWNER = 'FHNSDB' AND TABLE_NAME = 'BASE_APP_HOT_TABLE';
  IF num > 0 THEN
    EXECUTE IMMEDIATE 'DROP TABLE "FHNSDB"."BASE_APP_HOT_TABLE"';
  END IF;
END;
CREATE TABLE "FHNSDB"."BASE_APP_HOT_TABLE" (
  "NAME" VARCHAR2(255 BYTE) NOT NULL,
  "LOWER_NAME" VARCHAR2(255 BYTE) NOT NULL,
  "DESCRIPTION" VARCHAR2(1024 BYTE),
  "CREATE_TIME" NUMBER(13,0) NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."BASE_APP_HOT_TABLE"."NAME" IS 'app名称,主键';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_HOT_TABLE"."LOWER_NAME" IS 'app名称小写';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_HOT_TABLE"."DESCRIPTION" IS '描述';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_HOT_TABLE"."CREATE_TIME" IS '创建时间';

-- ----------------------------
-- Records of BASE_APP_HOT_TABLE
-- ----------------------------
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('BT', 'bt', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('FlashGet', 'flashget', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('OpenVPN', 'openvpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('FaceTime', 'facetime', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('L2TP-vpn', 'l2tp-vpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SNMP', 'snmp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('eBay', 'ebay', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Xunlei', 'xunlei', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Spotify', 'spotify', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Viber', 'viber', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('ISAKMP', 'isakmp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Roblox', 'roblox', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Slack', 'slack', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('VPNBOOK', 'vpnbook', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Mijia', 'mijia', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Snapchat', 'snapchat', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('kuaishou', 'kuaishou', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Binance', 'binance', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Etsy', 'etsy', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Discord', 'discord', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WebRTC', 'webrtc', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tinder', 'tinder', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('mDNS', 'mdns', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SNTP', 'sntp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Amazon', 'amazon', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('UltraVPN', 'ultravpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('BaiduMap', 'baidumap', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Booking', 'booking', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Merchant', 'merchant', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Vimeo', 'vimeo', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Avast', 'avast', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Zalo', 'zalo', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Signal', 'signal', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Funshion', 'funshion', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Paypal', 'paypal', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('tmall', 'tmall', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('MP4Video', 'mp4video', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('NETBIOS', 'netbios', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Paysera', 'paysera', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('BBC News', 'bbc news', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('NTP', 'ntp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('1.1.1.1', '1.1.1.1', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('TopBuzz', 'topbuzz', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('TouchVPN', 'touchvpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('uTorrent', 'utorrent', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Netflix', 'netflix', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Deezer', 'deezer', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Wattpad', 'wattpad', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('VPN_Inf', 'vpn_inf', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Duolingo', 'duolingo', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Badoo', 'badoo', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Gameloft', 'gameloft', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Alibaba', 'alibaba', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('tftp', 'tftp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Telegram', 'telegram', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('RIP', 'rip', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('NFS', 'nfs', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('FreeFire', 'freefire', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WNS', 'wns', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('TikTok', 'tiktok', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Waze', 'waze', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('skype', 'skype', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tango', 'tango', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('P2PSrv', 'p2psrv', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('QUIC', 'quic', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Bit_VPN', 'bit_vpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Ichat', 'ichat', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Jumia', 'jumia', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SIP', 'sip', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Steam', 'steam', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('XVPN', 'xvpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('AutoCAD', 'autocad', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('QQGame', 'qqgame', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('QQ', 'qq', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('AnyDesk', 'anydesk', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WhatsApp', 'whatsapp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Religion', 'religion', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('OCSP', 'ocsp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Sports', 'sports', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Twitch', 'twitch', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('LDAP', 'ldap', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Zoom', 'zoom', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Hola', 'hola', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('YASSIR', 'yassir', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('LINE', 'line', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Psiphon', 'psiphon', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SSH', 'ssh', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Evernote', 'evernote', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Espn', 'espn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Coinbase', 'coinbase', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Gambling', 'gambling', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('URLTest', 'urltest', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('TamTam', 'tamtam', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('PandaVPN', 'pandavpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Canva', 'canva', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('BigoLive', 'bigolive', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SSDP', 'ssdp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Zynga Game', 'zynga game', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Mobile_Legends', 'mobile_legends', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Adult Content', 'adult content', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Pinduoduo', 'pinduoduo', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Facebook_Video', 'facebook_video', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Flickr[Browse]', 'flickr[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('TeamViewer', 'teamviewer', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WeChat_Moments', 'wechat_moments', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Kaspersky', 'kaspersky', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SemptianURLTest', 'semptianurltest', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Zendesk_Chat', 'zendesk_chat', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('IT Related', 'it related', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Health Care', 'health care', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WordPress', 'wordpress', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('PUBG_Mobile', 'pubg_mobile', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('VPN_Proxy_Master', 'vpn_proxy_master', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Pornography', 'pornography', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('IMO_Messenger', 'imo_messenger', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SSL Yahoo Mail', 'ssl yahoo mail', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('QQ-mobile', 'qq-mobile', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Facebook[Photo]', 'facebook[photo]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WebSocket', 'websocket', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Online Chat', 'online chat', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Facebook game', 'facebook game', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('RemoteDesktop', 'remotedesktop', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Avast update', 'avast update', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('HTTP_proxy', 'http_proxy', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Block_URL', 'block_url', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Avira_antivrus', 'avira_antivrus', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('MiTalk-Mobile', 'mitalk-mobile', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Mega[Browse]', 'mega[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Truecaller', 'truecaller', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Clash of clans', 'clash of clans', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Proxy(Web)', 'proxy(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Outlook[Browse]', 'outlook[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Call_Of_Duty', 'call_of_duty', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Web Tmall', 'web tmall', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Windscribe', 'windscribe', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Hangouts', 'google_hangouts', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('OneDrive[Browse]', 'onedrive[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Marketing', 'marketing', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Twitter[Browse]', 'twitter[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Online Shopping', 'online shopping', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Financial News', 'financial news', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google Maps', 'google maps', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Translate', 'google_translate', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('ExpressVPN', 'expressvpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tripadvisor', 'tripadvisor', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Web_WhatsApp', 'web_whatsapp', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Office365_Delve', 'office365_delve', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('AliExpress', 'aliexpress', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Search Engine', 'search engine', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Facebook[Browse]', 'facebook[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Game(Web)', 'game(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Microblog(Web)', 'microblog(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('NetworkNeighbors', 'networkneighbors', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('QCloud_Data', 'qcloud_data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Education', 'education', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Online Payment', 'online payment', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Windows_Store', 'windows_store', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Apple_Push_Base', 'apple_push_base', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Youku Video', 'youku video', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('JinRiTouTiao', 'jinritoutiao', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Sound_Cloud', 'sound_cloud', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('PC-WeChat', 'pc-wechat', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Culture & Art', 'culture & art', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('ThunderVPN', 'thundervpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('hikvision', 'hikvision', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Kitty_Live', 'kitty_live', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('OS Update', 'os update', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('VPN_Domain', 'vpn_domain', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('P2P Behavior', 'p2p behavior', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WhiteList_URL', 'whitelist_url', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('App Store', 'app store', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Forum(Web)', 'forum(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Candy Crush Saga', 'candy crush saga', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Mailbox(Web)', 'mailbox(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Microsoft Data', 'microsoft data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Dahua_Technology', 'dahua_technology', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Yahoo Data', 'yahoo data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Weiyun[browse]', 'weiyun[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WinMediaPlayer', 'winmediaplayer', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Office365', 'office365', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Crashlytics Data', 'crashlytics data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Clubhouse', 'clubhouse', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Huawei AppMarket', 'huawei appmarket', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Wikipedia', 'wikipedia', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Flipboard', 'flipboard', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google Data', 'google data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('YouTube[Video]', 'youtube[video]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('TELNETProtocol', 'telnetprotocol', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('360Safe update', '360safe update', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('HTTP-HEAD', 'http-head', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Game Information', 'game information', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('IT Industry', 'it industry', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Play', 'google_play', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('IPsec_VPN', 'ipsec_vpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Automobile', 'automobile', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Law Information', 'law information', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SecureVPN', 'securevpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Advertisement', 'advertisement', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tencent Data', 'tencent data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('YouTube[Browse]', 'youtube[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Xiaomi_Data', 'xiaomi_data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('HTTP-HTTP2.0', 'http-http2.0', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('pcAnywhere', 'pcanywhere', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Exchange MAPI', 'exchange mapi', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Gmail[Browse]', 'gmail[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tencent weixin', 'tencent weixin', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Opera_Mini', 'opera_mini', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Game_For_Peace', 'game_for_peace', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Yalla', 'yalla', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Linkedin[Browse]', 'linkedin[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('STUN_TURN', 'stun_turn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('News Portal', 'news portal', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('StarMaker', 'starmaker', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Loudtalks', 'loudtalks', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Life Information', 'life information', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Kaspersky update', 'kaspersky update', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Xunlei P2P', 'xunlei p2p', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Travel & Traffic', 'travel & traffic', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Opera Turbo', 'opera turbo', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Xiaomi AppMarket', 'xiaomi appmarket', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Custom_BlockURL', 'custom_blockurl', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Adobe update', 'adobe update', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Microsoft update', 'microsoft update', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Battle Net', 'battle net', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('smtp send mail', 'smtp send mail', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('AdGuardVPN', 'adguardvpn', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Reddit[Browse]', 'reddit[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('chirpwire', 'chirpwire', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Military & Weapon', 'military & weapon', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Office365_OneNote', 'office365_onenote', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Docs[Browse]', 'google_docs[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Drive[Browse]', 'google_drive[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Skype_File_Transfer', 'skype_file_transfer', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Remote_Desktop', 'google_remote_desktop', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tencent_GameAssistant', 'tencent_gameassistant', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('MediaFire[Browse]', 'mediafire[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Cloud_Messaging', 'google_cloud_messaging', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Amazon_Prime_Video', 'amazon_prime_video', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Skype&Teams_Voice_Video', 'skype&teams_voice_video', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('MI_Intelligent_Camera', 'mi_intelligent_camera', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Job-hunting & Employment', 'job-hunting & employment', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Private IP Addresses', 'private ip addresses', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('ByteDance_Basic_Services', 'bytedance_basic_services', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Instagram[Browse]', 'instagram[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Tencent_Games_Base', 'tencent_games_base', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Facebook_Messenger', 'facebook_messenger', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Microsoft_Account_Center', 'microsoft_account_center', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Social Contact(Web)', 'social contact(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('CounterStrike 1.6', 'counterstrike 1.6', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WhatsApp_File_Transfer', 'whatsapp_file_transfer', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Realty & Decoration', 'realty & decoration', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Online Video & Download', 'online video & download', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Nonprofit Organization', 'nonprofit organization', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Personal Website & Blog', 'personal website & blog', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Cloudinary[Browse]', 'cloudinary[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Viber_Voice_Video', 'viber_voice_video', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Entertainment News', 'entertainment news', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Outlook_Business[Browse]', 'outlook_business[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Software Download', 'software download', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Securities Quotes(Web)', 'securities quotes(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Microsoft_Teams[Browse]', 'microsoft_teams[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Google_Account_Center', 'google_account_center', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WeChat_Basic_Services', 'wechat_basic_services', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Government Organization', 'government organization', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('iCloud_Drive[browse]', 'icloud_drive[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SSL smtp send mail', 'ssl smtp send mail', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Pinterest[Browse]', 'pinterest[browse]', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Business Opportunity', 'business opportunity', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Network Storage(Web)', 'network storage(web)', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('WhiteListURL_Djezzy', 'whitelisturl_djezzy', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('SSL pop3 or imap receive mail', 'ssl pop3 or imap receive mail', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Video_Conference_Basic_Data', 'video_conference_basic_data', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('pop3 or imap receive mail', 'pop3 or imap receive mail', NULL, '*************');
INSERT INTO "FHNSDB"."BASE_APP_HOT_TABLE" VALUES ('Phishing & Malicious Website', 'phishing & malicious website', NULL, '*************');

-- ----------------------------
-- Primary Key structure for table BASE_APP_HOT_TABLE
-- ----------------------------
ALTER TABLE "FHNSDB"."BASE_APP_HOT_TABLE" ADD CONSTRAINT "SYS_C006457" PRIMARY KEY ("NAME");

-- ----------------------------
-- Checks structure for table BASE_APP_HOT_TABLE
-- ----------------------------
ALTER TABLE "FHNSDB"."BASE_APP_HOT_TABLE" ADD CONSTRAINT "SYS_C006453" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_HOT_TABLE" ADD CONSTRAINT "SYS_C006454" CHECK ("LOWER_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_HOT_TABLE" ADD CONSTRAINT "SYS_C006455" CHECK ("CREATE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
