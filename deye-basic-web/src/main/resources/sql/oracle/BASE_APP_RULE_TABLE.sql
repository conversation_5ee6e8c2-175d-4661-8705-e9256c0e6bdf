/*
 Navicat Premium Data Transfer

 Source Server         : dev-128
 Source Server Type    : Oracle
 Source Server Version : 110200
 Source Host           : **************:1521
 Source Schema         : FHNSDB

 Target Server Type    : Oracle
 Target Server Version : 110200
 File Encoding         : 65001

 Date: 30/05/2022 14:48:15
*/


-- ----------------------------
-- Table structure for BASE_APP_RULE_TABLE
-- ----------------------------
DECLARE
  num number;
BEGIN
  SELECT count(1) into num FROM all_tables WHERE OWNER = 'FHNSDB' AND TABLE_NAME = 'BASE_APP_RULE_TABLE';
  IF num > 0 THEN
    EXECUTE IMMEDIATE 'DROP TABLE "FHNSDB"."BASE_APP_RULE_TABLE"';
  END IF;
END;
CREATE TABLE "FHNSDB"."BASE_APP_RULE_TABLE" (
  "ID" NUMBER NOT NULL,
  "NAME" VARCHAR2(255 BYTE) NOT NULL,
  "RULE" VARCHAR2(4000 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "STATUS" NUMBER DEFAULT 1 NOT NULL,
  "TYPE" VARCHAR2(64 BYTE),
  "SOURCE_TYPE" NUMBER(1,0) DEFAULT 0 NOT NULL,
  "USER_ID" NUMBER NOT NULL,
  "CREATE_TIME" NUMBER(13,0) NOT NULL,
  "UPDATE_TIME" NUMBER(13,0) NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."ID" IS 'ID';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."NAME" IS '名称';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."RULE" IS '规则';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."DESCRIPTION" IS '描述';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."STATUS" IS '状态，0：停用  1：启用';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."TYPE" IS '类型';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."SOURCE_TYPE" IS '来源 0-预置, 1-自动更新, 2-用户导入';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."USER_ID" IS '用户ID';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."BASE_APP_RULE_TABLE"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Records of BASE_APP_RULE_TABLE
-- ----------------------------
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('1', 'IT Industry', 'IT Industry', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('2', 'Google Data', 'Google Data', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('3', 'Facebook', 'Facebook', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('4', 'Facebook_Messenger', 'Facebook_Messenger', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('5', 'QUIC', 'QUIC', NULL, '1', 'NET Protocol', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('6', 'IT Related', 'IT Related', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('7', 'Google_Play', 'Google_Play', NULL, '1', 'Download Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('8', 'YouTube', 'YouTube', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('9', 'Advertisement', 'Advertisement', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('10', 'TikTok', 'TikTok', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('11', 'Instagram', 'Instagram', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('12', 'Software Download', 'Software Download', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('13', 'Search Engine', 'Search Engine', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('14', 'Viber', 'Viber', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('15', 'VPN_Domain', 'VPN_Domain', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('16', 'Google_Account_Center', 'Google_Account_Center', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('17', 'Microsoft Data', 'Microsoft Data', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('18', 'Snapchat', 'Snapchat', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('19', 'WhatsApp', 'WhatsApp', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('20', 'Gmail', 'Gmail', NULL, '1', 'Mail', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('21', 'Truecaller', 'Truecaller', NULL, '1', 'Life-Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('22', 'Google_Hangouts', 'Google_Hangouts', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('23', 'Crashlytics Data', 'Crashlytics Data', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('24', 'Facebook_Video', 'Facebook_Video', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('25', 'Game Information', 'Game Information', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('26', 'Online Shopping', 'Online Shopping', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('27', 'Twitter', 'Twitter', NULL, '1', 'Microblog', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('28', 'iCloud_Drive', 'iCloud_Drive', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('29', 'Psiphon', 'Psiphon', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('30', 'IMO_Messenger', 'IMO_Messenger', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('31', 'News Portal', 'News Portal', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('32', 'Custom_BlockURL', 'Custom_BlockURL', NULL, '1', 'Custom_BlockURL', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('33', 'Social Contact(Web)', 'Social Contact(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('34', 'Linkedin', 'Linkedin', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('35', 'Entertainment News', 'Entertainment News', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('36', 'Network Storage(Web)', 'Network Storage(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('37', 'Google Maps', 'Google Maps', NULL, '1', 'Life-Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('38', 'skype', 'skype', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('39', 'Xiaomi AppMarket', 'Xiaomi AppMarket', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('40', 'Pornography', 'Pornography', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('41', 'Pinterest', 'Pinterest', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('42', 'Netflix', 'Netflix', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('43', 'Yahoo Data', 'Yahoo Data', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('44', 'Business Opportunity', 'Business Opportunity', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('45', 'Life Information', 'Life Information', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('46', 'chirpwire', 'chirpwire', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('47', 'Mijia', 'Mijia', NULL, '1', 'IoT', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('48', 'Marketing', 'Marketing', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('49', 'Online Video & Download', 'Online Video & Download', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('50', 'Google_Translate', 'Google_Translate', NULL, '1', 'OA', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('51', 'FreeFire', 'FreeFire', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('52', 'Microsoft_Account_Center', 'Microsoft_Account_Center', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('53', 'XVPN', 'XVPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('54', 'Personal Website & Blog', 'Personal Website & Blog', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('55', 'WhiteListURL_Djezzy', 'WhiteListURL_Djezzy', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('56', 'OneDrive', 'OneDrive', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('57', 'WhatsApp_File_Transfer', 'WhatsApp_File_Transfer', NULL, '1', 'File Transfer', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('58', 'Online Chat', 'Online Chat', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('59', 'Huawei AppMarket', 'Huawei AppMarket', NULL, '1', 'Download Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('60', 'Spotify', 'Spotify', NULL, '1', 'P2P Stream Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('61', 'Forum(Web)', 'Forum(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('62', 'Outlook_Business', 'Outlook_Business', NULL, '1', 'Mail', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('63', 'Sports', 'Sports', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('64', 'Travel & Traffic', 'Travel & Traffic', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('65', 'Game(Web)', 'Game(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('66', 'OS Update', 'OS Update', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('67', 'Kaspersky', 'Kaspersky', NULL, '1', 'Soft-update', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('68', 'Mailbox(Web)', 'Mailbox(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('69', 'Funshion', 'Funshion', NULL, '1', 'P2P Stream Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('70', 'TomatoVPN', 'TomatoVPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('71', 'VPNBOOK', 'VPNBOOK', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('72', 'Tencent_Games_Base', 'Tencent_Games_Base', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('73', 'TELNETProtocol', 'TELNETProtocol', NULL, '1', 'Remote Login', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('74', 'Amazon', 'Amazon', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('75', 'Opera Turbo', 'Opera Turbo', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('76', 'Government Organization', 'Government Organization', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('77', 'tmall', 'tmall', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('78', 'Alibaba', 'Alibaba', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('79', 'Education', 'Education', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('80', 'Microsoft_Teams', 'Microsoft_Teams', NULL, '1', 'OA', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('81', 'PUBG_Mobile', 'PUBG_Mobile', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('82', 'Yalla', 'Yalla', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('83', 'Google_Cloud_Messaging', 'Google_Cloud_Messaging', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('84', 'Financial News', 'Financial News', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('85', 'AliExpress', 'AliExpress', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('86', 'SSL Yahoo Mail', 'SSL Yahoo Mail', NULL, '1', 'Mail', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('87', 'WhiteList_URL', 'WhiteList_URL', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('88', 'Google_Drive', 'Google_Drive', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('89', 'Discord', 'Discord', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('90', 'SecureVPN', 'SecureVPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('91', 'Game_For_Peace', 'Game_For_Peace', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('92', 'Roblox', 'Roblox', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('93', 'Health Care', 'Health Care', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('94', 'Google_Docs', 'Google_Docs', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('95', 'Flipboard', 'Flipboard', NULL, '1', 'News-Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('96', 'TopBuzz', 'TopBuzz', NULL, '1', 'News-Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('97', 'Twitch', 'Twitch', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('98', 'Wikipedia', 'Wikipedia', NULL, '1', 'Book-Encyclopedia', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('99', 'Office365_OneNote', 'Office365_OneNote', NULL, '1', 'OA', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('102', 'Candy Crush Saga', 'Candy Crush Saga', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('103', 'Chictalk_whiteListURL', 'Chictalk_whiteListURL', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('104', 'Military & Weapon', 'Military & Weapon', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('105', 'Booking', 'Booking', NULL, '1', 'Travel-Traffic', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('106', 'BaiduMap', 'BaiduMap', NULL, '1', 'Life-Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('107', 'StarMaker', 'StarMaker', NULL, '1', 'P2P Stream Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('108', 'App Store', 'App Store', NULL, '1', 'Download Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('109', 'Amazon_Prime_Video', 'Amazon_Prime_Video', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('110', 'Wattpad', 'Wattpad', NULL, '1', 'Book-Encyclopedia', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('100', 'Gambling', 'Gambling', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('101', 'Realty & Decoration', 'Realty & Decoration', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('111', 'Web Tmall', 'Web Tmall', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('112', 'Binance', 'Binance', NULL, '1', 'Internet Finance', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('113', 'SSH', 'SSH', NULL, '1', 'Remote Login', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('114', 'Outlook', 'Outlook', NULL, '1', 'Mail', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('115', 'JinRiTouTiao', 'JinRiTouTiao', NULL, '1', 'News-Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('116', 'Law Information', 'Law Information', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('117', 'Office365', 'Office365', NULL, '1', 'OA', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('118', 'Jumia', 'Jumia', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('119', 'NTP', 'NTP', NULL, '1', 'NET Protocol', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('120', 'Merchant', 'Merchant', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('121', 'Phishing & Malicious Website', 'Phishing & Malicious Website', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('122', 'ByteDance_Basic_Services', 'ByteDance_Basic_Services', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('123', 'TamTam', 'TamTam', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('124', 'Duolingo', 'Duolingo', NULL, '1', 'Education-Learn', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('125', 'VPN_Inf', 'VPN_Inf', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('126', 'AdGuardVPN', 'AdGuardVPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('127', 'Tencent_GameAssistant', 'Tencent_GameAssistant', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('128', 'Signal', 'Signal', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('129', 'YASSIR', 'YASSIR', NULL, '1', 'Life-Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('130', 'Automobile', 'Automobile', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('131', 'Bit_VPN', 'Bit_VPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('132', 'QCloud_Data', 'QCloud_Data', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('133', 'Fortune Teller', 'Fortune Teller', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('134', 'Nonprofit Organization', 'Nonprofit Organization', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('135', 'Tencent weixin', 'Tencent weixin', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('136', 'Tinder', 'Tinder', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('137', 'Culture & Art', 'Culture & Art', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('138', 'Apple_Push_Base', 'Apple_Push_Base', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('139', 'Securities Quotes(Web)', 'Securities Quotes(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('140', 'Reddit', 'Reddit', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('141', 'Paypal', 'Paypal', NULL, '1', 'Internet Finance', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('142', 'Xiaomi_Data', 'Xiaomi_Data', NULL, '1', 'SSL Data', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('143', 'LINE', 'LINE', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('144', 'Facebook game', 'Facebook game', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('145', 'MediaFire', 'MediaFire', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('146', 'Vimeo', 'Vimeo', NULL, '1', 'Web Streaming Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('147', 'MiTalk-Mobile', 'MiTalk-Mobile', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('148', 'eBay', 'eBay', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('149', 'ExpressVPN', 'ExpressVPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('150', 'Telegram', 'Telegram', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('151', 'Flickr', 'Flickr', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('152', 'Zoom', 'Zoom', NULL, '1', 'Net Meeting', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('153', 'Web_WhatsApp', 'Web_WhatsApp', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('154', 'WordPress', 'WordPress', NULL, '1', 'OA', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('155', 'Online Payment', 'Online Payment', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('156', 'Skype&Teams_Voice_Video', 'Skype&Teams_Voice_Video', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('157', 'Office365_Delve', 'Office365_Delve', NULL, '1', 'OA', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('158', 'Adult Content', 'Adult Content', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('159', 'Private IP Addresses', 'Private IP Addresses', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('160', 'WeChat_Basic_Services', 'WeChat_Basic_Services', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('161', 'Religion', 'Religion', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('162', 'Badoo', 'Badoo', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('163', 'UltraVPN', 'UltraVPN', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('164', 'Proxy(Web)', 'Proxy(Web)', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('165', 'Gameloft', 'Gameloft', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('166', 'BBC News', 'BBC News', NULL, '1', 'News-Media', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('167', 'Joybuy', 'Joybuy', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('168', 'Avira_antivrus', 'Avira_antivrus', NULL, '1', 'Soft-update', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('169', 'Weiyun', 'Weiyun', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('170', 'Adobe update', 'Adobe update', NULL, '1', 'Soft-update', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('171', 'URLTest', 'URLTest', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('172', 'Steam', 'Steam', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('173', 'Hola', 'Hola', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('174', 'Pinduoduo', 'Pinduoduo', NULL, '1', 'Shopping', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('175', 'Windows_Store', 'Windows_Store', NULL, '1', 'Download Tools', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('176', 'Job-hunting & Employment', 'Job-hunting & Employment', NULL, '1', 'Visit Web Site', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('177', 'Loudtalks', 'Loudtalks', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('178', 'Paysera', 'Paysera', NULL, '1', 'Internet Finance', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('179', 'Skype_File_Transfer', 'Skype_File_Transfer', NULL, '1', 'File Transfer', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('180', 'Canva', 'Canva', NULL, '1', 'Book-Encyclopedia', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('181', 'Sound_Cloud', 'Sound_Cloud', NULL, '1', 'Mobile applications', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('182', 'Scribd', 'Scribd', NULL, '1', 'Network storage', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('183', 'TeamViewer', 'TeamViewer', NULL, '1', 'Remote Login', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('184', 'Windscribe', 'Windscribe', NULL, '1', 'ProxyTool', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('185', 'Clubhouse', 'Clubhouse', NULL, '1', 'Social Networking', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('186', 'Zendesk_Chat', 'Zendesk_Chat', NULL, '1', 'IM', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('187', 'Coinbase', 'Coinbase', NULL, '1', 'Internet Finance', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('188', 'SSDP', 'SSDP', NULL, '1', 'NET Protocol', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('189', 'Zynga Game', 'Zynga Game', NULL, '1', 'Game', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('190', 'HTTP_proxy', 'HTTP_proxy', NULL, '1', 'NET Protocol', '0', '1', '*************', '*************');
INSERT INTO "FHNSDB"."BASE_APP_RULE_TABLE" VALUES ('191', 'Slack', 'Slack', NULL, '1', 'OA', '0', '1', '*************', '*************');

-- ----------------------------
-- Checks structure for table BASE_APP_RULE_TABLE
-- ----------------------------
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006312" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006313" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006316" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006317" CHECK ("SOURCE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006318" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006319" CHECK ("CREATE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_APP_RULE_TABLE" ADD CONSTRAINT "SYS_C006320" CHECK ("UPDATE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table BASE_APP_RULE_TABLE
-- ----------------------------
CREATE UNIQUE INDEX "FHNSDB"."BASE_APP_RULE_TABLE_ID_IDX"
  ON "FHNSDB"."BASE_APP_RULE_TABLE" ("ID" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- SEQUENCE for table BASE_APP_RULE_TABLE
-- ----------------------------
DECLARE
  num number;
BEGIN
  SELECT count(1) into num FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_BASE_APP_RULE_ID';
  IF num > 0 THEN
    EXECUTE IMMEDIATE 'DROP SEQUENCE FHNSDB.SEQ_BASE_APP_RULE_ID';
  END IF;
END;
CREATE SEQUENCE FHNSDB.SEQ_BASE_APP_RULE_ID
MINVALUE 210
MAXVALUE 9999999999999
START WITH 210
INCREMENT BY 1
cache 20;