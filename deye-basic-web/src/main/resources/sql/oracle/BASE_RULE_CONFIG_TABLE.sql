/*
 Navicat Premium Data Transfer

 Source Server         : dev-128
 Source Server Type    : Oracle
 Source Server Version : 110200
 Source Host           : **************:1521
 Source Schema         : FHNSDB

 Target Server Type    : Oracle
 Target Server Version : 110200
 File Encoding         : 65001

 Date: 30/05/2022 14:45:31
*/


-- ----------------------------
-- Table structure for BASE_RULE_CONFIG_TABLE
-- ----------------------------
DECLARE
  num number;
BEGIN
  SELECT count(1) into num FROM all_tables WHERE OWNER = 'FHNSDB' AND TABLE_NAME = 'BASE_RULE_CONFIG_TABLE';
  IF num > 0 THEN
    EXECUTE IMMEDIATE 'DROP TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE"';
  END IF;
END;
CREATE TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE" (
  "ID" NUMBER(6,0) NOT NULL,
  "RULE_TYPE" NUMBER(1,0) NOT NULL,
  "AUTO_UPDATE_STATUS" NUMBER(1,0) NOT NULL,
  "CREATE_TIME" NUMBER(13,0) NOT NULL,
  "UPDATE_TIME" NUMBER(13,0) NOT NULL,
  "LAST_EXEC_TIME" NUMBER(13,0)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."BASE_RULE_CONFIG_TABLE"."ID" IS 'ID';
COMMENT ON COLUMN "FHNSDB"."BASE_RULE_CONFIG_TABLE"."RULE_TYPE" IS '规则类型,1-域名知识库,2-应用知识库';
COMMENT ON COLUMN "FHNSDB"."BASE_RULE_CONFIG_TABLE"."AUTO_UPDATE_STATUS" IS '自动更新状态,0-关闭,1-开启';
COMMENT ON COLUMN "FHNSDB"."BASE_RULE_CONFIG_TABLE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."BASE_RULE_CONFIG_TABLE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "FHNSDB"."BASE_RULE_CONFIG_TABLE"."LAST_EXEC_TIME" IS '上一次执行时间';

-- ----------------------------
-- Records of BASE_RULE_CONFIG_TABLE
-- ----------------------------
INSERT INTO "FHNSDB"."BASE_RULE_CONFIG_TABLE" VALUES ('1', '1', '1', '1653279606000', '1653710188052', NULL);
INSERT INTO "FHNSDB"."BASE_RULE_CONFIG_TABLE" VALUES ('2', '2', '1', '1653279606000', '1653877875805', NULL);

-- ----------------------------
-- Checks structure for table BASE_RULE_CONFIG_TABLE
-- ----------------------------
ALTER TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE" ADD CONSTRAINT "SYS_C006287" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE" ADD CONSTRAINT "SYS_C006288" CHECK ("RULE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE" ADD CONSTRAINT "SYS_C006289" CHECK ("AUTO_UPDATE_STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE" ADD CONSTRAINT "SYS_C006290" CHECK ("CREATE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."BASE_RULE_CONFIG_TABLE" ADD CONSTRAINT "SYS_C006291" CHECK ("UPDATE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
