CREATE TABLE "FHNSDB"."FIXED_IP_ARCHIVE"
   (	"ID" NUMBER(38,0) NOT NULL ENABLE,
	"IP_NAME" VARCHAR2(255) DEFAULT '' NOT NULL ENABLE,
	"IP_ADDRESS" VARCHAR2(255) DEFAULT '' NOT NULL ENABLE,
	"ENTITY_TYPE" NUMBER(1,0) DEFAULT 0 NOT NULL ENABLE,
	"DELETE_STATUS" NUMBER(1,0) DEFAULT 0 NOT NULL ENABLE,
	"REMARK" VARCHAR2(1024) DEFAULT '',
	"CREATE_TIME" NUMBER(13,0) DEFAULT 0 NOT NULL ENABLE,
	"MODIFY_TIME" NUMBER(13,0) DEFAULT 0 NOT NULL ENABLE,
	"INDIVIDUAL_NAME" VARCHAR2(255) DEFAULT '' NOT NULL ENABLE,
	"PERSON_SEX" NUMBER(1,0) DEFAULT 2 NOT NULL ENABLE,
	"BIRTH" DATE,
	"CARD_ENTERPRISE_TYPE" NUMBER(1,0) DEFAULT 2 NOT NULL ENABLE,
	"CARD_NUM_CODE" VARCHAR2(255) DEFAULT '',
	"ADDRESS" VARCHAR2(1024) DEFAULT '',
	"WORK_ADDRESS" VARCHAR2(1024) DEFAULT '',
	"JURIDICAL_PERSON" VARCHAR2(255) DEFAULT '',
	"POSITION" VARCHAR2(255) DEFAULT '',
	"PERSON_DEGREE" NUMBER(1,0) DEFAULT 3 NOT NULL ENABLE,
	"PHONE_NUM" VARCHAR2(255) DEFAULT '',
	"PERSON_POLITICAL_STATUS" VARCHAR2(255) DEFAULT '',
	"NATIONALITY_INDUSTRY" VARCHAR2(255) DEFAULT '',
	"ENTERPRISE_SCALE" NUMBER(1,0) DEFAULT 5 NOT NULL ENABLE,
	"ENTERPRISE_EMPLOYEE_NUM" NUMBER(11,0) DEFAULT 0,
	"ENTERPRISE_REGISTER_CAPITAL" BINARY_DOUBLE DEFAULT 0,
	 PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "FENGHUO"  ENABLE
   ) SEGMENT CREATION IMMEDIATE
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "FENGHUO" ;

COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."ID" IS '主键';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."IP_NAME" IS 'ip名称，默认为ip地址';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."IP_ADDRESS" IS 'ip地址,  等同auth_account字段';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."ENTITY_TYPE" IS '实体类型，0：企业，1：个人，默认0';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."DELETE_STATUS" IS '删除状态，0：未删除，1：已删除';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."REMARK" IS '备注';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."MODIFY_TIME" IS '修改时间';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."INDIVIDUAL_NAME" IS '个人/政企名称';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."PERSON_SEX" IS '个人性别，0：女，1：男，2：未知，默认2';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."BIRTH" IS '出生/成立日期';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."CARD_ENTERPRISE_TYPE" IS '证件/政企类型，0：护照/有限责任公司，1：户口本/股份责任公司，2：身份证/其他';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."CARD_NUM_CODE" IS '证件号码/政企代码';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."ADDRESS" IS '住址/注册地址';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."WORK_ADDRESS" IS '工作地址/官网地址';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."JURIDICAL_PERSON" IS '法人';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."POSITION" IS '职位';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."PERSON_DEGREE" IS '学位，0：本科，1：硕士，2：博士，3：其他';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."PHONE_NUM" IS '联系电话/办公电话';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."PERSON_POLITICAL_STATUS" IS '政治地位';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."NATIONALITY_INDUSTRY" IS '国籍/所属行业';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."ENTERPRISE_SCALE" IS '政企规模，0：特大型、1：大型、2中型、3：小型、4：微型、5：其他';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."ENTERPRISE_EMPLOYEE_NUM" IS '员工人数';
   COMMENT ON COLUMN "FHNSDB"."FIXED_IP_ARCHIVE"."ENTERPRISE_REGISTER_CAPITAL" IS '注册资本，单位：万';

CREATE SEQUENCE autoID INCREMENT BY 1 START WITH 1 MAXVALUE 2100000000 CYCLE;

CREATE OR REPLACE TRIGGER autoID BEFORE INSERT ON "FHNSDB"."FIXED_IP_ARCHIVE" FOR EACH ROW
BEGIN
	SELECT autoID.nextval INTO:new.id FROM sys.dual;
END;