-- **************************************************序列****************************************************************
CREATE SEQUENCE  "FHNSDB"."SEQ_ADSL"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_BASE_STATION"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_IP_LIBRARY"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_MOBILE_REGISTER"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_IDCARD"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_MOBILE_SECTION"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_COUNTRY_CODE"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;
CREATE SEQUENCE  "FHNSDB"."SEQ_INVALID_TELEPHONE"  MINVALUE 1 MAXVALUE ************ INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE;

-- **************************************************表结构如下***********************************************************
-- ----------------------------
-- Table structure for TB_ADSL
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_ADSL" (
  "ID" NUMBER(11) NOT NULL ,
  "ACCOUNT_HOLDER_NAME" VARCHAR2(255 BYTE) NOT NULL ,
  "IDENTITY_CARD_NUMBER" VARCHAR2(255 BYTE) NOT NULL ,
  "ASSEMBLY_PHONE" VARCHAR2(255 BYTE) NOT NULL ,
  "INSTALLATION_POSITION" VARCHAR2(255 BYTE) ,
  "ONLINE_ACCOUNT" VARCHAR2(255 BYTE) NOT NULL ,
  "ACCOUNT_HOLDER_PHONE" VARCHAR2(255 BYTE) ,
  "ACCESS_MODE" NUMBER(2) ,
  "ISP_CODE" NUMBER(2) ,
  "CITY_CODE" VARCHAR2(255 BYTE) NOT NULL ,
  "ADDRESS" VARCHAR2(255 BYTE) ,
  "INSTALLATION_TIME" NUMBER(10) ,
  "CONTACT_NAME" VARCHAR2(255 BYTE) ,
  "CONTACT_ID_NUMBER" VARCHAR2(255 BYTE) ,
  "CONTACT_NUMBER" VARCHAR2(255 BYTE) ,
  "LONGITUDE" VARCHAR2(255 BYTE) ,
  "LATITUDE" VARCHAR2(255 BYTE) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ACCOUNT_HOLDER_NAME" IS '账号持有人姓名';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."IDENTITY_CARD_NUMBER" IS '账号持有人身份证号码';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ASSEMBLY_PHONE" IS '设备组装电话';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."INSTALLATION_POSITION" IS '安装位置';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ONLINE_ACCOUNT" IS '上网账号';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ACCOUNT_HOLDER_PHONE" IS '账号持有人电话';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ACCESS_MODE" IS '存取模式,1.专用网络真实IP地址 2.专线 3.ADSL号码输入 4.ISDN 5.普通号码 6.电缆调制解调器编号 7.电线 8.无线上网 99.其他连接方式';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ISP_CODE" IS '运营商,1:电信  99:其他';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."CITY_CODE" IS '城市代码';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."ADDRESS" IS '地址';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."INSTALLATION_TIME" IS '安装时间';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."CONTACT_NAME" IS '联系人姓名';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."CONTACT_ID_NUMBER" IS '联系人身份证号';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."CONTACT_NUMBER" IS '联系人电话';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."LONGITUDE" IS '经度';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."LATITUDE" IS '纬度';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."STATUS" IS '数据状态,0:无效 1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_ADSL"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_BASE_STATION
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_BASE_STATION" (
  "ID" NUMBER(11) NOT NULL ,
  "BASE_STATION_ID" VARCHAR2(255 BYTE) NOT NULL ,
  "ISP_CODE" NUMBER(2) ,
  "CITY_CODE" VARCHAR2(255 BYTE) ,
  "LONGITUDE" VARCHAR2(255 BYTE) ,
  "LATITUDE" VARCHAR2(255 BYTE) ,
  "BASE_STATION_TYPE" NUMBER(2) ,
  "BASE_STATION_NAME" VARCHAR2(255 BYTE) ,
  "BASE_STATION_ADDRESS" VARCHAR2(255 BYTE) ,
  "GEOGRAPHICAL_INDICATION" VARCHAR2(255 BYTE) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."BASE_STATION_ID" IS '基站编号';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."ISP_CODE" IS '运营商,1:电信 99:其他';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."CITY_CODE" IS '城市代码';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."LONGITUDE" IS '经度';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."LATITUDE" IS '纬度';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."BASE_STATION_TYPE" IS '基站类型,1:基本基站 2:内部分布 98:地铁基站 99:其他类别基站';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."BASE_STATION_NAME" IS '基站名称';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."BASE_STATION_ADDRESS" IS '基站地址';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."GEOGRAPHICAL_INDICATION" IS '地理标志';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."STATUS" IS '数据状态,0:无效 1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_BASE_STATION"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_COUNTRY_CODE
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_COUNTRY_CODE" (
  "ID" NUMBER(11) NOT NULL ,
  "COUNTRY_CODE" VARCHAR2(255 BYTE) ,
  "COUNTRY_NAME" VARCHAR2(255 BYTE) ,
  "COUNTRY_EN_NAME" VARCHAR2(255 BYTE) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."COUNTRY_CODE" IS '国家码';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."COUNTRY_NAME" IS '国家名称';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."COUNTRY_EN_NAME" IS '国家英文名称';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."STATUS" IS '数据状态,0:无效  1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_COUNTRY_CODE"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_HOME_PAGE_INFO
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_HOME_PAGE_INFO" (
  "ID" NUMBER(2) NOT NULL ,
  "DESCRIPTION" VARCHAR2(2000 BYTE) NOT NULL
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('1', 'ADSL注册信息');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('2', '基站信息');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('3', '全球IP库');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('4', '手机注册信息');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('5', '身份证信息');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('6', '号码段');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('7', '国家代码');
INSERT INTO "FHNSDB"."TB_HOME_PAGE_INFO" VALUES ('8', '号码黑名单');

-- ----------------------------
-- Table structure for TB_IDCARD
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_IDCARD" (
  "ID" NUMBER(11) NOT NULL ,
  "CITY_CODE" VARCHAR2(255 BYTE) ,
  "NET_SITE_ID" VARCHAR2(255 BYTE) ,
  "NET_SITE_NAME" VARCHAR2(255 BYTE) ,
  "CARD_ID" VARCHAR2(255 BYTE) ,
  "USERNAME" VARCHAR2(255 BYTE) ,
  "SEX" NUMBER(1) ,
  "CERTIFICATE_TYPE" VARCHAR2(255 BYTE) ,
  "CERTIFICATE_CODE" VARCHAR2(255 BYTE) ,
  "CERTIFICATION_UNIT" VARCHAR2(255 BYTE) ,
  "NATIONALITY" VARCHAR2(255 BYTE) ,
  "COMPANY_NAME" VARCHAR2(255 BYTE) ,
  "MOBILE" VARCHAR2(255 BYTE) ,
  "BIRTHDAY" VARCHAR2(10 BYTE) ,
  "ROOM_ID" VARCHAR2(255 BYTE) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."CITY_CODE" IS '城市代码';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."NET_SITE_ID" IS '互联网服务位置代码';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."NET_SITE_NAME" IS '互联网服务标题';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."CARD_ID" IS '网卡号';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."USERNAME" IS '上网人员姓名';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."SEX" IS '性别';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."CERTIFICATE_TYPE" IS '证件类型';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."CERTIFICATE_CODE" IS '证件号码';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."CERTIFICATION_UNIT" IS '发证单位名称';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."NATIONALITY" IS '国籍';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."COMPANY_NAME" IS '单位名称';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."MOBILE" IS '联系电话';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."BIRTHDAY" IS '生日';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."ROOM_ID" IS '房间号';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."STATUS" IS '数据状态,0:无效  1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_IDCARD"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_INVALID_TELEPHONE
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_INVALID_TELEPHONE" (
  "ID" NUMBER(11) NOT NULL ,
  "PHONE_NUMBER" VARCHAR2(255 BYTE) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_INVALID_TELEPHONE"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_INVALID_TELEPHONE"."PHONE_NUMBER" IS '电话号码';
COMMENT ON COLUMN "FHNSDB"."TB_INVALID_TELEPHONE"."STATUS" IS '数据状态,0:无效  1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_INVALID_TELEPHONE"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_INVALID_TELEPHONE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_INVALID_TELEPHONE"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_IP_LIBRARY
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_IP_LIBRARY" (
  "ID" NUMBER(11) NOT NULL ,
  "IP_ID" NUMBER(38) NOT NULL ,
  "START_IP" NUMBER(38) NOT NULL ,
  "END_IP" NUMBER(38) NOT NULL ,
  "COMPANY_NAME" VARCHAR2(255 BYTE) ,
  "ADDRESS" VARCHAR2(255 BYTE) ,
  "CONTACT_NAME" VARCHAR2(255 BYTE) ,
  "CONTACT_NUMBER" VARCHAR2(255 BYTE) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."IP_ID" IS 'IPID';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."START_IP" IS '启动IP';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."END_IP" IS '结束IP';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."COMPANY_NAME" IS '单位名称';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."ADDRESS" IS '地址';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."CONTACT_NAME" IS '联系人姓名';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."CONTACT_NUMBER" IS '联系人电话';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."STATUS" IS '数据状态,0:无效 1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_IP_LIBRARY"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_MOBILE_REGISTER
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_MOBILE_REGISTER" (
  "ID" NUMBER(11) NOT NULL ,
  "IMSI" VARCHAR2(255 BYTE) ,
  "MSISDN" VARCHAR2(255 BYTE) NOT NULL ,
  "NAME" VARCHAR2(255 BYTE) ,
  "SEX" NUMBER(1) ,
  "AGE" NUMBER(3) ,
  "NATIONALITY" VARCHAR2(255 BYTE) ,
  "EDUCATION" VARCHAR2(255 BYTE) ,
  "CERTIFICATE_TYPE" VARCHAR2(255 BYTE) ,
  "CERTIFICATE_CODE" VARCHAR2(255 BYTE) ,
  "COMPANY_NAME" VARCHAR2(255 BYTE) ,
  "ALLEGED_ORG" VARCHAR2(255 BYTE) ,
  "REGISTERED_ADDRESS" VARCHAR2(255 BYTE) ,
  "NOW_ADDRESS" VARCHAR2(255 BYTE) ,
  "CONTACT_NUMBER" VARCHAR2(255 BYTE) ,
  "POST_CODE" VARCHAR2(255 BYTE) ,
  "EMAIL" VARCHAR2(255 BYTE) ,
  "BIRTHDAY" VARCHAR2(10 BYTE) ,
  "OPEN_DATE" NUMBER(10) ,
  "INVALID_DATE" NUMBER(10) ,
  "ROOM_ID" VARCHAR2(255 BYTE) ,
  "RESIDENCE_TYPE" NUMBER(2) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."IMSI" IS 'IMSI号';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."MSISDN" IS '手机号码';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."NAME" IS '姓名';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."SEX" IS '性别';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."AGE" IS '年龄';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."NATIONALITY" IS '国籍';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."EDUCATION" IS '文化程度';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."CERTIFICATE_TYPE" IS '证件类型';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."CERTIFICATE_CODE" IS '证件号';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."COMPANY_NAME" IS '单位名称';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."ALLEGED_ORG" IS '涉嫌组织';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."REGISTERED_ADDRESS" IS '户口所在地';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."NOW_ADDRESS" IS '现住址';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."CONTACT_NUMBER" IS '联系人电话';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."POST_CODE" IS '邮编';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."EMAIL" IS '邮箱';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."BIRTHDAY" IS '生日';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."OPEN_DATE" IS '开通日期';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."INVALID_DATE" IS '无效日期';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."ROOM_ID" IS '房间号';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."RESIDENCE_TYPE" IS '居住类型,1:普通护照 2: 出入境许可证 3:外国人入境许可证 4:外国人永久居留证 5:外国人临时居留证 6:入籍证明';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."STATUS" IS '数据状态,0:无效 1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_REGISTER"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Table structure for TB_MOBILE_SECTION
-- ----------------------------
CREATE TABLE "FHNSDB"."TB_MOBILE_SECTION" (
  "ID" NUMBER(11) NOT NULL ,
  "MSISDN" VARCHAR2(255 BYTE) ,
  "CITY_CODE" VARCHAR2(255 BYTE) ,
  "CITY_NAME" VARCHAR2(255 BYTE) ,
  "NET_TYPE" VARCHAR2(255 BYTE) ,
  "POST_CODE" VARCHAR2(255 BYTE) ,
  "AREA_CODE" VARCHAR2(255 BYTE) ,
  "ISP_CODE" NUMBER(2) ,
  "STATUS" NUMBER(1) ,
  "REMARK" VARCHAR2(2000 BYTE) ,
  "CREATE_TIME" NUMBER(10) ,
  "UPDATE_TIME" NUMBER(10)
)
TABLESPACE "FENGHUO"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS **********
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."ID" IS '主键';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."MSISDN" IS '手机号码';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."CITY_CODE" IS '城市代码';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."CITY_NAME" IS '城市名称';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."NET_TYPE" IS '网络类型';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."POST_CODE" IS '邮编号';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."AREA_CODE" IS '区号';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."ISP_CODE" IS '运营商,1:电信  99:其他';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."STATUS" IS '数据状态,0:无效 1:有效';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."REMARK" IS '备注';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "FHNSDB"."TB_MOBILE_SECTION"."UPDATE_TIME" IS '更新时间';

-- ----------------------------
-- Primary Key structure for table TB_ADSL
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_ADSL" ADD CONSTRAINT "SYS_C004754" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table TB_ADSL
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_ADSL" ADD CONSTRAINT "SYS_C004755" CHECK ("ACCOUNT_HOLDER_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."TB_ADSL" ADD CONSTRAINT "SYS_C004756" CHECK ("IDENTITY_CARD_NUMBER" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."TB_ADSL" ADD CONSTRAINT "SYS_C004757" CHECK ("ASSEMBLY_PHONE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."TB_ADSL" ADD CONSTRAINT "SYS_C004758" CHECK ("ONLINE_ACCOUNT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."TB_ADSL" ADD CONSTRAINT "SYS_C004759" CHECK ("CITY_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table TB_BASE_STATION
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_BASE_STATION" ADD CONSTRAINT "SYS_C004760" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table TB_BASE_STATION
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_BASE_STATION" ADD CONSTRAINT "SYS_C004763" CHECK ("BASE_STATION_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table TB_COUNTRY_CODE
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_COUNTRY_CODE" ADD CONSTRAINT "SYS_C004772" PRIMARY KEY ("ID");

-- ----------------------------
-- Primary Key structure for table TB_HOME_PAGE_INFO
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_HOME_PAGE_INFO" ADD CONSTRAINT "SYS_C004786" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table TB_HOME_PAGE_INFO
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_HOME_PAGE_INFO" ADD CONSTRAINT "SYS_C004785" CHECK ("DESCRIPTION" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table TB_IDCARD
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_IDCARD" ADD CONSTRAINT "SYS_C004770" PRIMARY KEY ("ID");

-- ----------------------------
-- Primary Key structure for table TB_INVALID_TELEPHONE
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_INVALID_TELEPHONE" ADD CONSTRAINT "SYS_C004773" PRIMARY KEY ("ID");

-- ----------------------------
-- Primary Key structure for table TB_IP_LIBRARY
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_IP_LIBRARY" ADD CONSTRAINT "SYS_C004769" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table TB_IP_LIBRARY
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_IP_LIBRARY" ADD CONSTRAINT "SYS_C004767" CHECK ("START_IP" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."TB_IP_LIBRARY" ADD CONSTRAINT "SYS_C004768" CHECK ("END_IP" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FHNSDB"."TB_IP_LIBRARY" ADD CONSTRAINT "SYS_C004774" CHECK ("IP_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table TB_MOBILE_REGISTER
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_MOBILE_REGISTER" ADD CONSTRAINT "SYS_C004762" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table TB_MOBILE_REGISTER
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_MOBILE_REGISTER" ADD CONSTRAINT "SYS_C004766" CHECK ("MSISDN" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table TB_MOBILE_SECTION
-- ----------------------------
ALTER TABLE "FHNSDB"."TB_MOBILE_SECTION" ADD CONSTRAINT "SYS_C004771" PRIMARY KEY ("ID");
