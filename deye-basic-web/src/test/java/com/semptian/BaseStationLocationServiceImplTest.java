package com.semptian;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.regex.Pattern;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = BasicWebApplication.class)
public class BaseStationLocationServiceImplTest {

    //经度正则表达式
    private static final Pattern PATTERN_LNG_REGEX = Pattern.compile("^(-?(?:180(\\.0{1,8})?|1[0-7]\\d(\\.\\d{1,8})?|0?\\d?\\d(\\.\\d{1,8})?))$");

    //纬度正则表达式
    private static final Pattern PATTERN_LAT_REGEX = Pattern.compile("^(-?(?:[0-8]?\\d(\\.\\d{1,8})?|90(\\.0{1,8})?))$");

    private static final Pattern PATTERN_COVERAGE_RADIUS_REGEX = Pattern.compile("^[1-9]\\d*(\\.\\d+)?$");

    /**
     * 基站经纬度和覆盖半径正则表达式
     */
    @Test
    public void testPatternRegex() {
        String[] testLongitudes = { "0", "180", "-180", "179.99999999", "-179.99999999", "180.0", "-180.00000000", "100.12345678", "-0.123456789", "190", "-200", "abc", "100." };
        String[] testLatitudes = { "0", "90", "-90", "89.99999999", "-89.99999999", "90.0", "-90.00000000", "45.12345678", "-0.123456789", "100", "-100", "abc", "45." };

        System.out.println("Testing Longitudes:");
        for (String longitude : testLongitudes) {
            System.out.println("Longitude: " + longitude + " is valid: " + PATTERN_LNG_REGEX.matcher(longitude).matches());
        }

        System.out.println("\nTesting Latitudes:");
        for (String latitude : testLatitudes) {
            System.out.println("Latitude: " + latitude + " is valid: " + PATTERN_LAT_REGEX.matcher(latitude).matches());
        }

        String[] testStrings = {"1", "123", "0.1222", "123.456", "-123.456", "123.", ".123", ""};
        for (String test : testStrings) {
            System.out.println(test + ": " + PATTERN_COVERAGE_RADIUS_REGEX.matcher(test).matches());
        }
    }
}