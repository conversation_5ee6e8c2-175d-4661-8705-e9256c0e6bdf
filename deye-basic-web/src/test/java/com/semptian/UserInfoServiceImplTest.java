package com.semptian;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.regex.Pattern;

/**
 * 上网用户信息测试
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = BasicWebApplication.class)
public class UserInfoServiceImplTest {

    private static final Pattern PATTERN_IPV4_REGEX = Pattern.compile("^((\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])\\.){3}(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\/(0|[1-9]|1[0-9]|2[0-9]|3[0-2]))?$");
    private static final Pattern PATTERN_IPV6_REGEX = Pattern.compile("^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?(\\/(0|[1-9]|[1-9][0-9]|1[0-2][0-8]))?$");

    /**
     * ipv4和ipv6正则表达式测试
     */
    @Test
    public void testPatternRegex() {
        String[] testIPs = {
                "***********",
                "***********/24",
                "***************",
                "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
                "2001:0db8:85a3::8a2e:0370:7334",
                "2001:0db8:85a3::8a2e:0370:7334/64",
                "2001:0db8:85a3::8a2e:0370:7334/32",
                "::1",
                "fe80::",
                "123.456.78.90",
                "2001:db8:85a3::8a2e:370:7334::",
                "fe80:::1"
        };

        for (String ip : testIPs) {
            System.out.println("IP: " + ip + " is valid: " + (PATTERN_IPV4_REGEX.matcher(ip).matches() || PATTERN_IPV6_REGEX.matcher(ip).matches()));
        }
    }

}