package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.semptian.BasicWebApplication;
import com.semptian.base.enums.ReturnCode;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.WhiteListEntity;
import com.semptian.enums.ErrorCodeEnum;
import com.semptian.enums.WhiteListScopeEnum;
import com.semptian.enums.WhiteListStatusEnum;
import com.semptian.enums.WhiteListTypeEnum;
import com.semptian.external.YJApiService;
import com.semptian.i18n.I18nUtils;
import com.semptian.param.WhiteListModel;
import com.semptian.service.WhiteListService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Field;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = BasicWebApplication.class)
@RunWith(SpringRunner.class)
public class WhiteListServiceImplTest {

    @Autowired
    private WhiteListService whiteListService;

    @Autowired
    private YJApiService yjApiService;

    @Value("${ics.login.username}")
    private String username;

    @Value("${ics.login.password}")
    private String password;

    @Test
    public void addOrUpdate_WhenRuleIsEmpty_ShouldReturnError() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("");
        param.setType(WhiteListTypeEnum.IP.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString());

        ReturnModel result = whiteListService.addOrUpdate(param);

        assertEquals(ErrorCodeEnum.PARAM_EXCEPTION.getCode(), result.getCode());
        assertEquals(I18nUtils.getMessage("white.list.rule.is.empty"), result.getMsg());
    }

    @Test
    public void addOrUpdate_WhenTypeIsInvalid_ShouldReturnError() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("***********");
        param.setType(0);
        param.setEffectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString());

        ReturnModel result = whiteListService.addOrUpdate(param);

        assertEquals(ErrorCodeEnum.PARAM_EXCEPTION.getCode(), result.getCode());
        assertEquals(I18nUtils.getMessage("white.list.type.is.error"), result.getMsg());
    }

    @Test
    public void addOrUpdate_WhenEffectiveScopeIsEmpty_ShouldReturnError() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("***********");
        param.setType(WhiteListTypeEnum.IP.getCode());

        ReturnModel result = whiteListService.addOrUpdate(param);

        assertEquals(ErrorCodeEnum.PARAM_EXCEPTION.getCode(), result.getCode());
        assertEquals(I18nUtils.getMessage("white.list.effective.scope.is.empty"), result.getMsg());
    }

    @Test
    public void addOrUpdate_WhenCountryCodeIsEmptyForMobilePhone_ShouldReturnError() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("1234567890");
        param.setType(WhiteListTypeEnum.MOBILE_PHONE.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString());

        ReturnModel result = whiteListService.addOrUpdate(param);

        assertEquals(ErrorCodeEnum.PARAM_EXCEPTION.getCode(), result.getCode());
        assertEquals(I18nUtils.getMessage("white.list.country.code.is.empty"), result.getMsg());
    }

    @Test
    public void addOrUpdate_WhenIpIsInvalid_ShouldReturnError() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("invalid_ip");
        param.setType(WhiteListTypeEnum.IP.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString());
        param.setCreateUser("7923454");

        ReturnModel result = whiteListService.addOrUpdate(param);

        assertEquals(ErrorCodeEnum.PARAM_EXCEPTION.getCode(), result.getCode());
        assertEquals(I18nUtils.getMessage("white.list.ip.is.error"), result.getMsg());
    }

    @Test
    public void addOrUpdate_WhenSuccess_ShouldReturnOk() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("***********");
        param.setType(WhiteListTypeEnum.IP.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString());
        param.setCreateUser("7923454");

        ReturnModel result = whiteListService.addOrUpdate(param);
        assertEquals(ReturnCode.SUCCESS.getCode(), result.getCode());

        // 查询是否插入数据库
        int count = whiteListService.count(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(WhiteListEntity::getRule, param.getRule())
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.IN_CONTROL.getCode())
        );
        assertEquals(1, count);
    }

    @Test
    public void addOrUpdate_WhenDataAlreadyExists_ShouldReturnError() {
        WhiteListModel param = new WhiteListModel();
        param.setRule("***********");
        param.setType(WhiteListTypeEnum.IP.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.ONLINE_DATA.getCode().toString());
        param.setCreateUser("7923454");

        ReturnModel result = whiteListService.addOrUpdate(param);

        assertEquals(ErrorCodeEnum.DATA_ALREADY_EXISTED.getCode(), result.getCode());
        assertEquals(I18nUtils.getMessage("white.list.rule.already.exists"), result.getMsg());
    }

    @Test
    public void addOrUpdate_WhenSuccessAndToBeStart_ShouldIssueToYJ() throws InterruptedException {
        WhiteListModel param = new WhiteListModel();
        String content = RandomStringUtils.randomNumeric(10);
        param.setCountryCode("86");
        param.setRule(content);
        param.setType(WhiteListTypeEnum.MOBILE_PHONE.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.PHONE.getCode().toString());
        param.setCreateUser("7923454");

        ReturnModel result = whiteListService.addOrUpdate(param);
        assertEquals(ReturnCode.SUCCESS.getCode(), result.getCode());

        // 等待5秒查看状态是否改为在控
        Thread.sleep(5000);
        int count = whiteListService.count(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(WhiteListEntity::getRule, param.getRule())
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.IN_CONTROL.getCode())
        );
        assertEquals(1, count);
    }

    @Test
    public void issuedWhiteListToYJ_SessionIdInvalid_Retry() throws Exception {
        WhiteListModel param = new WhiteListModel();
        String content = RandomStringUtils.randomNumeric(10);
        param.setCountryCode("86");
        param.setRule(content);
        param.setType(WhiteListTypeEnum.MOBILE_PHONE.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.PHONE.getCode().toString());
        param.setCreateUser("7923454");

        //先登录，再登出，清除sessionId模拟未登录的场景
        yjApiService.logout(yjApiService.login(username, password));
        ReturnModel result = whiteListService.addOrUpdate(param);
        assertEquals(ReturnCode.SUCCESS.getCode(), result.getCode());

        // 等待5秒查看状态是否改为在控
        Thread.sleep(5000);
        int count = whiteListService.count(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(WhiteListEntity::getRule, param.getRule())
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.IN_CONTROL.getCode())
        );
        assertEquals(1, count);
    }

    @Test
    public void issuedWhiteListToYJ_ApiCallFails_Retry() throws Exception {
        WhiteListModel param = new WhiteListModel();
        String content = RandomStringUtils.randomNumeric(10);
        param.setCountryCode("86");
        param.setRule(content);
        param.setType(WhiteListTypeEnum.MOBILE_PHONE.getCode());
        param.setEffectiveScope(WhiteListScopeEnum.PHONE.getCode().toString());
        param.setCreateUser("7923454");

        //使用反射修改yjApiService中的url
        Field urlField = YJApiService.class.getDeclaredField("icsUrl");
        urlField.setAccessible(true);
        urlField.set(yjApiService, "http://localhost:8080");

        ReturnModel result = whiteListService.addOrUpdate(param);
        assertEquals(ReturnCode.SUCCESS.getCode(), result.getCode());
        int count = whiteListService.count(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(WhiteListEntity::getRule, param.getRule())
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.TO_BE_START.getCode())
        );
        assertEquals(1, count);

        // 等待5秒查看状态是否仍为在控中
        Thread.sleep(5000);
        count = whiteListService.count(new LambdaQueryWrapper<WhiteListEntity>()
                .eq(WhiteListEntity::getRule, param.getRule())
                .eq(WhiteListEntity::getStatus, WhiteListStatusEnum.TO_BE_START.getCode())
        );
        assertEquals(1, count);
    }
}
