<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.semptian</groupId>
    <artifactId>deye-basic-library</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>basic-core</module>
        <module>basic-dao</module>
        <module>basic-service</module>
        <module>deye-basic-web</module>
    </modules>

    <name>deye-basic-library</name>
    <description>D-EYE basic library</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/>
    </parent>

    <!-- 系统版本参数中心 -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <module.version>1.0-SNAPSHOT</module.version>
        <base-core.version>4.0.0-SNAPSHOT</base-core.version>
        <base-dao.version>4.0.0-SNAPSHOT</base-dao.version>
        <base-web.version>4.0.0-SNAPSHOT</base-web.version>
        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <mybatis-plus-boot-starter>3.4.0</mybatis-plus-boot-starter>
        <openfeign.version>2.2.3.RELEASE</openfeign.version>
        <spring.boot.version>2.2.3.RELEASE</spring.boot.version>
        <junit.version>4.12</junit.version>
        <swagger.version>2.7.0</swagger.version>
        <lombok.version>1.16.18</lombok.version>
        <guava.version>18.0</guava.version>
        <poi.version>3.17</poi.version>
        <poi.ooxml.version>3.17</poi.ooxml.version>
        <jxl.version>2.6.12</jxl.version>
        <google.code.gson.version>2.8.5</google.code.gson.version>
        <apollo.version>1.6.0</apollo.version>
        <guice.version>4.1.0</guice.version>

        <ojdbc.version>11.2.0.1.0</ojdbc.version>
        <mysql.version>5.1.45</mysql.version>
        <dynamic-datasource.version>3.4.0</dynamic-datasource.version>
        <druid.version>1.1.3</druid.version>
        <commons-beanutils.version>1.7.0</commons-beanutils.version>
        <openfeign.version>2.2.6.RELEASE</openfeign.version>
        <base-log.version>3.0.13-SNAPSHOT</base-log.version>
        <logback.version>1.2.3</logback.version>
        <operate-log-report.version>3.0.15-SNAPSHOT</operate-log-report.version>
        <base-tianhe.version>4.2.0-SNAPSHOT</base-tianhe.version>
    </properties>

    <!-- 系统所有依赖包的管理中心 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>operate-log-report</artifactId>
                <version>${operate-log-report.version}</version>
            </dependency>

            <!--modules-->
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>basic-core</artifactId>
                <version>${module.version}</version>
            </dependency>
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>base-dao</artifactId>
                <version>${base-dao.version}</version>
            </dependency>
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>base-core</artifactId>
                <version>${base-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>base-web</artifactId>
                <version>${base-web.version}</version>
            </dependency>

            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>base-log</artifactId>
                <version>${base-log.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>basic-dao</artifactId>
                <version>${module.version}</version>
            </dependency>
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>basic-service</artifactId>
                <version>${module.version}</version>
            </dependency>
            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>deye-basic-web</artifactId>
                <version>${module.version}</version>
            </dependency>
            <!--modules-->
            <!--junit依赖-->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <!--junit-->
            <!--swagger2 start-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!--swagger2 end-->
            <!--lombok start-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!--lombok end-->
            <!--excel-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.ooxml.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.jexcelapi</groupId>
                <artifactId>jxl</artifactId>
                <version>${jxl.version}</version>
            </dependency>
            <!--excel-->
            <!--data jar start-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc</artifactId>
                <version>${ojdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--data jar end-->


            <!--guava 集合 util-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!--guava 集合 util-->
            <!--gson Json转换-->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${google.code.gson.version}</version>
            </dependency>
            <!--gson Json转换-->

            <!-- apollo -->
            <dependency>
                <groupId>fakepath</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <dependency>
                <groupId>fakepath</groupId>
                <artifactId>apollo-core</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>
            <!-- apollo -->

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.semptian</groupId>
                <artifactId>base-web</artifactId>
                <version>${base-web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>